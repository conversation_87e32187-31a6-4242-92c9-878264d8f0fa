/* [project]/src/css/common/Select.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

.common_select {
  margin-bottom: 1.25rem;
}

.common_select .select__control {
  min-height: 56px;
  box-shadow: none;
  background-color: rgba(0, 0, 0, 0);
  border: 1px solid #666;
  border-radius: 1rem;
  padding: .625rem 1rem;
  font-size: 1.25rem;
  transition: none;
}

@media (max-width: 1599px) {
  .common_select .select__control {
    min-height: 52px;
    padding: .625rem 1rem;
    font-size: 1rem;
  }
}

.common_select .select__control:hover, .common_select .select__control:focus {
  border-color: #666;
}

.common_select .select__control .select__input-container {
  color: #fff;
}

.common_select .select__control .select__input {
  opacity: 0 !important;
}

.common_select .select__control .select__placeholder {
  color: #fff;
}

.common_select .select__control .select__value-container {
  padding-left: 0;
  padding-right: 0;
}

.common_select .select__control .select__value-container .select__multi-value {
  color: #fff;
  background-color: #00adef;
}

.common_select .select__control .select__value-container .select__multi-value .select__multi-value__label {
  color: #fff;
}

.common_select .select__control .select__value-container--is-multi {
  flex-wrap: unset;
  overflow-x: auto;
  overflow-y: hidden;
}

.common_select .select__control .select__value-container--is-multi .select__multi-value {
  min-width: max-content;
}

.common_select .select__control .select__single-value {
  color: #fff;
  align-items: center;
  gap: 10px;
  display: flex;
}

.common_select .select__control .select__indicator-separator {
  display: none;
}

.common_select .select__control .select__indicator {
  cursor: pointer;
  padding: 0;
}

.common_select .select__control .select__indicator svg {
  fill: #fff;
  width: 24px;
  height: 24px;
  transition: all .3s ease-in-out;
}

.common_select .select__menu {
  width: 100%;
  right: 0;
  left: unset;
  background-color: #000;
  margin-top: 0;
  margin-bottom: 0;
}

.common_select .select__menu .select__menu-notice {
  font-size: 14px;
}

.common_select .select__menu .select__menu-list .select__option {
  cursor: pointer;
  color: #fff;
  align-items: center;
  gap: 10px;
  font-size: 16px;
  display: flex;
}

.common_select .select__menu .select__menu-list .select__option:hover {
  background-color: rgba(0, 0, 0, 0);
  color: #00adef !important;
}

.common_select .select__menu .select__menu-list .select__option.select__option--is-focused {
  color: #fff;
  background-color: rgba(0, 0, 0, 0);
}

.common_select .select__menu .select__menu-list .select__option.select__option--is-selected {
  background-color: rgba(0, 0, 0, 0);
  color: #00adef !important;
}

.common_select .select__control--menu-is-open .select__indicator svg {
  transform: rotate(-180deg);
}

/*# sourceMappingURL=src_css_common_Select_scss_css_e59ae46c._.single.css.map*/