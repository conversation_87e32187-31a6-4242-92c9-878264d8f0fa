"use client";

import React, { useEffect, useState } from "react";
import { Container, Row, Col, Form, Button } from "react-bootstrap";
import { useFormik } from "formik";
import DashboardLayout from "@/Layouts/DashboardLayout";
import AdminHeading from "@/Components/common/Dashboard/AdminHeading";
import toast from "react-hot-toast";
import { deleteRequest, get, post, put } from "@/utils/apiUtils";
import { ArticleSchema } from "@/validations/schema";
import JoditEditorComponent from "@/Components/UI/JoditEditorComponent";
import ListingTable from "@/Components/UI/ListingTable";
import "@/css/dashboard/StrategyManager.scss";
import dynamic from "next/dynamic";
import CustomPagination from "@/Components/UI/CustomPagination";
const Select = dynamic(() => import("react-select"), { ssr: false });

const EducationForm = () => {
  const [meta, setMeta] = useState(null);
  const [newPage, setNewPage] = useState(null);
  const [fetchCategories, setFetchCategories] = useState([]);
  const [listingEducationArticles, setListingEducationArticles] = useState([]);
  const [editingEducation, setEditingEducation] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [scopeSummaries] = useState({
    transaction_summary: 'Transaction-Level ID is a unique identifier assigned to every entry or exit transaction, distinguishing it from all other transactions.',
    trade_summary: 'Trade-Level ID is a shared identifier for all transactions grouped within the same trade, linking entries and exits to a unified trade context.',
    portfolio_summary: 'Portfolio-Level ID is a unique identifier assigned to the entire portfolio, consolidating all trades and transactions under a single portfolio reference.'
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

  useEffect(() => {
    const listingCategories = async () => {
      try {
        const response = await get("/super-admin/articles/create");
        setFetchCategories(Array.isArray(response?.data) ? response.data : []);
      } catch (error) {
        console.log(error);
      }
    };

    listingCategories();
  }, []);


  // Debounce search term to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [searchTerm]);

  const listingAdminEducationArticles = async () => {
    const params = { page: newPage };

    // Add search parameter if search term exists
    if (debouncedSearchTerm) {
      params.search = debouncedSearchTerm;
    }

    const response = await get(
      "super-admin/articles/list/education",
      params
    );
    const articles = response?.data || [];
    setListingEducationArticles(articles); // Server handles filtering/sorting
    setMeta(response?.pagination);
  };

  // Trigger API call when debounced search term changes or page changes
  useEffect(() => {
    listingAdminEducationArticles();
  }, [newPage, debouncedSearchTerm]);

  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
  };

  const handlePrimaryCategoryChange = (event) => {
    const selectedPrimaryCategory = event.target.value;
    formik.setFieldValue("primary_category_id", selectedPrimaryCategory);

    if (selectedPrimaryCategory) {
      const updatedSecondaryCategories = formik.values.secondary_categories.filter(
        (id) => String(id) !== String(selectedPrimaryCategory)
      );
      formik.setFieldValue("secondary_categories", updatedSecondaryCategories);
    }
  };

  const getFilteredSecondaryOptions = () => {
    return (fetchCategories || [])
      .filter((cat) => String(cat.id) !== String(formik.values.primary_category_id))
      .map((cat) => ({
        value: cat.id,
        label: cat.title
      }));
  };

  const getSelectedSecondaryValues = () => {
    return formik.values.secondary_categories
      .map((categoryId) => {
        const category = fetchCategories.find((cat) => String(cat.id) === String(categoryId));
        if (!category) return null;
        return { value: category.id, label: category.title };
      })
      .filter(Boolean);
  };

  const handleSecondaryCategoryChange = (selectedOptions) => {
    const selectedIds = selectedOptions.map((option) => option.value);
    formik.setFieldValue("secondary_categories", selectedIds);
  };

  const handleEdit = (educationArticles) => {
    setEditingEducation(educationArticles);

    formik.setValues({
      primary_category_id: educationArticles?.primary_category?.id || "",
      title: educationArticles?.title || "",
      summary: educationArticles?.summary || "",
      content: educationArticles?.content || "",
      secondary_categories: educationArticles?.secondary_categories?.map(cat => String(cat.id)) || [],
      feature_image: educationArticles?.feature_image || null,
      transaction_scope_summary: educationArticles?.transaction_scope_summary || "",
      trade_scope_summary: educationArticles?.trade_scope_summary || "",
      portfolio_scope_summary: educationArticles?.portfolio_scope_summary || "",
    });

  };

  const formik = useFormik({
    initialValues: {
      // url_path: "",
      secondary_categories: [],
      primary_category_id: "",
      title: "",
      feature_image: "",
      content: "",
      summary: "",
      transaction_scope_summary: "",
      trade_scope_summary: "",
      portfolio_scope_summary: ""
    },
    validationSchema: ArticleSchema,
    validateOnChange: true,
    validateOnBlur: true,
    onSubmit: async (values, { resetForm }) => {
      setIsSubmitting(true); // disable the button
    
      try {
        const formData = new FormData();
    
        if (values.primary_category_id) {
          formData.append("primary_category_id", values.primary_category_id);
        }
    
        values.secondary_categories.forEach((id) => {
          formData.append("secondary_categories[]", id);
        });
    
        formData.append("title", values.title);
        formData.append("content", values.content);
        formData.append("summary", values.summary);
        formData.append("feature_image", values.feature_image);
        formData.append("type", "education");
    
        let response;
        if (editingEducation) {
          formData.append("_method", "PUT");
          response = await post(`/super-admin/articles/${editingEducation?.slug}`, formData);
        } else {
          response = await post("/super-admin/articles/store", formData);
        }
    
        if (response?.message) {
          toast.success(response.message);
        }
    
        resetForm(); // this clears the form fields
        setEditingEducation(null);
        listingAdminEducationArticles();
      } catch (error) {
        console.error("Error submitting form:", error);
        toast.error(error?.response?.data?.message ?? "Something went wrong. Please try again.");
      } finally {
        setIsSubmitting(false); // re-enable the button
      }
    }
    
  });

  const secondaryCategoryOptions = fetchCategories?.map((category) => ({
    value: category.id,
    label: category.title,
    database_field: category.database_field
  }));

  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure you want to delete this item?")) return;

    try {
      const response = await deleteRequest(`/super-admin/articles/${id}`);
      if (response?.success) {
        toast.success("Education deleted successfully");
        setListingEducationArticles((prev) => prev.filter((item) => item.id !== id));
      } else {
        toast.error("Error deleting education");
      }
    } catch (error) {
      toast.error(error?.response?.data?.message || "Error deleting");
    }
  };

  const handleDataFromChild = (childData) => {
    setNewPage(childData);
  };

  return (
    <DashboardLayout>
      <Container>
        <AdminHeading heading="Education" className="pt-4 pb-6" centered />
        <Form onSubmit={formik.handleSubmit}>

          <Row>
            <Col md={6}>
              <Form.Group>
                <Form.Label>Primary Category</Form.Label>
                <Form.Select
                  name="primary_category_id"
                  value={formik.values.primary_category_id}
                  onChange={handlePrimaryCategoryChange}
                >
                  <option value="">Select a category</option>
                  {fetchCategories?.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.title}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group>
                <Form.Label>Secondary Categories</Form.Label>
                <Select
                  isMulti
                  name="secondary_categories"
                  options={getFilteredSecondaryOptions()} // ✅ Uses function to filter options
                  value={getSelectedSecondaryValues()} // ✅ Uses function to format selected values
                  onChange={handleSecondaryCategoryChange} // ✅ Uses function to update values
                  isDisabled={!formik.values.primary_category_id} // ✅ Disable if no primary is selected
                  classNamePrefix="select"
                  styles={{
                    control: (provided) => ({
                      ...provided,
                      backgroundColor: "white",
                      color: "black",
                      borderColor: "#ccc",
                    }),
                    menu: (provided) => ({
                      ...provided,
                      backgroundColor: "white",
                      color: "black",
                    }),
                    option: (provided, state) => ({
                      ...provided,
                      backgroundColor: state.isSelected ? "#f0f0f0" : "white",
                      color: "black",
                      "&:hover": {
                        backgroundColor: "#e6e6e6",
                      },
                    }),
                  }}
                />
              </Form.Group>
            </Col>
            <Col md={6} className="mt-3">
              <Form.Group>
                <Form.Label>Page Title</Form.Label>
                <Form.Control
                  type="text"
                  name="title"
                  value={formik.values.title}
                  onChange={formik.handleChange}
                  isInvalid={formik.touched.title && formik.errors.title}
                />
                <Form.Control.Feedback type="invalid" className="text-white">
                  {formik.errors.title}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={6} className="mt-3">
              <Form.Group>
                <Form.Label>Header Image Filename (936x410px) - Serves CDN /education/featured/</Form.Label>
                <Form.Control
                  type="text"
                  name="feature_image"
                  value={formik.values.feature_image}
                  onChange={formik.handleChange}
                  isInvalid={formik.touched.feature_image && formik.errors.feature_image}
                />
                <Form.Control.Feedback type="invalid" className="text-white">
                  {formik.errors.feature_image}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={12} className="mt-3">
              <Form.Group>
                <Form.Label>Schema: articleBody (500-600 char summary)</Form.Label>
                <Form.Control
                  as="textarea"
                  name="articleBody"
                  maxLength={250}
                  value={formik.values.articleBody}
                  onChange={formik.handleChange}
                  isInvalid={formik.touched.articleBody && formik.errors.articleBody}
                />
                <Form.Control.Feedback type="invalid" className="text-white">
                  {formik.errors.articleBody}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={6} className="mt-3">
              <Form.Group>
                <Form.Label>Question 1</Form.Label>
                <Form.Control
                  type="text"
                  name="question1"
                  value={formik.values.question1}
                  onChange={formik.handleChange}
                  isInvalid={formik.touched.question1 && formik.errors.question1}
                />
                <Form.Control.Feedback type="invalid" className="text-white">
                  {formik.errors.question1}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={6} className="mt-3">
              <Form.Group>
                <Form.Label>Question 1 Answer</Form.Label>
                <Form.Control
                  type="text"
                  name="answer1"
                  value={formik.values.answer1}
                  onChange={formik.handleChange}
                  isInvalid={formik.touched.answer1 && formik.errors.answer1}
                />
                <Form.Control.Feedback type="invalid" className="text-white">
                  {formik.errors.answer1}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={6} className="mt-3">
              <Form.Group>
                <Form.Label>Question 2</Form.Label>
                <Form.Control
                  type="text"
                  name="question2"
                  value={formik.values.question2}
                  onChange={formik.handleChange}
                  isInvalid={formik.touched.question2 && formik.errors.question2}
                />
                <Form.Control.Feedback type="invalid" className="text-white">
                  {formik.errors.question2}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={6} className="mt-3">
              <Form.Group>
                <Form.Label>Question 2 Answer</Form.Label>
                <Form.Control
                  type="text"
                  name="answer2"
                  value={formik.values.answer2}
                  onChange={formik.handleChange}
                  isInvalid={formik.touched.answer2 && formik.errors.answer2}
                />
                <Form.Control.Feedback type="invalid" className="text-white">
                  {formik.errors.answer2}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={6} className="mt-3">
              <Form.Group>
                <Form.Label>Question 3</Form.Label>
                <Form.Control
                  type="text"
                  name="question3"
                  value={formik.values.question3}
                  onChange={formik.handleChange}
                  isInvalid={formik.touched.question3 && formik.errors.question3}
                />
                <Form.Control.Feedback type="invalid" className="text-white">
                  {formik.errors.question3}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={6} className="mt-3">
              <Form.Group>
                <Form.Label>Question 3 Answer</Form.Label>
                <Form.Control
                  type="text"
                  name="answer3"
                  value={formik.values.answer3}
                  onChange={formik.handleChange}
                  isInvalid={formik.touched.answer3 && formik.errors.answer3}
                />
                <Form.Control.Feedback type="invalid" className="text-white">
                  {formik.errors.answer3}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={12} className="mt-3">
              <Form.Group>
                <Form.Label>Page Summary</Form.Label>
                <Form.Control
                  as="textarea"
                  name="summary"
                  maxLength={250}
                  value={formik.values.summary}
                  onChange={formik.handleChange}
                  isInvalid={formik.touched.summary && formik.errors.summary}
                />
                <Form.Control.Feedback type="invalid" className="text-white">
                  {formik.errors.summary}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={12} className="mt-3">
              <Form.Group>
                <Form.Label>Transaction Scope Summary</Form.Label>
                <Form.Control
                  as="textarea"
                  name="transaction_scope_summary"
                  value={formik.values.transaction_scope_summary || scopeSummaries.transaction_summary}
                  onChange={formik.handleChange}
                  placeholder={scopeSummaries.transaction_summary}
                  isInvalid={formik.touched.transaction_scope_summary && formik.errors.transaction_scope_summary}
                />
                <Form.Control.Feedback type="invalid" className="text-white">
                  {formik.errors.transaction_scope_summary}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={12} className="mt-3">
              <Form.Group>
                <Form.Label>Trade Scope Summary</Form.Label>
                <Form.Control
                  as="textarea"
                  name="trade_scope_summary"
                  value={formik.values.trade_scope_summary || scopeSummaries.trade_summary}
                  onChange={formik.handleChange}
                  placeholder={scopeSummaries.trade_summary}
                  isInvalid={formik.touched.trade_scope_summary && formik.errors.trade_scope_summary}
                />
                <Form.Control.Feedback type="invalid" className="text-white">
                  {formik.errors.trade_scope_summary}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={12} className="mt-3">
              <Form.Group>
                <Form.Label>Portfolio Scope Summary</Form.Label>
                <Form.Control
                  as="textarea"
                  name="portfolio_scope_summary"
                  value={formik.values.portfolio_scope_summary || scopeSummaries.portfolio_summary}
                  onChange={formik.handleChange}
                  placeholder={scopeSummaries.portfolio_summary}
                  isInvalid={formik.touched.portfolio_scope_summary && formik.errors.portfolio_scope_summary}
                />
                <Form.Control.Feedback type="invalid" className="text-white">
                  {formik.errors.portfolio_scope_summary}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={12} className="mt-3">
              <Form.Group>
                <Form.Label>Body Text</Form.Label>
                <div className="editor-container">
                  <JoditEditorComponent
                    value={formik.values.content}
                    onChange={(newValue) =>
                      formik.setFieldValue("content", newValue)
                    }
                  />
                </div>
                {formik.touched.content && formik.errors.content && (
                  <div className="text-white">{formik.errors.content}</div>
                )}
              </Form.Group>
            </Col>
          </Row>

          {/* Submit Button */}
          <Button type="submit" className="mt-4" disabled={isSubmitting}>
            {isSubmitting ? "Submitting..." : editingEducation ? "Update" : "Submit"}
          </Button>

        </Form>

        {/* Search Bar */}
        <div className="mt-4 mb-3">
          <Form.Group>
            <Form.Control
              type="text"
              placeholder="Search by name..."
              value={searchTerm}
              onChange={handleSearch}
              className="w-100"
            />
          </Form.Group>
        </div>

        <div className="trade_manager_entrylist">
          <ListingTable
            array={listingEducationArticles}
            onUpdate={handleEdit}
            onDelete={handleDelete}
          />
        </div>

        <div className="d-flex justify-content-end mt-3">
          <CustomPagination
            useLinks={false}
            links={meta}
            onDataSend={handleDataFromChild}
            pageUrl={"super-admin/education/"}
          />
        </div>
      </Container>
    </DashboardLayout>
  );
};

export default EducationForm;
