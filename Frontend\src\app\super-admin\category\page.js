"use client";

import { Container, Form } from "react-bootstrap";
import { PlusIcon } from "@/assets/svgIcons/SvgIcon";
import CommonButton from "@/Components/UI/CommonButton";
import AdminHeading from "@/Components/common/Dashboard/AdminHeading";
import CommonHead from "@/Components/common/Dashboard/CommonHead";
import "@/css/dashboard/StrategyManager.scss";
import "@/css/dashboard/StrategyBuilder.scss";
import DashboardLayout from "@/Layouts/DashboardLayout";
import { useEffect, useState } from "react";
import { deleteRequest, get, post, put } from "@/utils/apiUtils";
import toast from "react-hot-toast";
import ListingTable from "@/Components/UI/ListingTable";
import CustomPagination from "@/Components/UI/CustomPagination";

const CategoryForm = () => {
  const [createCategories, setCreateCategories] = useState({
    title: "",
    content: "",
  });
  const [listingCategories, setListingCategories] = useState([]);
  const [allCategories, setAllCategories] = useState([]);
  const [editCategory, setEditCategory] = useState(null);
  const [meta, setMeta] = useState(null);
  const [newPage, setNewPage] = useState(1);
  const [isEdit, setIsEdit] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchCategory();
  }, [newPage]);

  const fetchCategory = async () => {
    try {
      const response = await get("/super-admin/category/categories-list", {
        page: newPage,
      });
      const categories = response?.data || [];
      setAllCategories(categories);
      setMeta(response?.pagination);
    } catch (error) {
      console.error(error);
    }
  };

  // Filter and sort categories based on search term
  useEffect(() => {
    let categories = [...allCategories];

    // Sort alphabetically by title
    categories.sort((a, b) => (a.title || '').localeCompare(b.title || ''));

    if (!searchTerm) {
      setListingCategories(categories);
    } else {
      const filtered = categories.filter(category =>
        category.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        category.content?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setListingCategories(filtered);
    }
  }, [allCategories, searchTerm]);

  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
  };

  const handleChange = (e) => {
    setCreateCategories({
      ...createCategories,
      [e.target.name]: e.target.value,
    });
  };

  const createCategory = async () => {
    try {
      if (isEdit && editCategory) {
        const response = await put(
          `/super-admin/category/${editCategory.slug}`,
          createCategories
        );

        if (response?.success) {
          toast.success("Category successfully updated!");
          setListingCategories((prev) =>
            prev.map((item) =>
              item.id === editCategory.id
                ? { ...item, ...response.data } // Use API response to update all fields including slug
                : item
            )
          );
          resetForm();
        }
      } else {
        const response = await post("/super-admin/category/store", createCategories);

        if (response?.success) {
          toast.success("Category successfully created!");
          setListingCategories((prev) => [...prev, response.data]);
          resetForm();
        }
      }
    } catch (error) {
      console.error(error);
      toast.error(error?.response?.error || "Something went wrong");
    }
  };

  const resetForm = () => {
    setCreateCategories({ title: "", content: "" });
    setEditCategory(null);
    setIsEdit(false);
  };

  const handleDataFromChild = (childData) => {
    setNewPage(childData);
  };

  const handleEdit = (category) => {
    setEditCategory(category);
    setCreateCategories({ title: category.title, content: category.content });
    setIsEdit(true);
  };

  const handleDelete = async (id, slug) => {
    if (!window.confirm("Are you sure you want to delete this item?")) return;

    try {
      const response = await deleteRequest(`/super-admin/category/${slug}`);
      if (response?.success) {
        toast.success(response?.message);
        setListingCategories((prev) => prev.filter((item) => item.id !== id));
      } else {
        toast.error("Error deleting category");
      }
    } catch (error) {
      console.error("Error in Deleting Category", error);
      toast.error(error?.response?.data?.message || "Error deleting");
    }
  };

  return (
    <DashboardLayout>
      <div className="trade_manager">
        <Container>
          <CommonHead />
          <AdminHeading heading="Category" className="pt-4 pb-6" centered />

          <div className="strategy_builder_inputs">
            <div className="customInput">
              <label htmlFor="category_name" className="form-label">
                Category Name
              </label>
              <input
                type="text"
                name="title"
                value={createCategories.title}
                onChange={handleChange}
                placeholder="e.g., Earnings Breakout"
                className="form-control"
              />
            </div>
            <div className="customInput">
              <label htmlFor="category_description" className="form-label">
                Category Description
              </label>
              <input
                type="text"
                name="content"
                value={createCategories.content}
                onChange={handleChange}
                placeholder="e.g., Trades triggered by a price surge following an earnings report."
                className="form-control"
              />
            </div>
          </div>

          <div className="trade_manager_btns">
            <CommonButton
              onClick={createCategory}
              onlyIcon={<PlusIcon />}
              title={isEdit ? "Update Category" : "Create Category"}
              className="w-100 me-2"
            />
          </div>

          {/* Search Bar */}
          <div className="mt-4 mb-3">
            <Form.Group>
              <Form.Control
                type="text"
                placeholder="Search by name..."
                value={searchTerm}
                onChange={handleSearch}
                className="w-100"
              />
            </Form.Group>
          </div>

          <div className="trade_manager_entrylist">
            <ListingTable array={listingCategories} onUpdate={handleEdit} onDelete={handleDelete} categoryFlag={true} />
          </div>

          <div className="d-flex justify-content-end mt-3">
            <CustomPagination
              useLinks={false}
              links={meta}
              onDataSend={handleDataFromChild}
              pageUrl={"super-admin/category"}
            />
          </div>
        </Container>
      </div>
    </DashboardLayout>
  );
};

export default CategoryForm;
