module.exports = {

"[project]/node_modules/@restart/ui/cjs/Button.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
exports.default = void 0;
exports.isTrivialHref = isTrivialHref;
exports.useButtonProps = useButtonProps;
var React = _interopRequireWildcard(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var _jsxRuntime = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js [app-ssr] (ecmascript)");
const _excluded = [
    "as",
    "disabled"
];
function _getRequireWildcardCache(e) {
    if ("function" != typeof WeakMap) return null;
    var r = new WeakMap(), t = new WeakMap();
    return (_getRequireWildcardCache = function(e) {
        return e ? t : r;
    })(e);
}
function _interopRequireWildcard(e, r) {
    if (!r && e && e.__esModule) return e;
    if (null === e || "object" != typeof e && "function" != typeof e) return {
        default: e
    };
    var t = _getRequireWildcardCache(r);
    if (t && t.has(e)) return t.get(e);
    var n = {
        __proto__: null
    }, a = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var u in e)if ("default" !== u && ({}).hasOwnProperty.call(e, u)) {
        var i = a ? Object.getOwnPropertyDescriptor(e, u) : null;
        i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u];
    }
    return n.default = e, t && t.set(e, n), n;
}
function _objectWithoutPropertiesLoose(r, e) {
    if (null == r) return {};
    var t = {};
    for(var n in r)if (({}).hasOwnProperty.call(r, n)) {
        if (e.indexOf(n) >= 0) continue;
        t[n] = r[n];
    }
    return t;
}
function isTrivialHref(href) {
    return !href || href.trim() === '#';
}
function useButtonProps({ tagName, disabled, href, target, rel, role, onClick, tabIndex = 0, type }) {
    if (!tagName) {
        if (href != null || target != null || rel != null) {
            tagName = 'a';
        } else {
            tagName = 'button';
        }
    }
    const meta = {
        tagName
    };
    if (tagName === 'button') {
        return [
            {
                type: type || 'button',
                disabled
            },
            meta
        ];
    }
    const handleClick = (event)=>{
        if (disabled || tagName === 'a' && isTrivialHref(href)) {
            event.preventDefault();
        }
        if (disabled) {
            event.stopPropagation();
            return;
        }
        onClick == null ? void 0 : onClick(event);
    };
    const handleKeyDown = (event)=>{
        if (event.key === ' ') {
            event.preventDefault();
            handleClick(event);
        }
    };
    if (tagName === 'a') {
        // Ensure there's a href so Enter can trigger anchor button.
        href || (href = '#');
        if (disabled) {
            href = undefined;
        }
    }
    return [
        {
            role: role != null ? role : 'button',
            // explicitly undefined so that it overrides the props disabled in a spread
            // e.g. <Tag {...props} {...hookProps} />
            disabled: undefined,
            tabIndex: disabled ? undefined : tabIndex,
            href,
            target: tagName === 'a' ? target : undefined,
            'aria-disabled': !disabled ? undefined : disabled,
            rel: tagName === 'a' ? rel : undefined,
            onClick: handleClick,
            onKeyDown: handleKeyDown
        },
        meta
    ];
}
const Button = /*#__PURE__*/ React.forwardRef((_ref, ref)=>{
    let { as: asProp, disabled } = _ref, props = _objectWithoutPropertiesLoose(_ref, _excluded);
    const [buttonProps, { tagName: Component }] = useButtonProps(Object.assign({
        tagName: asProp,
        disabled
    }, props));
    return /*#__PURE__*/ (0, _jsxRuntime.jsx)(Component, Object.assign({}, props, buttonProps, {
        ref: ref
    }));
});
Button.displayName = 'Button';
var _default = exports.default = Button;
}}),
"[project]/node_modules/@restart/ui/cjs/Anchor.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
exports.default = void 0;
exports.isTrivialHref = isTrivialHref;
var React = _interopRequireWildcard(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var _hooks = __turbopack_context__.r("[project]/node_modules/@restart/ui/node_modules/@restart/hooks/cjs/index.js [app-ssr] (ecmascript)");
var _Button = __turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/Button.js [app-ssr] (ecmascript)");
var _jsxRuntime = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js [app-ssr] (ecmascript)");
const _excluded = [
    "onKeyDown"
];
/* eslint-disable jsx-a11y/no-static-element-interactions */ /* eslint-disable jsx-a11y/anchor-has-content */ function _getRequireWildcardCache(e) {
    if ("function" != typeof WeakMap) return null;
    var r = new WeakMap(), t = new WeakMap();
    return (_getRequireWildcardCache = function(e) {
        return e ? t : r;
    })(e);
}
function _interopRequireWildcard(e, r) {
    if (!r && e && e.__esModule) return e;
    if (null === e || "object" != typeof e && "function" != typeof e) return {
        default: e
    };
    var t = _getRequireWildcardCache(r);
    if (t && t.has(e)) return t.get(e);
    var n = {
        __proto__: null
    }, a = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var u in e)if ("default" !== u && ({}).hasOwnProperty.call(e, u)) {
        var i = a ? Object.getOwnPropertyDescriptor(e, u) : null;
        i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u];
    }
    return n.default = e, t && t.set(e, n), n;
}
function _objectWithoutPropertiesLoose(r, e) {
    if (null == r) return {};
    var t = {};
    for(var n in r)if (({}).hasOwnProperty.call(r, n)) {
        if (e.indexOf(n) >= 0) continue;
        t[n] = r[n];
    }
    return t;
}
function isTrivialHref(href) {
    return !href || href.trim() === '#';
}
/**
 * An generic `<a>` component that covers a few A11y cases, ensuring that
 * cases where the `href` is missing or trivial like "#" are treated like buttons.
 */ const Anchor = /*#__PURE__*/ React.forwardRef((_ref, ref)=>{
    let { onKeyDown } = _ref, props = _objectWithoutPropertiesLoose(_ref, _excluded);
    const [buttonProps] = (0, _Button.useButtonProps)(Object.assign({
        tagName: 'a'
    }, props));
    const handleKeyDown = (0, _hooks.useEventCallback)((e)=>{
        buttonProps.onKeyDown(e);
        onKeyDown == null ? void 0 : onKeyDown(e);
    });
    if (isTrivialHref(props.href) || props.role === 'button') {
        return /*#__PURE__*/ (0, _jsxRuntime.jsx)("a", Object.assign({
            ref: ref
        }, props, buttonProps, {
            onKeyDown: handleKeyDown
        }));
    }
    return /*#__PURE__*/ (0, _jsxRuntime.jsx)("a", Object.assign({
        ref: ref
    }, props, {
        onKeyDown: onKeyDown
    }));
});
Anchor.displayName = 'Anchor';
var _default = exports.default = Anchor;
}}),
"[project]/node_modules/@restart/ui/cjs/SelectableContext.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
exports.makeEventKey = exports.default = void 0;
var React = _interopRequireWildcard(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
function _getRequireWildcardCache(e) {
    if ("function" != typeof WeakMap) return null;
    var r = new WeakMap(), t = new WeakMap();
    return (_getRequireWildcardCache = function(e) {
        return e ? t : r;
    })(e);
}
function _interopRequireWildcard(e, r) {
    if (!r && e && e.__esModule) return e;
    if (null === e || "object" != typeof e && "function" != typeof e) return {
        default: e
    };
    var t = _getRequireWildcardCache(r);
    if (t && t.has(e)) return t.get(e);
    var n = {
        __proto__: null
    }, a = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var u in e)if ("default" !== u && ({}).hasOwnProperty.call(e, u)) {
        var i = a ? Object.getOwnPropertyDescriptor(e, u) : null;
        i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u];
    }
    return n.default = e, t && t.set(e, n), n;
}
const SelectableContext = /*#__PURE__*/ React.createContext(null);
const makeEventKey = (eventKey, href = null)=>{
    if (eventKey != null) return String(eventKey);
    return href || null;
};
exports.makeEventKey = makeEventKey;
var _default = exports.default = SelectableContext;
}}),
"[project]/node_modules/@restart/ui/cjs/utils.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
exports.getChildRef = getChildRef;
exports.getReactVersion = getReactVersion;
exports.isEscKey = isEscKey;
var React = _interopRequireWildcard(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
function _getRequireWildcardCache(e) {
    if ("function" != typeof WeakMap) return null;
    var r = new WeakMap(), t = new WeakMap();
    return (_getRequireWildcardCache = function(e) {
        return e ? t : r;
    })(e);
}
function _interopRequireWildcard(e, r) {
    if (!r && e && e.__esModule) return e;
    if (null === e || "object" != typeof e && "function" != typeof e) return {
        default: e
    };
    var t = _getRequireWildcardCache(r);
    if (t && t.has(e)) return t.get(e);
    var n = {
        __proto__: null
    }, a = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var u in e)if ("default" !== u && ({}).hasOwnProperty.call(e, u)) {
        var i = a ? Object.getOwnPropertyDescriptor(e, u) : null;
        i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u];
    }
    return n.default = e, t && t.set(e, n), n;
}
function isEscKey(e) {
    return e.code === 'Escape' || e.keyCode === 27;
}
function getReactVersion() {
    const parts = React.version.split('.');
    return {
        major: +parts[0],
        minor: +parts[1],
        patch: +parts[2]
    };
}
function getChildRef(element) {
    if (!element || typeof element === 'function') {
        return null;
    }
    const { major } = getReactVersion();
    const childRef = major >= 19 ? element.props.ref : element.ref;
    return childRef;
}
}}),
"[project]/node_modules/@restart/ui/cjs/DataKey.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
exports.PROPERTY_PREFIX = exports.ATTRIBUTE_PREFIX = void 0;
exports.dataAttr = dataAttr;
exports.dataProp = dataProp;
const ATTRIBUTE_PREFIX = exports.ATTRIBUTE_PREFIX = `data-rr-ui-`;
const PROPERTY_PREFIX = exports.PROPERTY_PREFIX = `rrUi`;
function dataAttr(property) {
    return `${ATTRIBUTE_PREFIX}${property}`;
}
function dataProp(property) {
    return `${PROPERTY_PREFIX}${property}`;
}
}}),
"[project]/node_modules/@restart/ui/cjs/getScrollbarWidth.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
exports.default = getBodyScrollbarWidth;
/**
 * Get the width of the vertical window scrollbar if it's visible
 */ function getBodyScrollbarWidth(ownerDocument = document) {
    const window = ownerDocument.defaultView;
    return Math.abs(window.innerWidth - ownerDocument.documentElement.clientWidth);
}
}}),
"[project]/node_modules/@restart/ui/cjs/ModalManager.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
exports.default = exports.OPEN_DATA_ATTRIBUTE = void 0;
var _css = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/dom-helpers/esm/css.js [app-ssr] (ecmascript)"));
var _DataKey = __turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/DataKey.js [app-ssr] (ecmascript)");
var _getScrollbarWidth = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/getScrollbarWidth.js [app-ssr] (ecmascript)"));
function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}
const OPEN_DATA_ATTRIBUTE = exports.OPEN_DATA_ATTRIBUTE = (0, _DataKey.dataAttr)('modal-open');
/**
 * Manages a stack of Modals as well as ensuring
 * body scrolling is is disabled and padding accounted for
 */ class ModalManager {
    constructor({ ownerDocument, handleContainerOverflow = true, isRTL = false } = {}){
        this.handleContainerOverflow = handleContainerOverflow;
        this.isRTL = isRTL;
        this.modals = [];
        this.ownerDocument = ownerDocument;
    }
    getScrollbarWidth() {
        return (0, _getScrollbarWidth.default)(this.ownerDocument);
    }
    getElement() {
        return (this.ownerDocument || document).body;
    }
    setModalAttributes(_modal) {
    // For overriding
    }
    removeModalAttributes(_modal) {
    // For overriding
    }
    setContainerStyle(containerState) {
        const style = {
            overflow: 'hidden'
        };
        // we are only interested in the actual `style` here
        // because we will override it
        const paddingProp = this.isRTL ? 'paddingLeft' : 'paddingRight';
        const container = this.getElement();
        containerState.style = {
            overflow: container.style.overflow,
            [paddingProp]: container.style[paddingProp]
        };
        if (containerState.scrollBarWidth) {
            // use computed style, here to get the real padding
            // to add our scrollbar width
            style[paddingProp] = `${parseInt((0, _css.default)(container, paddingProp) || '0', 10) + containerState.scrollBarWidth}px`;
        }
        container.setAttribute(OPEN_DATA_ATTRIBUTE, '');
        (0, _css.default)(container, style);
    }
    reset() {
        [
            ...this.modals
        ].forEach((m)=>this.remove(m));
    }
    removeContainerStyle(containerState) {
        const container = this.getElement();
        container.removeAttribute(OPEN_DATA_ATTRIBUTE);
        Object.assign(container.style, containerState.style);
    }
    add(modal) {
        let modalIdx = this.modals.indexOf(modal);
        if (modalIdx !== -1) {
            return modalIdx;
        }
        modalIdx = this.modals.length;
        this.modals.push(modal);
        this.setModalAttributes(modal);
        if (modalIdx !== 0) {
            return modalIdx;
        }
        this.state = {
            scrollBarWidth: this.getScrollbarWidth(),
            style: {}
        };
        if (this.handleContainerOverflow) {
            this.setContainerStyle(this.state);
        }
        return modalIdx;
    }
    remove(modal) {
        const modalIdx = this.modals.indexOf(modal);
        if (modalIdx === -1) {
            return;
        }
        this.modals.splice(modalIdx, 1);
        // if that was the last modal in a container,
        // clean up the container
        if (!this.modals.length && this.handleContainerOverflow) {
            this.removeContainerStyle(this.state);
        }
        this.removeModalAttributes(modal);
    }
    isTopModal(modal) {
        return !!this.modals.length && this.modals[this.modals.length - 1] === modal;
    }
}
var _default = exports.default = ModalManager;
}}),
"[project]/node_modules/@restart/ui/cjs/useWindow.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
exports.WindowProvider = void 0;
exports.default = useWindow;
var _react = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var _canUseDOM = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/dom-helpers/esm/canUseDOM.js [app-ssr] (ecmascript)"));
function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}
const Context = /*#__PURE__*/ (0, _react.createContext)(_canUseDOM.default ? window : undefined);
const WindowProvider = exports.WindowProvider = Context.Provider;
/**
 * The document "window" placed in React context. Helpful for determining
 * SSR context, or when rendering into an iframe.
 *
 * @returns the current window
 */ function useWindow() {
    return (0, _react.useContext)(Context);
}
}}),
"[project]/node_modules/@restart/ui/cjs/useWaitForDOMRef.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
exports.default = useWaitForDOMRef;
exports.resolveContainerRef = void 0;
var _ownerDocument = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/dom-helpers/esm/ownerDocument.js [app-ssr] (ecmascript)"));
var _canUseDOM = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/dom-helpers/esm/canUseDOM.js [app-ssr] (ecmascript)"));
var _react = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var _useWindow = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/useWindow.js [app-ssr] (ecmascript)"));
function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}
const resolveContainerRef = (ref, document)=>{
    if (!_canUseDOM.default) return null;
    if (ref == null) return (document || (0, _ownerDocument.default)()).body;
    if (typeof ref === 'function') ref = ref();
    if (ref && 'current' in ref) ref = ref.current;
    if (ref && ('nodeType' in ref || ref.getBoundingClientRect)) return ref;
    return null;
};
exports.resolveContainerRef = resolveContainerRef;
function useWaitForDOMRef(ref, onResolved) {
    const window = (0, _useWindow.default)();
    const [resolvedRef, setRef] = (0, _react.useState)(()=>resolveContainerRef(ref, window == null ? void 0 : window.document));
    if (!resolvedRef) {
        const earlyRef = resolveContainerRef(ref);
        if (earlyRef) setRef(earlyRef);
    }
    (0, _react.useEffect)(()=>{
        if (onResolved && resolvedRef) {
            onResolved(resolvedRef);
        }
    }, [
        onResolved,
        resolvedRef
    ]);
    (0, _react.useEffect)(()=>{
        const nextRef = resolveContainerRef(ref);
        if (nextRef !== resolvedRef) {
            setRef(nextRef);
        }
    }, [
        ref,
        resolvedRef
    ]);
    return resolvedRef;
}
}}),
"[project]/node_modules/@restart/ui/cjs/NoopTransition.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
exports.default = void 0;
var _useEventCallback = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useEventCallback.js [app-ssr] (ecmascript)"));
var _useMergedRefs = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMergedRefs.js [app-ssr] (ecmascript)"));
var _react = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var _utils = __turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/utils.js [app-ssr] (ecmascript)");
function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}
function NoopTransition({ children, in: inProp, onExited, mountOnEnter, unmountOnExit }) {
    const ref = (0, _react.useRef)(null);
    const hasEnteredRef = (0, _react.useRef)(inProp);
    const handleExited = (0, _useEventCallback.default)(onExited);
    (0, _react.useEffect)(()=>{
        if (inProp) hasEnteredRef.current = true;
        else {
            handleExited(ref.current);
        }
    }, [
        inProp,
        handleExited
    ]);
    const combinedRef = (0, _useMergedRefs.default)(ref, (0, _utils.getChildRef)(children));
    const child = /*#__PURE__*/ (0, _react.cloneElement)(children, {
        ref: combinedRef
    });
    if (inProp) return child;
    if (unmountOnExit) {
        return null;
    }
    if (!hasEnteredRef.current && mountOnEnter) {
        return null;
    }
    return child;
}
var _default = exports.default = NoopTransition;
}}),
"[project]/node_modules/@restart/ui/cjs/useRTGTransitionProps.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
exports.default = useRTGTransitionProps;
var _react = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var _useMergedRefs = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMergedRefs.js [app-ssr] (ecmascript)"));
var _utils = __turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/utils.js [app-ssr] (ecmascript)");
const _excluded = [
    "onEnter",
    "onEntering",
    "onEntered",
    "onExit",
    "onExiting",
    "onExited",
    "addEndListener",
    "children"
];
function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}
function _objectWithoutPropertiesLoose(r, e) {
    if (null == r) return {};
    var t = {};
    for(var n in r)if (({}).hasOwnProperty.call(r, n)) {
        if (e.indexOf(n) >= 0) continue;
        t[n] = r[n];
    }
    return t;
}
/**
 * Normalizes RTG transition callbacks with nodeRef to better support
 * strict mode.
 *
 * @param props Transition props.
 * @returns Normalized transition props.
 */ function useRTGTransitionProps(_ref) {
    let { onEnter, onEntering, onEntered, onExit, onExiting, onExited, addEndListener, children } = _ref, props = _objectWithoutPropertiesLoose(_ref, _excluded);
    const nodeRef = (0, _react.useRef)(null);
    const mergedRef = (0, _useMergedRefs.default)(nodeRef, (0, _utils.getChildRef)(children));
    const normalize = (callback)=>(param)=>{
            if (callback && nodeRef.current) {
                callback(nodeRef.current, param);
            }
        };
    /* eslint-disable react-hooks/exhaustive-deps */ const handleEnter = (0, _react.useCallback)(normalize(onEnter), [
        onEnter
    ]);
    const handleEntering = (0, _react.useCallback)(normalize(onEntering), [
        onEntering
    ]);
    const handleEntered = (0, _react.useCallback)(normalize(onEntered), [
        onEntered
    ]);
    const handleExit = (0, _react.useCallback)(normalize(onExit), [
        onExit
    ]);
    const handleExiting = (0, _react.useCallback)(normalize(onExiting), [
        onExiting
    ]);
    const handleExited = (0, _react.useCallback)(normalize(onExited), [
        onExited
    ]);
    const handleAddEndListener = (0, _react.useCallback)(normalize(addEndListener), [
        addEndListener
    ]);
    /* eslint-enable react-hooks/exhaustive-deps */ return Object.assign({}, props, {
        nodeRef
    }, onEnter && {
        onEnter: handleEnter
    }, onEntering && {
        onEntering: handleEntering
    }, onEntered && {
        onEntered: handleEntered
    }, onExit && {
        onExit: handleExit
    }, onExiting && {
        onExiting: handleExiting
    }, onExited && {
        onExited: handleExited
    }, addEndListener && {
        addEndListener: handleAddEndListener
    }, {
        children: typeof children === 'function' ? (status, innerProps)=>// TODO: Types for RTG missing innerProps, so need to cast.
            children(status, Object.assign({}, innerProps, {
                ref: mergedRef
            })) : /*#__PURE__*/ (0, _react.cloneElement)(children, {
            ref: mergedRef
        })
    });
}
}}),
"[project]/node_modules/@restart/ui/cjs/RTGTransition.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
exports.default = void 0;
var React = _interopRequireWildcard(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var _useRTGTransitionProps = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/useRTGTransitionProps.js [app-ssr] (ecmascript)"));
var _jsxRuntime = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js [app-ssr] (ecmascript)");
const _excluded = [
    "component"
];
function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}
function _getRequireWildcardCache(e) {
    if ("function" != typeof WeakMap) return null;
    var r = new WeakMap(), t = new WeakMap();
    return (_getRequireWildcardCache = function(e) {
        return e ? t : r;
    })(e);
}
function _interopRequireWildcard(e, r) {
    if (!r && e && e.__esModule) return e;
    if (null === e || "object" != typeof e && "function" != typeof e) return {
        default: e
    };
    var t = _getRequireWildcardCache(r);
    if (t && t.has(e)) return t.get(e);
    var n = {
        __proto__: null
    }, a = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var u in e)if ("default" !== u && ({}).hasOwnProperty.call(e, u)) {
        var i = a ? Object.getOwnPropertyDescriptor(e, u) : null;
        i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u];
    }
    return n.default = e, t && t.set(e, n), n;
}
function _objectWithoutPropertiesLoose(r, e) {
    if (null == r) return {};
    var t = {};
    for(var n in r)if (({}).hasOwnProperty.call(r, n)) {
        if (e.indexOf(n) >= 0) continue;
        t[n] = r[n];
    }
    return t;
}
// Normalizes Transition callbacks when nodeRef is used.
const RTGTransition = /*#__PURE__*/ React.forwardRef((_ref, ref)=>{
    let { component: Component } = _ref, props = _objectWithoutPropertiesLoose(_ref, _excluded);
    const transitionProps = (0, _useRTGTransitionProps.default)(props);
    return /*#__PURE__*/ (0, _jsxRuntime.jsx)(Component, Object.assign({
        ref: ref
    }, transitionProps));
});
var _default = exports.default = RTGTransition;
}}),
"[project]/node_modules/@restart/ui/cjs/ImperativeTransition.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
exports.default = ImperativeTransition;
exports.renderTransition = renderTransition;
exports.useTransition = useTransition;
var _useMergedRefs = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMergedRefs.js [app-ssr] (ecmascript)"));
var _useEventCallback = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useEventCallback.js [app-ssr] (ecmascript)"));
var _useIsomorphicEffect = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useIsomorphicEffect.js [app-ssr] (ecmascript)"));
var _react = _interopRequireWildcard(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var _NoopTransition = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/NoopTransition.js [app-ssr] (ecmascript)"));
var _RTGTransition = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/RTGTransition.js [app-ssr] (ecmascript)"));
var _utils = __turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/utils.js [app-ssr] (ecmascript)");
var _jsxRuntime = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js [app-ssr] (ecmascript)");
function _getRequireWildcardCache(e) {
    if ("function" != typeof WeakMap) return null;
    var r = new WeakMap(), t = new WeakMap();
    return (_getRequireWildcardCache = function(e) {
        return e ? t : r;
    })(e);
}
function _interopRequireWildcard(e, r) {
    if (!r && e && e.__esModule) return e;
    if (null === e || "object" != typeof e && "function" != typeof e) return {
        default: e
    };
    var t = _getRequireWildcardCache(r);
    if (t && t.has(e)) return t.get(e);
    var n = {
        __proto__: null
    }, a = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var u in e)if ("default" !== u && ({}).hasOwnProperty.call(e, u)) {
        var i = a ? Object.getOwnPropertyDescriptor(e, u) : null;
        i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u];
    }
    return n.default = e, t && t.set(e, n), n;
}
function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}
function useTransition({ in: inProp, onTransition }) {
    const ref = (0, _react.useRef)(null);
    const isInitialRef = (0, _react.useRef)(true);
    const handleTransition = (0, _useEventCallback.default)(onTransition);
    (0, _useIsomorphicEffect.default)(()=>{
        if (!ref.current) {
            return undefined;
        }
        let stale = false;
        handleTransition({
            in: inProp,
            element: ref.current,
            initial: isInitialRef.current,
            isStale: ()=>stale
        });
        return ()=>{
            stale = true;
        };
    }, [
        inProp,
        handleTransition
    ]);
    (0, _useIsomorphicEffect.default)(()=>{
        isInitialRef.current = false;
        // this is for strict mode
        return ()=>{
            isInitialRef.current = true;
        };
    }, []);
    return ref;
}
/**
 * Adapts an imperative transition function to a subset of the RTG `<Transition>` component API.
 *
 * ImperativeTransition does not support mounting options or `appear` at the moment, meaning
 * that it always acts like: `mountOnEnter={true} unmountOnExit={true} appear={true}`
 */ function ImperativeTransition({ children, in: inProp, onExited, onEntered, transition }) {
    const [exited, setExited] = (0, _react.useState)(!inProp);
    // TODO: I think this needs to be in an effect
    if (inProp && exited) {
        setExited(false);
    }
    const ref = useTransition({
        in: !!inProp,
        onTransition: (options)=>{
            const onFinish = ()=>{
                if (options.isStale()) return;
                if (options.in) {
                    onEntered == null ? void 0 : onEntered(options.element, options.initial);
                } else {
                    setExited(true);
                    onExited == null ? void 0 : onExited(options.element);
                }
            };
            Promise.resolve(transition(options)).then(onFinish, (error)=>{
                if (!options.in) setExited(true);
                throw error;
            });
        }
    });
    const combinedRef = (0, _useMergedRefs.default)(ref, (0, _utils.getChildRef)(children));
    return exited && !inProp ? null : /*#__PURE__*/ (0, _react.cloneElement)(children, {
        ref: combinedRef
    });
}
function renderTransition(component, runTransition, props) {
    if (component) {
        return /*#__PURE__*/ (0, _jsxRuntime.jsx)(_RTGTransition.default, Object.assign({}, props, {
            component: component
        }));
    }
    if (runTransition) {
        return /*#__PURE__*/ (0, _jsxRuntime.jsx)(ImperativeTransition, Object.assign({}, props, {
            transition: runTransition
        }));
    }
    return /*#__PURE__*/ (0, _jsxRuntime.jsx)(_NoopTransition.default, Object.assign({}, props));
}
}}),
"[project]/node_modules/@restart/ui/cjs/Modal.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
exports.default = void 0;
var _activeElement = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/dom-helpers/esm/activeElement.js [app-ssr] (ecmascript)"));
var _contains = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/dom-helpers/esm/contains.js [app-ssr] (ecmascript)"));
var _canUseDOM = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/dom-helpers/esm/canUseDOM.js [app-ssr] (ecmascript)"));
var _listen = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/dom-helpers/esm/listen.js [app-ssr] (ecmascript)"));
var _react = _interopRequireWildcard(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var React = _react;
var _reactDom = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js [app-ssr] (ecmascript)"));
var _useMounted = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMounted.js [app-ssr] (ecmascript)"));
var _useWillUnmount = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useWillUnmount.js [app-ssr] (ecmascript)"));
var _usePrevious = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/node_modules/@restart/hooks/cjs/usePrevious.js [app-ssr] (ecmascript)"));
var _useEventCallback = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useEventCallback.js [app-ssr] (ecmascript)"));
var _ModalManager = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/ModalManager.js [app-ssr] (ecmascript)"));
var _useWaitForDOMRef = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/useWaitForDOMRef.js [app-ssr] (ecmascript)"));
var _useWindow = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/useWindow.js [app-ssr] (ecmascript)"));
var _ImperativeTransition = __turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/ImperativeTransition.js [app-ssr] (ecmascript)");
var _utils = __turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/utils.js [app-ssr] (ecmascript)");
var _jsxRuntime = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js [app-ssr] (ecmascript)");
const _excluded = [
    "show",
    "role",
    "className",
    "style",
    "children",
    "backdrop",
    "keyboard",
    "onBackdropClick",
    "onEscapeKeyDown",
    "transition",
    "runTransition",
    "backdropTransition",
    "runBackdropTransition",
    "autoFocus",
    "enforceFocus",
    "restoreFocus",
    "restoreFocusOptions",
    "renderDialog",
    "renderBackdrop",
    "manager",
    "container",
    "onShow",
    "onHide",
    "onExit",
    "onExited",
    "onExiting",
    "onEnter",
    "onEntering",
    "onEntered"
];
/* eslint-disable @typescript-eslint/no-use-before-define, react/prop-types */ function _getRequireWildcardCache(e) {
    if ("function" != typeof WeakMap) return null;
    var r = new WeakMap(), t = new WeakMap();
    return (_getRequireWildcardCache = function(e) {
        return e ? t : r;
    })(e);
}
function _interopRequireWildcard(e, r) {
    if (!r && e && e.__esModule) return e;
    if (null === e || "object" != typeof e && "function" != typeof e) return {
        default: e
    };
    var t = _getRequireWildcardCache(r);
    if (t && t.has(e)) return t.get(e);
    var n = {
        __proto__: null
    }, a = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var u in e)if ("default" !== u && ({}).hasOwnProperty.call(e, u)) {
        var i = a ? Object.getOwnPropertyDescriptor(e, u) : null;
        i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u];
    }
    return n.default = e, t && t.set(e, n), n;
}
function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}
function _objectWithoutPropertiesLoose(r, e) {
    if (null == r) return {};
    var t = {};
    for(var n in r)if (({}).hasOwnProperty.call(r, n)) {
        if (e.indexOf(n) >= 0) continue;
        t[n] = r[n];
    }
    return t;
}
let manager;
/*
  Modal props are split into a version with and without index signature so that you can fully use them in another projects
  This is due to Typescript not playing well with index signatures e.g. when using Omit
*/ function getManager(window) {
    if (!manager) manager = new _ModalManager.default({
        ownerDocument: window == null ? void 0 : window.document
    });
    return manager;
}
function useModalManager(provided) {
    const window = (0, _useWindow.default)();
    const modalManager = provided || getManager(window);
    const modal = (0, _react.useRef)({
        dialog: null,
        backdrop: null
    });
    return Object.assign(modal.current, {
        add: ()=>modalManager.add(modal.current),
        remove: ()=>modalManager.remove(modal.current),
        isTopModal: ()=>modalManager.isTopModal(modal.current),
        setDialogRef: (0, _react.useCallback)((ref)=>{
            modal.current.dialog = ref;
        }, []),
        setBackdropRef: (0, _react.useCallback)((ref)=>{
            modal.current.backdrop = ref;
        }, [])
    });
}
const Modal = /*#__PURE__*/ (0, _react.forwardRef)((_ref, ref)=>{
    let { show = false, role = 'dialog', className, style, children, backdrop = true, keyboard = true, onBackdropClick, onEscapeKeyDown, transition, runTransition, backdropTransition, runBackdropTransition, autoFocus = true, enforceFocus = true, restoreFocus = true, restoreFocusOptions, renderDialog, renderBackdrop = (props)=>/*#__PURE__*/ (0, _jsxRuntime.jsx)("div", Object.assign({}, props)), manager: providedManager, container: containerRef, onShow, onHide = ()=>{}, onExit, onExited, onExiting, onEnter, onEntering, onEntered } = _ref, rest = _objectWithoutPropertiesLoose(_ref, _excluded);
    const ownerWindow = (0, _useWindow.default)();
    const container = (0, _useWaitForDOMRef.default)(containerRef);
    const modal = useModalManager(providedManager);
    const isMounted = (0, _useMounted.default)();
    const prevShow = (0, _usePrevious.default)(show);
    const [exited, setExited] = (0, _react.useState)(!show);
    const lastFocusRef = (0, _react.useRef)(null);
    (0, _react.useImperativeHandle)(ref, ()=>modal, [
        modal
    ]);
    if (_canUseDOM.default && !prevShow && show) {
        lastFocusRef.current = (0, _activeElement.default)(ownerWindow == null ? void 0 : ownerWindow.document);
    }
    // TODO: I think this needs to be in an effect
    if (show && exited) {
        setExited(false);
    }
    const handleShow = (0, _useEventCallback.default)(()=>{
        modal.add();
        removeKeydownListenerRef.current = (0, _listen.default)(document, 'keydown', handleDocumentKeyDown);
        removeFocusListenerRef.current = (0, _listen.default)(document, 'focus', // the timeout is necessary b/c this will run before the new modal is mounted
        // and so steals focus from it
        ()=>setTimeout(handleEnforceFocus), true);
        if (onShow) {
            onShow();
        }
        // autofocus after onShow to not trigger a focus event for previous
        // modals before this one is shown.
        if (autoFocus) {
            var _modal$dialog$ownerDo, _modal$dialog;
            const currentActiveElement = (0, _activeElement.default)((_modal$dialog$ownerDo = (_modal$dialog = modal.dialog) == null ? void 0 : _modal$dialog.ownerDocument) != null ? _modal$dialog$ownerDo : ownerWindow == null ? void 0 : ownerWindow.document);
            if (modal.dialog && currentActiveElement && !(0, _contains.default)(modal.dialog, currentActiveElement)) {
                lastFocusRef.current = currentActiveElement;
                modal.dialog.focus();
            }
        }
    });
    const handleHide = (0, _useEventCallback.default)(()=>{
        modal.remove();
        removeKeydownListenerRef.current == null ? void 0 : removeKeydownListenerRef.current();
        removeFocusListenerRef.current == null ? void 0 : removeFocusListenerRef.current();
        if (restoreFocus) {
            var _lastFocusRef$current;
            // Support: <=IE11 doesn't support `focus()` on svg elements (RB: #917)
            (_lastFocusRef$current = lastFocusRef.current) == null ? void 0 : _lastFocusRef$current.focus == null ? void 0 : _lastFocusRef$current.focus(restoreFocusOptions);
            lastFocusRef.current = null;
        }
    });
    // TODO: try and combine these effects: https://github.com/react-bootstrap/react-overlays/pull/794#discussion_r409954120
    // Show logic when:
    //  - show is `true` _and_ `container` has resolved
    (0, _react.useEffect)(()=>{
        if (!show || !container) return;
        handleShow();
    }, [
        show,
        container,
        /* should never change: */ handleShow
    ]);
    // Hide cleanup logic when:
    //  - `exited` switches to true
    //  - component unmounts;
    (0, _react.useEffect)(()=>{
        if (!exited) return;
        handleHide();
    }, [
        exited,
        handleHide
    ]);
    (0, _useWillUnmount.default)(()=>{
        handleHide();
    });
    // --------------------------------
    const handleEnforceFocus = (0, _useEventCallback.default)(()=>{
        if (!enforceFocus || !isMounted() || !modal.isTopModal()) {
            return;
        }
        const currentActiveElement = (0, _activeElement.default)(ownerWindow == null ? void 0 : ownerWindow.document);
        if (modal.dialog && currentActiveElement && !(0, _contains.default)(modal.dialog, currentActiveElement)) {
            modal.dialog.focus();
        }
    });
    const handleBackdropClick = (0, _useEventCallback.default)((e)=>{
        if (e.target !== e.currentTarget) {
            return;
        }
        onBackdropClick == null ? void 0 : onBackdropClick(e);
        if (backdrop === true) {
            onHide();
        }
    });
    const handleDocumentKeyDown = (0, _useEventCallback.default)((e)=>{
        if (keyboard && (0, _utils.isEscKey)(e) && modal.isTopModal()) {
            onEscapeKeyDown == null ? void 0 : onEscapeKeyDown(e);
            if (!e.defaultPrevented) {
                onHide();
            }
        }
    });
    const removeFocusListenerRef = (0, _react.useRef)();
    const removeKeydownListenerRef = (0, _react.useRef)();
    const handleHidden = (...args)=>{
        setExited(true);
        onExited == null ? void 0 : onExited(...args);
    };
    if (!container) {
        return null;
    }
    const dialogProps = Object.assign({
        role,
        ref: modal.setDialogRef,
        // apparently only works on the dialog role element
        'aria-modal': role === 'dialog' ? true : undefined
    }, rest, {
        style,
        className,
        tabIndex: -1
    });
    let dialog = renderDialog ? renderDialog(dialogProps) : /*#__PURE__*/ (0, _jsxRuntime.jsx)("div", Object.assign({}, dialogProps, {
        children: /*#__PURE__*/ React.cloneElement(children, {
            role: 'document'
        })
    }));
    dialog = (0, _ImperativeTransition.renderTransition)(transition, runTransition, {
        unmountOnExit: true,
        mountOnEnter: true,
        appear: true,
        in: !!show,
        onExit,
        onExiting,
        onExited: handleHidden,
        onEnter,
        onEntering,
        onEntered,
        children: dialog
    });
    let backdropElement = null;
    if (backdrop) {
        backdropElement = renderBackdrop({
            ref: modal.setBackdropRef,
            onClick: handleBackdropClick
        });
        backdropElement = (0, _ImperativeTransition.renderTransition)(backdropTransition, runBackdropTransition, {
            in: !!show,
            appear: true,
            mountOnEnter: true,
            unmountOnExit: true,
            children: backdropElement
        });
    }
    return /*#__PURE__*/ (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: /*#__PURE__*/ _reactDom.default.createPortal(/*#__PURE__*/ (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
            children: [
                backdropElement,
                dialog
            ]
        }), container)
    });
});
Modal.displayName = 'Modal';
var _default = exports.default = Object.assign(Modal, {
    Manager: _ModalManager.default
});
}}),
"[project]/node_modules/@restart/ui/cjs/DropdownContext.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
exports.default = void 0;
var React = _interopRequireWildcard(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
function _getRequireWildcardCache(e) {
    if ("function" != typeof WeakMap) return null;
    var r = new WeakMap(), t = new WeakMap();
    return (_getRequireWildcardCache = function(e) {
        return e ? t : r;
    })(e);
}
function _interopRequireWildcard(e, r) {
    if (!r && e && e.__esModule) return e;
    if (null === e || "object" != typeof e && "function" != typeof e) return {
        default: e
    };
    var t = _getRequireWildcardCache(r);
    if (t && t.has(e)) return t.get(e);
    var n = {
        __proto__: null
    }, a = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var u in e)if ("default" !== u && ({}).hasOwnProperty.call(e, u)) {
        var i = a ? Object.getOwnPropertyDescriptor(e, u) : null;
        i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u];
    }
    return n.default = e, t && t.set(e, n), n;
}
const DropdownContext = /*#__PURE__*/ React.createContext(null);
var _default = exports.default = DropdownContext;
}}),
"[project]/node_modules/@restart/ui/cjs/popper.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var top = 'top';
var bottom = 'bottom';
var right = 'right';
var left = 'left';
var auto = 'auto';
var basePlacements = [
    top,
    bottom,
    right,
    left
];
var start = 'start';
var end = 'end';
var clippingParents = 'clippingParents';
var viewport = 'viewport';
var popper = 'popper';
var reference = 'reference';
var variationPlacements = /*#__PURE__*/ basePlacements.reduce(function(acc, placement) {
    return acc.concat([
        placement + "-" + start,
        placement + "-" + end
    ]);
}, []);
var placements = /*#__PURE__*/ [].concat(basePlacements, [
    auto
]).reduce(function(acc, placement) {
    return acc.concat([
        placement,
        placement + "-" + start,
        placement + "-" + end
    ]);
}, []); // modifiers that need to read the DOM
var beforeRead = 'beforeRead';
var read = 'read';
var afterRead = 'afterRead'; // pure-logic modifiers
var beforeMain = 'beforeMain';
var main = 'main';
var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)
var beforeWrite = 'beforeWrite';
var write = 'write';
var afterWrite = 'afterWrite';
var modifierPhases = [
    beforeRead,
    read,
    afterRead,
    beforeMain,
    main,
    afterMain,
    beforeWrite,
    write,
    afterWrite
];
function getBasePlacement(placement) {
    return placement.split('-')[0];
}
function getWindow(node) {
    if (node == null) {
        return window;
    }
    if (node.toString() !== '[object Window]') {
        var ownerDocument = node.ownerDocument;
        return ownerDocument ? ownerDocument.defaultView || window : window;
    }
    return node;
}
function isElement(node) {
    var OwnElement = getWindow(node).Element;
    return node instanceof OwnElement || node instanceof Element;
}
function isHTMLElement(node) {
    var OwnElement = getWindow(node).HTMLElement;
    return node instanceof OwnElement || node instanceof HTMLElement;
}
function isShadowRoot(node) {
    // IE 11 has no ShadowRoot
    if (typeof ShadowRoot === 'undefined') {
        return false;
    }
    var OwnElement = getWindow(node).ShadowRoot;
    return node instanceof OwnElement || node instanceof ShadowRoot;
}
var max = Math.max;
var min = Math.min;
var round = Math.round;
function getUAString() {
    var uaData = navigator.userAgentData;
    if (uaData != null && uaData.brands && Array.isArray(uaData.brands)) {
        return uaData.brands.map(function(item) {
            return item.brand + "/" + item.version;
        }).join(' ');
    }
    return navigator.userAgent;
}
function isLayoutViewport() {
    return !/^((?!chrome|android).)*safari/i.test(getUAString());
}
function getBoundingClientRect(element, includeScale, isFixedStrategy) {
    if (includeScale === void 0) {
        includeScale = false;
    }
    if (isFixedStrategy === void 0) {
        isFixedStrategy = false;
    }
    var clientRect = element.getBoundingClientRect();
    var scaleX = 1;
    var scaleY = 1;
    if (includeScale && isHTMLElement(element)) {
        scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;
        scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;
    }
    var _ref = isElement(element) ? getWindow(element) : window, visualViewport = _ref.visualViewport;
    var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;
    var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;
    var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;
    var width = clientRect.width / scaleX;
    var height = clientRect.height / scaleY;
    return {
        width: width,
        height: height,
        top: y,
        right: x + width,
        bottom: y + height,
        left: x,
        x: x,
        y: y
    };
}
// means it doesn't take into account transforms.
function getLayoutRect(element) {
    var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.
    // Fixes https://github.com/popperjs/popper-core/issues/1223
    var width = element.offsetWidth;
    var height = element.offsetHeight;
    if (Math.abs(clientRect.width - width) <= 1) {
        width = clientRect.width;
    }
    if (Math.abs(clientRect.height - height) <= 1) {
        height = clientRect.height;
    }
    return {
        x: element.offsetLeft,
        y: element.offsetTop,
        width: width,
        height: height
    };
}
function contains(parent, child) {
    var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method
    if (parent.contains(child)) {
        return true;
    } else if (rootNode && isShadowRoot(rootNode)) {
        var next = child;
        do {
            if (next && parent.isSameNode(next)) {
                return true;
            } // $FlowFixMe[prop-missing]: need a better way to handle this...
            next = next.parentNode || next.host;
        }while (next)
    } // Give up, the result is false
    return false;
}
function getNodeName(element) {
    return element ? (element.nodeName || '').toLowerCase() : null;
}
function getComputedStyle(element) {
    return getWindow(element).getComputedStyle(element);
}
function isTableElement(element) {
    return [
        'table',
        'td',
        'th'
    ].indexOf(getNodeName(element)) >= 0;
}
function getDocumentElement(element) {
    // $FlowFixMe[incompatible-return]: assume body is always available
    return ((isElement(element) ? element.ownerDocument : element.document) || window.document).documentElement;
}
function getParentNode(element) {
    if (getNodeName(element) === 'html') {
        return element;
    }
    return(// $FlowFixMe[incompatible-return]
    // $FlowFixMe[prop-missing]
    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node
    element.parentNode || (isShadowRoot(element) ? element.host : null) || // ShadowRoot detected
    // $FlowFixMe[incompatible-call]: HTMLElement is a Node
    getDocumentElement(element) // fallback
    );
}
function getTrueOffsetParent(element) {
    if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837
    getComputedStyle(element).position === 'fixed') {
        return null;
    }
    return element.offsetParent;
} // `.offsetParent` reports `null` for fixed elements, while absolute elements
// return the containing block
function getContainingBlock(element) {
    var isFirefox = /firefox/i.test(getUAString());
    var isIE = /Trident/i.test(getUAString());
    if (isIE && isHTMLElement(element)) {
        // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport
        var elementCss = getComputedStyle(element);
        if (elementCss.position === 'fixed') {
            return null;
        }
    }
    var currentNode = getParentNode(element);
    if (isShadowRoot(currentNode)) {
        currentNode = currentNode.host;
    }
    while(isHTMLElement(currentNode) && [
        'html',
        'body'
    ].indexOf(getNodeName(currentNode)) < 0){
        var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that
        // create a containing block.
        // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block
        if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || [
            'transform',
            'perspective'
        ].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {
            return currentNode;
        } else {
            currentNode = currentNode.parentNode;
        }
    }
    return null;
} // Gets the closest ancestor positioned element. Handles some edge cases,
// such as table ancestors and cross browser bugs.
function getOffsetParent(element) {
    var window1 = getWindow(element);
    var offsetParent = getTrueOffsetParent(element);
    while(offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static'){
        offsetParent = getTrueOffsetParent(offsetParent);
    }
    if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {
        return window1;
    }
    return offsetParent || getContainingBlock(element) || window1;
}
function getMainAxisFromPlacement(placement) {
    return [
        'top',
        'bottom'
    ].indexOf(placement) >= 0 ? 'x' : 'y';
}
function within(min$1, value, max$1) {
    return max(min$1, min(value, max$1));
}
function withinMaxClamp(min, value, max) {
    var v = within(min, value, max);
    return v > max ? max : v;
}
function getFreshSideObject() {
    return {
        top: 0,
        right: 0,
        bottom: 0,
        left: 0
    };
}
function mergePaddingObject(paddingObject) {
    return Object.assign({}, getFreshSideObject(), paddingObject);
}
function expandToHashMap(value, keys) {
    return keys.reduce(function(hashMap, key) {
        hashMap[key] = value;
        return hashMap;
    }, {});
}
var toPaddingObject = function toPaddingObject(padding, state) {
    padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {
        placement: state.placement
    })) : padding;
    return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));
};
function arrow(_ref) {
    var _state$modifiersData$;
    var state = _ref.state, name = _ref.name, options = _ref.options;
    var arrowElement = state.elements.arrow;
    var popperOffsets = state.modifiersData.popperOffsets;
    var basePlacement = getBasePlacement(state.placement);
    var axis = getMainAxisFromPlacement(basePlacement);
    var isVertical = [
        left,
        right
    ].indexOf(basePlacement) >= 0;
    var len = isVertical ? 'height' : 'width';
    if (!arrowElement || !popperOffsets) {
        return;
    }
    var paddingObject = toPaddingObject(options.padding, state);
    var arrowRect = getLayoutRect(arrowElement);
    var minProp = axis === 'y' ? top : left;
    var maxProp = axis === 'y' ? bottom : right;
    var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];
    var startDiff = popperOffsets[axis] - state.rects.reference[axis];
    var arrowOffsetParent = getOffsetParent(arrowElement);
    var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;
    var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is
    // outside of the popper bounds
    var min = paddingObject[minProp];
    var max = clientSize - arrowRect[len] - paddingObject[maxProp];
    var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;
    var offset = within(min, center, max); // Prevents breaking syntax highlighting...
    var axisProp = axis;
    state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);
}
function effect$1(_ref2) {
    var state = _ref2.state, options = _ref2.options;
    var _options$element = options.element, arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;
    if (arrowElement == null) {
        return;
    } // CSS selector
    if (typeof arrowElement === 'string') {
        arrowElement = state.elements.popper.querySelector(arrowElement);
        if (!arrowElement) {
            return;
        }
    }
    if (!contains(state.elements.popper, arrowElement)) {
        return;
    }
    state.elements.arrow = arrowElement;
} // eslint-disable-next-line import/no-unused-modules
var arrow$1 = {
    name: 'arrow',
    enabled: true,
    phase: 'main',
    fn: arrow,
    effect: effect$1,
    requires: [
        'popperOffsets'
    ],
    requiresIfExists: [
        'preventOverflow'
    ]
};
function getVariation(placement) {
    return placement.split('-')[1];
}
var unsetSides = {
    top: 'auto',
    right: 'auto',
    bottom: 'auto',
    left: 'auto'
}; // Round the offsets to the nearest suitable subpixel based on the DPR.
// Zooming can change the DPR, but it seems to report a value that will
// cleanly divide the values into the appropriate subpixels.
function roundOffsetsByDPR(_ref, win) {
    var x = _ref.x, y = _ref.y;
    var dpr = win.devicePixelRatio || 1;
    return {
        x: round(x * dpr) / dpr || 0,
        y: round(y * dpr) / dpr || 0
    };
}
function mapToStyles(_ref2) {
    var _Object$assign2;
    var popper = _ref2.popper, popperRect = _ref2.popperRect, placement = _ref2.placement, variation = _ref2.variation, offsets = _ref2.offsets, position = _ref2.position, gpuAcceleration = _ref2.gpuAcceleration, adaptive = _ref2.adaptive, roundOffsets = _ref2.roundOffsets, isFixed = _ref2.isFixed;
    var _offsets$x = offsets.x, x = _offsets$x === void 0 ? 0 : _offsets$x, _offsets$y = offsets.y, y = _offsets$y === void 0 ? 0 : _offsets$y;
    var _ref3 = typeof roundOffsets === 'function' ? roundOffsets({
        x: x,
        y: y
    }) : {
        x: x,
        y: y
    };
    x = _ref3.x;
    y = _ref3.y;
    var hasX = offsets.hasOwnProperty('x');
    var hasY = offsets.hasOwnProperty('y');
    var sideX = left;
    var sideY = top;
    var win = window;
    if (adaptive) {
        var offsetParent = getOffsetParent(popper);
        var heightProp = 'clientHeight';
        var widthProp = 'clientWidth';
        if (offsetParent === getWindow(popper)) {
            offsetParent = getDocumentElement(popper);
            if (getComputedStyle(offsetParent).position !== 'static' && position === 'absolute') {
                heightProp = 'scrollHeight';
                widthProp = 'scrollWidth';
            }
        } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it
        offsetParent = offsetParent;
        if (placement === top || (placement === left || placement === right) && variation === end) {
            sideY = bottom;
            var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : offsetParent[heightProp];
            y -= offsetY - popperRect.height;
            y *= gpuAcceleration ? 1 : -1;
        }
        if (placement === left || (placement === top || placement === bottom) && variation === end) {
            sideX = right;
            var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : offsetParent[widthProp];
            x -= offsetX - popperRect.width;
            x *= gpuAcceleration ? 1 : -1;
        }
    }
    var commonStyles = Object.assign({
        position: position
    }, adaptive && unsetSides);
    var _ref4 = roundOffsets === true ? roundOffsetsByDPR({
        x: x,
        y: y
    }, getWindow(popper)) : {
        x: x,
        y: y
    };
    x = _ref4.x;
    y = _ref4.y;
    if (gpuAcceleration) {
        var _Object$assign;
        return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? "translate(" + x + "px, " + y + "px)" : "translate3d(" + x + "px, " + y + "px, 0)", _Object$assign));
    }
    return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + "px" : '', _Object$assign2[sideX] = hasX ? x + "px" : '', _Object$assign2.transform = '', _Object$assign2));
}
function computeStyles(_ref5) {
    var state = _ref5.state, options = _ref5.options;
    var _options$gpuAccelerat = options.gpuAcceleration, gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat, _options$adaptive = options.adaptive, adaptive = _options$adaptive === void 0 ? true : _options$adaptive, _options$roundOffsets = options.roundOffsets, roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;
    var commonStyles = {
        placement: getBasePlacement(state.placement),
        variation: getVariation(state.placement),
        popper: state.elements.popper,
        popperRect: state.rects.popper,
        gpuAcceleration: gpuAcceleration,
        isFixed: state.options.strategy === 'fixed'
    };
    if (state.modifiersData.popperOffsets != null) {
        state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {
            offsets: state.modifiersData.popperOffsets,
            position: state.options.strategy,
            adaptive: adaptive,
            roundOffsets: roundOffsets
        })));
    }
    if (state.modifiersData.arrow != null) {
        state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {
            offsets: state.modifiersData.arrow,
            position: 'absolute',
            adaptive: false,
            roundOffsets: roundOffsets
        })));
    }
    state.attributes.popper = Object.assign({}, state.attributes.popper, {
        'data-popper-placement': state.placement
    });
} // eslint-disable-next-line import/no-unused-modules
var computeStyles$1 = {
    name: 'computeStyles',
    enabled: true,
    phase: 'beforeWrite',
    fn: computeStyles,
    data: {}
};
var passive = {
    passive: true
};
function effect(_ref) {
    var state = _ref.state, instance = _ref.instance, options = _ref.options;
    var _options$scroll = options.scroll, scroll = _options$scroll === void 0 ? true : _options$scroll, _options$resize = options.resize, resize = _options$resize === void 0 ? true : _options$resize;
    var window1 = getWindow(state.elements.popper);
    var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);
    if (scroll) {
        scrollParents.forEach(function(scrollParent) {
            scrollParent.addEventListener('scroll', instance.update, passive);
        });
    }
    if (resize) {
        window1.addEventListener('resize', instance.update, passive);
    }
    return function() {
        if (scroll) {
            scrollParents.forEach(function(scrollParent) {
                scrollParent.removeEventListener('scroll', instance.update, passive);
            });
        }
        if (resize) {
            window1.removeEventListener('resize', instance.update, passive);
        }
    };
} // eslint-disable-next-line import/no-unused-modules
var eventListeners = {
    name: 'eventListeners',
    enabled: true,
    phase: 'write',
    fn: function fn() {},
    effect: effect,
    data: {}
};
var hash$1 = {
    left: 'right',
    right: 'left',
    bottom: 'top',
    top: 'bottom'
};
function getOppositePlacement(placement) {
    return placement.replace(/left|right|bottom|top/g, function(matched) {
        return hash$1[matched];
    });
}
var hash = {
    start: 'end',
    end: 'start'
};
function getOppositeVariationPlacement(placement) {
    return placement.replace(/start|end/g, function(matched) {
        return hash[matched];
    });
}
function getWindowScroll(node) {
    var win = getWindow(node);
    var scrollLeft = win.pageXOffset;
    var scrollTop = win.pageYOffset;
    return {
        scrollLeft: scrollLeft,
        scrollTop: scrollTop
    };
}
function getWindowScrollBarX(element) {
    // If <html> has a CSS width greater than the viewport, then this will be
    // incorrect for RTL.
    // Popper 1 is broken in this case and never had a bug report so let's assume
    // it's not an issue. I don't think anyone ever specifies width on <html>
    // anyway.
    // Browsers where the left scrollbar doesn't cause an issue report `0` for
    // this (e.g. Edge 2019, IE11, Safari)
    return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;
}
function getViewportRect(element, strategy) {
    var win = getWindow(element);
    var html = getDocumentElement(element);
    var visualViewport = win.visualViewport;
    var width = html.clientWidth;
    var height = html.clientHeight;
    var x = 0;
    var y = 0;
    if (visualViewport) {
        width = visualViewport.width;
        height = visualViewport.height;
        var layoutViewport = isLayoutViewport();
        if (layoutViewport || !layoutViewport && strategy === 'fixed') {
            x = visualViewport.offsetLeft;
            y = visualViewport.offsetTop;
        }
    }
    return {
        width: width,
        height: height,
        x: x + getWindowScrollBarX(element),
        y: y
    };
}
// of the `<html>` and `<body>` rect bounds if horizontally scrollable
function getDocumentRect(element) {
    var _element$ownerDocumen;
    var html = getDocumentElement(element);
    var winScroll = getWindowScroll(element);
    var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;
    var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);
    var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);
    var x = -winScroll.scrollLeft + getWindowScrollBarX(element);
    var y = -winScroll.scrollTop;
    if (getComputedStyle(body || html).direction === 'rtl') {
        x += max(html.clientWidth, body ? body.clientWidth : 0) - width;
    }
    return {
        width: width,
        height: height,
        x: x,
        y: y
    };
}
function isScrollParent(element) {
    // Firefox wants us to check `-x` and `-y` variations as well
    var _getComputedStyle = getComputedStyle(element), overflow = _getComputedStyle.overflow, overflowX = _getComputedStyle.overflowX, overflowY = _getComputedStyle.overflowY;
    return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);
}
function getScrollParent(node) {
    if ([
        'html',
        'body',
        '#document'
    ].indexOf(getNodeName(node)) >= 0) {
        // $FlowFixMe[incompatible-return]: assume body is always available
        return node.ownerDocument.body;
    }
    if (isHTMLElement(node) && isScrollParent(node)) {
        return node;
    }
    return getScrollParent(getParentNode(node));
}
/*
given a DOM element, return the list of all scroll parents, up the list of ancesors
until we get to the top window object. This list is what we attach scroll listeners
to, because if any of these parent elements scroll, we'll need to re-calculate the
reference element's position.
*/ function listScrollParents(element, list) {
    var _element$ownerDocumen;
    if (list === void 0) {
        list = [];
    }
    var scrollParent = getScrollParent(element);
    var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);
    var win = getWindow(scrollParent);
    var target = isBody ? [
        win
    ].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;
    var updatedList = list.concat(target);
    return isBody ? updatedList : updatedList.concat(listScrollParents(getParentNode(target)));
}
function rectToClientRect(rect) {
    return Object.assign({}, rect, {
        left: rect.x,
        top: rect.y,
        right: rect.x + rect.width,
        bottom: rect.y + rect.height
    });
}
function getInnerBoundingClientRect(element, strategy) {
    var rect = getBoundingClientRect(element, false, strategy === 'fixed');
    rect.top = rect.top + element.clientTop;
    rect.left = rect.left + element.clientLeft;
    rect.bottom = rect.top + element.clientHeight;
    rect.right = rect.left + element.clientWidth;
    rect.width = element.clientWidth;
    rect.height = element.clientHeight;
    rect.x = rect.left;
    rect.y = rect.top;
    return rect;
}
function getClientRectFromMixedType(element, clippingParent, strategy) {
    return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));
} // A "clipping parent" is an overflowable container with the characteristic of
// clipping (or hiding) overflowing elements with a position different from
// `initial`
function getClippingParents(element) {
    var clippingParents = listScrollParents(getParentNode(element));
    var canEscapeClipping = [
        'absolute',
        'fixed'
    ].indexOf(getComputedStyle(element).position) >= 0;
    var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;
    if (!isElement(clipperElement)) {
        return [];
    } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414
    return clippingParents.filter(function(clippingParent) {
        return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';
    });
} // Gets the maximum area that the element is visible in due to any number of
// clipping parents
function getClippingRect(element, boundary, rootBoundary, strategy) {
    var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);
    var clippingParents = [].concat(mainClippingParents, [
        rootBoundary
    ]);
    var firstClippingParent = clippingParents[0];
    var clippingRect = clippingParents.reduce(function(accRect, clippingParent) {
        var rect = getClientRectFromMixedType(element, clippingParent, strategy);
        accRect.top = max(rect.top, accRect.top);
        accRect.right = min(rect.right, accRect.right);
        accRect.bottom = min(rect.bottom, accRect.bottom);
        accRect.left = max(rect.left, accRect.left);
        return accRect;
    }, getClientRectFromMixedType(element, firstClippingParent, strategy));
    clippingRect.width = clippingRect.right - clippingRect.left;
    clippingRect.height = clippingRect.bottom - clippingRect.top;
    clippingRect.x = clippingRect.left;
    clippingRect.y = clippingRect.top;
    return clippingRect;
}
function computeOffsets(_ref) {
    var reference = _ref.reference, element = _ref.element, placement = _ref.placement;
    var basePlacement = placement ? getBasePlacement(placement) : null;
    var variation = placement ? getVariation(placement) : null;
    var commonX = reference.x + reference.width / 2 - element.width / 2;
    var commonY = reference.y + reference.height / 2 - element.height / 2;
    var offsets;
    switch(basePlacement){
        case top:
            offsets = {
                x: commonX,
                y: reference.y - element.height
            };
            break;
        case bottom:
            offsets = {
                x: commonX,
                y: reference.y + reference.height
            };
            break;
        case right:
            offsets = {
                x: reference.x + reference.width,
                y: commonY
            };
            break;
        case left:
            offsets = {
                x: reference.x - element.width,
                y: commonY
            };
            break;
        default:
            offsets = {
                x: reference.x,
                y: reference.y
            };
    }
    var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;
    if (mainAxis != null) {
        var len = mainAxis === 'y' ? 'height' : 'width';
        switch(variation){
            case start:
                offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);
                break;
            case end:
                offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);
                break;
        }
    }
    return offsets;
}
function detectOverflow(state, options) {
    if (options === void 0) {
        options = {};
    }
    var _options = options, _options$placement = _options.placement, placement = _options$placement === void 0 ? state.placement : _options$placement, _options$strategy = _options.strategy, strategy = _options$strategy === void 0 ? state.strategy : _options$strategy, _options$boundary = _options.boundary, boundary = _options$boundary === void 0 ? clippingParents : _options$boundary, _options$rootBoundary = _options.rootBoundary, rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary, _options$elementConte = _options.elementContext, elementContext = _options$elementConte === void 0 ? popper : _options$elementConte, _options$altBoundary = _options.altBoundary, altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary, _options$padding = _options.padding, padding = _options$padding === void 0 ? 0 : _options$padding;
    var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));
    var altContext = elementContext === popper ? reference : popper;
    var popperRect = state.rects.popper;
    var element = state.elements[altBoundary ? altContext : elementContext];
    var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary, strategy);
    var referenceClientRect = getBoundingClientRect(state.elements.reference);
    var popperOffsets = computeOffsets({
        reference: referenceClientRect,
        element: popperRect,
        strategy: 'absolute',
        placement: placement
    });
    var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));
    var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect
    // 0 or negative = within the clipping rect
    var overflowOffsets = {
        top: clippingClientRect.top - elementClientRect.top + paddingObject.top,
        bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,
        left: clippingClientRect.left - elementClientRect.left + paddingObject.left,
        right: elementClientRect.right - clippingClientRect.right + paddingObject.right
    };
    var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element
    if (elementContext === popper && offsetData) {
        var offset = offsetData[placement];
        Object.keys(overflowOffsets).forEach(function(key) {
            var multiply = [
                right,
                bottom
            ].indexOf(key) >= 0 ? 1 : -1;
            var axis = [
                top,
                bottom
            ].indexOf(key) >= 0 ? 'y' : 'x';
            overflowOffsets[key] += offset[axis] * multiply;
        });
    }
    return overflowOffsets;
}
function computeAutoPlacement(state, options) {
    if (options === void 0) {
        options = {};
    }
    var _options = options, placement = _options.placement, boundary = _options.boundary, rootBoundary = _options.rootBoundary, padding = _options.padding, flipVariations = _options.flipVariations, _options$allowedAutoP = _options.allowedAutoPlacements, allowedAutoPlacements = _options$allowedAutoP === void 0 ? placements : _options$allowedAutoP;
    var variation = getVariation(placement);
    var placements$1 = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function(placement) {
        return getVariation(placement) === variation;
    }) : basePlacements;
    var allowedPlacements = placements$1.filter(function(placement) {
        return allowedAutoPlacements.indexOf(placement) >= 0;
    });
    if (allowedPlacements.length === 0) {
        allowedPlacements = placements$1;
    } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...
    var overflows = allowedPlacements.reduce(function(acc, placement) {
        acc[placement] = detectOverflow(state, {
            placement: placement,
            boundary: boundary,
            rootBoundary: rootBoundary,
            padding: padding
        })[getBasePlacement(placement)];
        return acc;
    }, {});
    return Object.keys(overflows).sort(function(a, b) {
        return overflows[a] - overflows[b];
    });
}
function getExpandedFallbackPlacements(placement) {
    if (getBasePlacement(placement) === auto) {
        return [];
    }
    var oppositePlacement = getOppositePlacement(placement);
    return [
        getOppositeVariationPlacement(placement),
        oppositePlacement,
        getOppositeVariationPlacement(oppositePlacement)
    ];
}
function flip(_ref) {
    var state = _ref.state, options = _ref.options, name = _ref.name;
    if (state.modifiersData[name]._skip) {
        return;
    }
    var _options$mainAxis = options.mainAxis, checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis, _options$altAxis = options.altAxis, checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis, specifiedFallbackPlacements = options.fallbackPlacements, padding = options.padding, boundary = options.boundary, rootBoundary = options.rootBoundary, altBoundary = options.altBoundary, _options$flipVariatio = options.flipVariations, flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio, allowedAutoPlacements = options.allowedAutoPlacements;
    var preferredPlacement = state.options.placement;
    var basePlacement = getBasePlacement(preferredPlacement);
    var isBasePlacement = basePlacement === preferredPlacement;
    var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [
        getOppositePlacement(preferredPlacement)
    ] : getExpandedFallbackPlacements(preferredPlacement));
    var placements = [
        preferredPlacement
    ].concat(fallbackPlacements).reduce(function(acc, placement) {
        return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {
            placement: placement,
            boundary: boundary,
            rootBoundary: rootBoundary,
            padding: padding,
            flipVariations: flipVariations,
            allowedAutoPlacements: allowedAutoPlacements
        }) : placement);
    }, []);
    var referenceRect = state.rects.reference;
    var popperRect = state.rects.popper;
    var checksMap = new Map();
    var makeFallbackChecks = true;
    var firstFittingPlacement = placements[0];
    for(var i = 0; i < placements.length; i++){
        var placement = placements[i];
        var _basePlacement = getBasePlacement(placement);
        var isStartVariation = getVariation(placement) === start;
        var isVertical = [
            top,
            bottom
        ].indexOf(_basePlacement) >= 0;
        var len = isVertical ? 'width' : 'height';
        var overflow = detectOverflow(state, {
            placement: placement,
            boundary: boundary,
            rootBoundary: rootBoundary,
            altBoundary: altBoundary,
            padding: padding
        });
        var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;
        if (referenceRect[len] > popperRect[len]) {
            mainVariationSide = getOppositePlacement(mainVariationSide);
        }
        var altVariationSide = getOppositePlacement(mainVariationSide);
        var checks = [];
        if (checkMainAxis) {
            checks.push(overflow[_basePlacement] <= 0);
        }
        if (checkAltAxis) {
            checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);
        }
        if (checks.every(function(check) {
            return check;
        })) {
            firstFittingPlacement = placement;
            makeFallbackChecks = false;
            break;
        }
        checksMap.set(placement, checks);
    }
    if (makeFallbackChecks) {
        // `2` may be desired in some cases – research later
        var numberOfChecks = flipVariations ? 3 : 1;
        var _loop = function _loop(_i) {
            var fittingPlacement = placements.find(function(placement) {
                var checks = checksMap.get(placement);
                if (checks) {
                    return checks.slice(0, _i).every(function(check) {
                        return check;
                    });
                }
            });
            if (fittingPlacement) {
                firstFittingPlacement = fittingPlacement;
                return "break";
            }
        };
        for(var _i = numberOfChecks; _i > 0; _i--){
            var _ret = _loop(_i);
            if (_ret === "break") break;
        }
    }
    if (state.placement !== firstFittingPlacement) {
        state.modifiersData[name]._skip = true;
        state.placement = firstFittingPlacement;
        state.reset = true;
    }
} // eslint-disable-next-line import/no-unused-modules
var flip$1 = {
    name: 'flip',
    enabled: true,
    phase: 'main',
    fn: flip,
    requiresIfExists: [
        'offset'
    ],
    data: {
        _skip: false
    }
};
function getSideOffsets(overflow, rect, preventedOffsets) {
    if (preventedOffsets === void 0) {
        preventedOffsets = {
            x: 0,
            y: 0
        };
    }
    return {
        top: overflow.top - rect.height - preventedOffsets.y,
        right: overflow.right - rect.width + preventedOffsets.x,
        bottom: overflow.bottom - rect.height + preventedOffsets.y,
        left: overflow.left - rect.width - preventedOffsets.x
    };
}
function isAnySideFullyClipped(overflow) {
    return [
        top,
        right,
        bottom,
        left
    ].some(function(side) {
        return overflow[side] >= 0;
    });
}
function hide(_ref) {
    var state = _ref.state, name = _ref.name;
    var referenceRect = state.rects.reference;
    var popperRect = state.rects.popper;
    var preventedOffsets = state.modifiersData.preventOverflow;
    var referenceOverflow = detectOverflow(state, {
        elementContext: 'reference'
    });
    var popperAltOverflow = detectOverflow(state, {
        altBoundary: true
    });
    var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);
    var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);
    var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);
    var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);
    state.modifiersData[name] = {
        referenceClippingOffsets: referenceClippingOffsets,
        popperEscapeOffsets: popperEscapeOffsets,
        isReferenceHidden: isReferenceHidden,
        hasPopperEscaped: hasPopperEscaped
    };
    state.attributes.popper = Object.assign({}, state.attributes.popper, {
        'data-popper-reference-hidden': isReferenceHidden,
        'data-popper-escaped': hasPopperEscaped
    });
} // eslint-disable-next-line import/no-unused-modules
var hide$1 = {
    name: 'hide',
    enabled: true,
    phase: 'main',
    requiresIfExists: [
        'preventOverflow'
    ],
    fn: hide
};
function distanceAndSkiddingToXY(placement, rects, offset) {
    var basePlacement = getBasePlacement(placement);
    var invertDistance = [
        left,
        top
    ].indexOf(basePlacement) >= 0 ? -1 : 1;
    var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {
        placement: placement
    })) : offset, skidding = _ref[0], distance = _ref[1];
    skidding = skidding || 0;
    distance = (distance || 0) * invertDistance;
    return [
        left,
        right
    ].indexOf(basePlacement) >= 0 ? {
        x: distance,
        y: skidding
    } : {
        x: skidding,
        y: distance
    };
}
function offset(_ref2) {
    var state = _ref2.state, options = _ref2.options, name = _ref2.name;
    var _options$offset = options.offset, offset = _options$offset === void 0 ? [
        0,
        0
    ] : _options$offset;
    var data = placements.reduce(function(acc, placement) {
        acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);
        return acc;
    }, {});
    var _data$state$placement = data[state.placement], x = _data$state$placement.x, y = _data$state$placement.y;
    if (state.modifiersData.popperOffsets != null) {
        state.modifiersData.popperOffsets.x += x;
        state.modifiersData.popperOffsets.y += y;
    }
    state.modifiersData[name] = data;
} // eslint-disable-next-line import/no-unused-modules
var offset$1 = {
    name: 'offset',
    enabled: true,
    phase: 'main',
    requires: [
        'popperOffsets'
    ],
    fn: offset
};
function popperOffsets(_ref) {
    var state = _ref.state, name = _ref.name;
    // Offsets are the actual position the popper needs to have to be
    // properly positioned near its reference element
    // This is the most basic placement, and will be adjusted by
    // the modifiers in the next step
    state.modifiersData[name] = computeOffsets({
        reference: state.rects.reference,
        element: state.rects.popper,
        strategy: 'absolute',
        placement: state.placement
    });
} // eslint-disable-next-line import/no-unused-modules
var popperOffsets$1 = {
    name: 'popperOffsets',
    enabled: true,
    phase: 'read',
    fn: popperOffsets,
    data: {}
};
function getAltAxis(axis) {
    return axis === 'x' ? 'y' : 'x';
}
function preventOverflow(_ref) {
    var state = _ref.state, options = _ref.options, name = _ref.name;
    var _options$mainAxis = options.mainAxis, checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis, _options$altAxis = options.altAxis, checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis, boundary = options.boundary, rootBoundary = options.rootBoundary, altBoundary = options.altBoundary, padding = options.padding, _options$tether = options.tether, tether = _options$tether === void 0 ? true : _options$tether, _options$tetherOffset = options.tetherOffset, tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;
    var overflow = detectOverflow(state, {
        boundary: boundary,
        rootBoundary: rootBoundary,
        padding: padding,
        altBoundary: altBoundary
    });
    var basePlacement = getBasePlacement(state.placement);
    var variation = getVariation(state.placement);
    var isBasePlacement = !variation;
    var mainAxis = getMainAxisFromPlacement(basePlacement);
    var altAxis = getAltAxis(mainAxis);
    var popperOffsets = state.modifiersData.popperOffsets;
    var referenceRect = state.rects.reference;
    var popperRect = state.rects.popper;
    var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {
        placement: state.placement
    })) : tetherOffset;
    var normalizedTetherOffsetValue = typeof tetherOffsetValue === 'number' ? {
        mainAxis: tetherOffsetValue,
        altAxis: tetherOffsetValue
    } : Object.assign({
        mainAxis: 0,
        altAxis: 0
    }, tetherOffsetValue);
    var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;
    var data = {
        x: 0,
        y: 0
    };
    if (!popperOffsets) {
        return;
    }
    if (checkMainAxis) {
        var _offsetModifierState$;
        var mainSide = mainAxis === 'y' ? top : left;
        var altSide = mainAxis === 'y' ? bottom : right;
        var len = mainAxis === 'y' ? 'height' : 'width';
        var offset = popperOffsets[mainAxis];
        var min$1 = offset + overflow[mainSide];
        var max$1 = offset - overflow[altSide];
        var additive = tether ? -popperRect[len] / 2 : 0;
        var minLen = variation === start ? referenceRect[len] : popperRect[len];
        var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go
        // outside the reference bounds
        var arrowElement = state.elements.arrow;
        var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {
            width: 0,
            height: 0
        };
        var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();
        var arrowPaddingMin = arrowPaddingObject[mainSide];
        var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want
        // to include its full size in the calculation. If the reference is small
        // and near the edge of a boundary, the popper can overflow even if the
        // reference is not overflowing as well (e.g. virtual elements with no
        // width or height)
        var arrowLen = within(0, referenceRect[len], arrowRect[len]);
        var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;
        var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;
        var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);
        var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;
        var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;
        var tetherMin = offset + minOffset - offsetModifierValue - clientOffset;
        var tetherMax = offset + maxOffset - offsetModifierValue;
        var preventedOffset = within(tether ? min(min$1, tetherMin) : min$1, offset, tether ? max(max$1, tetherMax) : max$1);
        popperOffsets[mainAxis] = preventedOffset;
        data[mainAxis] = preventedOffset - offset;
    }
    if (checkAltAxis) {
        var _offsetModifierState$2;
        var _mainSide = mainAxis === 'x' ? top : left;
        var _altSide = mainAxis === 'x' ? bottom : right;
        var _offset = popperOffsets[altAxis];
        var _len = altAxis === 'y' ? 'height' : 'width';
        var _min = _offset + overflow[_mainSide];
        var _max = _offset - overflow[_altSide];
        var isOriginSide = [
            top,
            left
        ].indexOf(basePlacement) !== -1;
        var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;
        var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;
        var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;
        var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);
        popperOffsets[altAxis] = _preventedOffset;
        data[altAxis] = _preventedOffset - _offset;
    }
    state.modifiersData[name] = data;
} // eslint-disable-next-line import/no-unused-modules
var preventOverflow$1 = {
    name: 'preventOverflow',
    enabled: true,
    phase: 'main',
    fn: preventOverflow,
    requiresIfExists: [
        'offset'
    ]
};
function getHTMLElementScroll(element) {
    return {
        scrollLeft: element.scrollLeft,
        scrollTop: element.scrollTop
    };
}
function getNodeScroll(node) {
    if (node === getWindow(node) || !isHTMLElement(node)) {
        return getWindowScroll(node);
    } else {
        return getHTMLElementScroll(node);
    }
}
function isElementScaled(element) {
    var rect = element.getBoundingClientRect();
    var scaleX = round(rect.width) / element.offsetWidth || 1;
    var scaleY = round(rect.height) / element.offsetHeight || 1;
    return scaleX !== 1 || scaleY !== 1;
} // Returns the composite rect of an element relative to its offsetParent.
// Composite means it takes into account transforms as well as layout.
function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {
    if (isFixed === void 0) {
        isFixed = false;
    }
    var isOffsetParentAnElement = isHTMLElement(offsetParent);
    var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);
    var documentElement = getDocumentElement(offsetParent);
    var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled, isFixed);
    var scroll = {
        scrollLeft: 0,
        scrollTop: 0
    };
    var offsets = {
        x: 0,
        y: 0
    };
    if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {
        if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078
        isScrollParent(documentElement)) {
            scroll = getNodeScroll(offsetParent);
        }
        if (isHTMLElement(offsetParent)) {
            offsets = getBoundingClientRect(offsetParent, true);
            offsets.x += offsetParent.clientLeft;
            offsets.y += offsetParent.clientTop;
        } else if (documentElement) {
            offsets.x = getWindowScrollBarX(documentElement);
        }
    }
    return {
        x: rect.left + scroll.scrollLeft - offsets.x,
        y: rect.top + scroll.scrollTop - offsets.y,
        width: rect.width,
        height: rect.height
    };
}
function order(modifiers) {
    var map = new Map();
    var visited = new Set();
    var result = [];
    modifiers.forEach(function(modifier) {
        map.set(modifier.name, modifier);
    }); // On visiting object, check for its dependencies and visit them recursively
    function sort(modifier) {
        visited.add(modifier.name);
        var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);
        requires.forEach(function(dep) {
            if (!visited.has(dep)) {
                var depModifier = map.get(dep);
                if (depModifier) {
                    sort(depModifier);
                }
            }
        });
        result.push(modifier);
    }
    modifiers.forEach(function(modifier) {
        if (!visited.has(modifier.name)) {
            // check for visited object
            sort(modifier);
        }
    });
    return result;
}
function orderModifiers(modifiers) {
    // order based on dependencies
    var orderedModifiers = order(modifiers); // order based on phase
    return modifierPhases.reduce(function(acc, phase) {
        return acc.concat(orderedModifiers.filter(function(modifier) {
            return modifier.phase === phase;
        }));
    }, []);
}
function debounce(fn) {
    var pending;
    return function() {
        if (!pending) {
            pending = new Promise(function(resolve) {
                Promise.resolve().then(function() {
                    pending = undefined;
                    resolve(fn());
                });
            });
        }
        return pending;
    };
}
function mergeByName(modifiers) {
    var merged = modifiers.reduce(function(merged, current) {
        var existing = merged[current.name];
        merged[current.name] = existing ? Object.assign({}, existing, current, {
            options: Object.assign({}, existing.options, current.options),
            data: Object.assign({}, existing.data, current.data)
        }) : current;
        return merged;
    }, {}); // IE11 does not support Object.values
    return Object.keys(merged).map(function(key) {
        return merged[key];
    });
}
var DEFAULT_OPTIONS = {
    placement: 'bottom',
    modifiers: [],
    strategy: 'absolute'
};
function areValidElements() {
    for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
        args[_key] = arguments[_key];
    }
    return !args.some(function(element) {
        return !(element && typeof element.getBoundingClientRect === 'function');
    });
}
function popperGenerator(generatorOptions) {
    if (generatorOptions === void 0) {
        generatorOptions = {};
    }
    var _generatorOptions = generatorOptions, _generatorOptions$def = _generatorOptions.defaultModifiers, defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def, _generatorOptions$def2 = _generatorOptions.defaultOptions, defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;
    return function createPopper(reference, popper, options) {
        if (options === void 0) {
            options = defaultOptions;
        }
        var state = {
            placement: 'bottom',
            orderedModifiers: [],
            options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),
            modifiersData: {},
            elements: {
                reference: reference,
                popper: popper
            },
            attributes: {},
            styles: {}
        };
        var effectCleanupFns = [];
        var isDestroyed = false;
        var instance = {
            state: state,
            setOptions: function setOptions(setOptionsAction) {
                var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;
                cleanupModifierEffects();
                state.options = Object.assign({}, defaultOptions, state.options, options);
                state.scrollParents = {
                    reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],
                    popper: listScrollParents(popper)
                }; // Orders the modifiers based on their dependencies and `phase`
                // properties
                var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers
                state.orderedModifiers = orderedModifiers.filter(function(m) {
                    return m.enabled;
                });
                runModifierEffects();
                return instance.update();
            },
            // Sync update – it will always be executed, even if not necessary. This
            // is useful for low frequency updates where sync behavior simplifies the
            // logic.
            // For high frequency updates (e.g. `resize` and `scroll` events), always
            // prefer the async Popper#update method
            forceUpdate: function forceUpdate() {
                if (isDestroyed) {
                    return;
                }
                var _state$elements = state.elements, reference = _state$elements.reference, popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements
                // anymore
                if (!areValidElements(reference, popper)) {
                    return;
                } // Store the reference and popper rects to be read by modifiers
                state.rects = {
                    reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),
                    popper: getLayoutRect(popper)
                }; // Modifiers have the ability to reset the current update cycle. The
                // most common use case for this is the `flip` modifier changing the
                // placement, which then needs to re-run all the modifiers, because the
                // logic was previously ran for the previous placement and is therefore
                // stale/incorrect
                state.reset = false;
                state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier
                // is filled with the initial data specified by the modifier. This means
                // it doesn't persist and is fresh on each update.
                // To ensure persistent data, use `${name}#persistent`
                state.orderedModifiers.forEach(function(modifier) {
                    return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);
                });
                for(var index = 0; index < state.orderedModifiers.length; index++){
                    if (state.reset === true) {
                        state.reset = false;
                        index = -1;
                        continue;
                    }
                    var _state$orderedModifie = state.orderedModifiers[index], fn = _state$orderedModifie.fn, _state$orderedModifie2 = _state$orderedModifie.options, _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2, name = _state$orderedModifie.name;
                    if (typeof fn === 'function') {
                        state = fn({
                            state: state,
                            options: _options,
                            name: name,
                            instance: instance
                        }) || state;
                    }
                }
            },
            // Async and optimistically optimized update – it will not be executed if
            // not necessary (debounced to run at most once-per-tick)
            update: debounce(function() {
                return new Promise(function(resolve) {
                    instance.forceUpdate();
                    resolve(state);
                });
            }),
            destroy: function destroy() {
                cleanupModifierEffects();
                isDestroyed = true;
            }
        };
        if (!areValidElements(reference, popper)) {
            return instance;
        }
        instance.setOptions(options).then(function(state) {
            if (!isDestroyed && options.onFirstUpdate) {
                options.onFirstUpdate(state);
            }
        }); // Modifiers have the ability to execute arbitrary code before the first
        // update cycle runs. They will be executed in the same order as the update
        // cycle. This is useful when a modifier adds some persistent data that
        // other modifiers need to use, but the modifier is run after the dependent
        // one.
        function runModifierEffects() {
            state.orderedModifiers.forEach(function(_ref) {
                var name = _ref.name, _ref$options = _ref.options, options = _ref$options === void 0 ? {} : _ref$options, effect = _ref.effect;
                if (typeof effect === 'function') {
                    var cleanupFn = effect({
                        state: state,
                        name: name,
                        instance: instance,
                        options: options
                    });
                    var noopFn = function noopFn() {};
                    effectCleanupFns.push(cleanupFn || noopFn);
                }
            });
        }
        function cleanupModifierEffects() {
            effectCleanupFns.forEach(function(fn) {
                return fn();
            });
            effectCleanupFns = [];
        }
        return instance;
    };
}
// For the common JS build we will turn this file into a bundle with no imports.
// This is b/c the Popper lib is all esm files, and would break in a common js only environment
const createPopper = popperGenerator({
    defaultModifiers: [
        hide$1,
        popperOffsets$1,
        computeStyles$1,
        eventListeners,
        offset$1,
        flip$1,
        preventOverflow$1,
        arrow$1
    ]
});
exports.createPopper = createPopper;
exports.placements = placements;
}}),
"[project]/node_modules/@restart/ui/cjs/usePopper.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
exports.default = void 0;
var _react = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var _dequal = __turbopack_context__.r("[project]/node_modules/dequal/dist/index.js [app-ssr] (ecmascript)");
var _useSafeState = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useSafeState.js [app-ssr] (ecmascript)"));
var _popper = __turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/popper.js [app-ssr] (ecmascript)");
const _excluded = [
    "enabled",
    "placement",
    "strategy",
    "modifiers"
];
function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}
function _objectWithoutPropertiesLoose(r, e) {
    if (null == r) return {};
    var t = {};
    for(var n in r)if (({}).hasOwnProperty.call(r, n)) {
        if (e.indexOf(n) >= 0) continue;
        t[n] = r[n];
    }
    return t;
}
const disabledApplyStylesModifier = {
    name: 'applyStyles',
    enabled: false,
    phase: 'afterWrite',
    fn: ()=>undefined
};
// until docjs supports type exports...
const ariaDescribedByModifier = {
    name: 'ariaDescribedBy',
    enabled: true,
    phase: 'afterWrite',
    effect: ({ state })=>()=>{
            const { reference, popper } = state.elements;
            if ('removeAttribute' in reference) {
                const ids = (reference.getAttribute('aria-describedby') || '').split(',').filter((id)=>id.trim() !== popper.id);
                if (!ids.length) reference.removeAttribute('aria-describedby');
                else reference.setAttribute('aria-describedby', ids.join(','));
            }
        },
    fn: ({ state })=>{
        var _popper$getAttribute;
        const { popper, reference } = state.elements;
        const role = (_popper$getAttribute = popper.getAttribute('role')) == null ? void 0 : _popper$getAttribute.toLowerCase();
        if (popper.id && role === 'tooltip' && 'setAttribute' in reference) {
            const ids = reference.getAttribute('aria-describedby');
            if (ids && ids.split(',').indexOf(popper.id) !== -1) {
                return;
            }
            reference.setAttribute('aria-describedby', ids ? `${ids},${popper.id}` : popper.id);
        }
    }
};
const EMPTY_MODIFIERS = [];
/**
 * Position an element relative some reference element using Popper.js
 *
 * @param referenceElement
 * @param popperElement
 * @param {object}      options
 * @param {object=}     options.modifiers Popper.js modifiers
 * @param {boolean=}    options.enabled toggle the popper functionality on/off
 * @param {string=}     options.placement The popper element placement relative to the reference element
 * @param {string=}     options.strategy the positioning strategy
 * @param {function=}   options.onCreate called when the popper is created
 * @param {function=}   options.onUpdate called when the popper is updated
 *
 * @returns {UsePopperState} The popper state
 */ function usePopper(referenceElement, popperElement, _ref = {}) {
    let { enabled = true, placement = 'bottom', strategy = 'absolute', modifiers = EMPTY_MODIFIERS } = _ref, config = _objectWithoutPropertiesLoose(_ref, _excluded);
    const prevModifiers = (0, _react.useRef)(modifiers);
    const popperInstanceRef = (0, _react.useRef)();
    const update = (0, _react.useCallback)(()=>{
        var _popperInstanceRef$cu;
        (_popperInstanceRef$cu = popperInstanceRef.current) == null ? void 0 : _popperInstanceRef$cu.update();
    }, []);
    const forceUpdate = (0, _react.useCallback)(()=>{
        var _popperInstanceRef$cu2;
        (_popperInstanceRef$cu2 = popperInstanceRef.current) == null ? void 0 : _popperInstanceRef$cu2.forceUpdate();
    }, []);
    const [popperState, setState] = (0, _useSafeState.default)((0, _react.useState)({
        placement,
        update,
        forceUpdate,
        attributes: {},
        styles: {
            popper: {},
            arrow: {}
        }
    }));
    const updateModifier = (0, _react.useMemo)(()=>({
            name: 'updateStateModifier',
            enabled: true,
            phase: 'write',
            requires: [
                'computeStyles'
            ],
            fn: ({ state })=>{
                const styles = {};
                const attributes = {};
                Object.keys(state.elements).forEach((element)=>{
                    styles[element] = state.styles[element];
                    attributes[element] = state.attributes[element];
                });
                setState({
                    state,
                    styles,
                    attributes,
                    update,
                    forceUpdate,
                    placement: state.placement
                });
            }
        }), [
        update,
        forceUpdate,
        setState
    ]);
    const nextModifiers = (0, _react.useMemo)(()=>{
        if (!(0, _dequal.dequal)(prevModifiers.current, modifiers)) {
            prevModifiers.current = modifiers;
        }
        return prevModifiers.current;
    }, [
        modifiers
    ]);
    (0, _react.useEffect)(()=>{
        if (!popperInstanceRef.current || !enabled) return;
        popperInstanceRef.current.setOptions({
            placement,
            strategy,
            modifiers: [
                ...nextModifiers,
                updateModifier,
                disabledApplyStylesModifier
            ]
        });
    }, [
        strategy,
        placement,
        updateModifier,
        enabled,
        nextModifiers
    ]);
    (0, _react.useEffect)(()=>{
        if (!enabled || referenceElement == null || popperElement == null) {
            return undefined;
        }
        popperInstanceRef.current = (0, _popper.createPopper)(referenceElement, popperElement, Object.assign({}, config, {
            placement,
            strategy,
            modifiers: [
                ...nextModifiers,
                ariaDescribedByModifier,
                updateModifier
            ]
        }));
        return ()=>{
            if (popperInstanceRef.current != null) {
                popperInstanceRef.current.destroy();
                popperInstanceRef.current = undefined;
                setState((s)=>Object.assign({}, s, {
                        attributes: {},
                        styles: {
                            popper: {}
                        }
                    }));
            }
        };
    // This is only run once to _create_ the popper
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        enabled,
        referenceElement,
        popperElement
    ]);
    return popperState;
}
var _default = exports.default = usePopper;
}}),
"[project]/node_modules/@restart/ui/cjs/useClickOutside.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
exports.getRefTarget = exports.default = void 0;
var _contains = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/dom-helpers/esm/contains.js [app-ssr] (ecmascript)"));
var _listen = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/dom-helpers/esm/listen.js [app-ssr] (ecmascript)"));
var _ownerDocument = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/dom-helpers/esm/ownerDocument.js [app-ssr] (ecmascript)"));
var _react = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var _useEventCallback = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useEventCallback.js [app-ssr] (ecmascript)"));
var _warning = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/warning/warning.js [app-ssr] (ecmascript)"));
function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}
const noop = ()=>{};
function isLeftClickEvent(event) {
    return event.button === 0;
}
function isModifiedEvent(event) {
    return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);
}
const getRefTarget = (ref)=>ref && ('current' in ref ? ref.current : ref);
exports.getRefTarget = getRefTarget;
const InitialTriggerEvents = {
    click: 'mousedown',
    mouseup: 'mousedown',
    pointerup: 'pointerdown'
};
/**
 * The `useClickOutside` hook registers your callback on the document that fires
 * when a pointer event is registered outside of the provided ref or element.
 *
 * @param {Ref<HTMLElement>| HTMLElement} ref  The element boundary
 * @param {function} onClickOutside
 * @param {object=}  options
 * @param {boolean=} options.disabled
 * @param {string=}  options.clickTrigger The DOM event name (click, mousedown, etc) to attach listeners on
 */ function useClickOutside(ref, onClickOutside = noop, { disabled, clickTrigger = 'click' } = {}) {
    const preventMouseClickOutsideRef = (0, _react.useRef)(false);
    const waitingForTrigger = (0, _react.useRef)(false);
    const handleMouseCapture = (0, _react.useCallback)((e)=>{
        const currentTarget = getRefTarget(ref);
        (0, _warning.default)(!!currentTarget, 'ClickOutside captured a close event but does not have a ref to compare it to. ' + 'useClickOutside(), should be passed a ref that resolves to a DOM node');
        preventMouseClickOutsideRef.current = !currentTarget || isModifiedEvent(e) || !isLeftClickEvent(e) || !!(0, _contains.default)(currentTarget, e.target) || waitingForTrigger.current;
        waitingForTrigger.current = false;
    }, [
        ref
    ]);
    const handleInitialMouse = (0, _useEventCallback.default)((e)=>{
        const currentTarget = getRefTarget(ref);
        if (currentTarget && (0, _contains.default)(currentTarget, e.target)) {
            waitingForTrigger.current = true;
        } else {
            // When clicking on scrollbars within current target, click events are not triggered, so this ref
            // is never reset inside `handleMouseCapture`. This would cause a bug where it requires 2 clicks
            // to close the overlay.
            waitingForTrigger.current = false;
        }
    });
    const handleMouse = (0, _useEventCallback.default)((e)=>{
        if (!preventMouseClickOutsideRef.current) {
            onClickOutside(e);
        }
    });
    (0, _react.useEffect)(()=>{
        var _ownerWindow$event, _ownerWindow$parent;
        if (disabled || ref == null) return undefined;
        const doc = (0, _ownerDocument.default)(getRefTarget(ref));
        const ownerWindow = doc.defaultView || window;
        // Store the current event to avoid triggering handlers immediately
        // For things rendered in an iframe, the event might originate on the parent window
        // so we should fall back to that global event if the local one doesn't exist
        // https://github.com/facebook/react/issues/20074
        let currentEvent = (_ownerWindow$event = ownerWindow.event) != null ? _ownerWindow$event : (_ownerWindow$parent = ownerWindow.parent) == null ? void 0 : _ownerWindow$parent.event;
        let removeInitialTriggerListener = null;
        if (InitialTriggerEvents[clickTrigger]) {
            removeInitialTriggerListener = (0, _listen.default)(doc, InitialTriggerEvents[clickTrigger], handleInitialMouse, true);
        }
        // Use capture for this listener so it fires before React's listener, to
        // avoid false positives in the contains() check below if the target DOM
        // element is removed in the React mouse callback.
        const removeMouseCaptureListener = (0, _listen.default)(doc, clickTrigger, handleMouseCapture, true);
        const removeMouseListener = (0, _listen.default)(doc, clickTrigger, (e)=>{
            // skip if this event is the same as the one running when we added the handlers
            if (e === currentEvent) {
                currentEvent = undefined;
                return;
            }
            handleMouse(e);
        });
        let mobileSafariHackListeners = [];
        if ('ontouchstart' in doc.documentElement) {
            mobileSafariHackListeners = [].slice.call(doc.body.children).map((el)=>(0, _listen.default)(el, 'mousemove', noop));
        }
        return ()=>{
            removeInitialTriggerListener == null ? void 0 : removeInitialTriggerListener();
            removeMouseCaptureListener();
            removeMouseListener();
            mobileSafariHackListeners.forEach((remove)=>remove());
        };
    }, [
        ref,
        disabled,
        clickTrigger,
        handleMouseCapture,
        handleInitialMouse,
        handleMouse
    ]);
}
var _default = exports.default = useClickOutside;
}}),
"[project]/node_modules/@restart/ui/cjs/mergeOptionsWithPopperConfig.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
exports.default = mergeOptionsWithPopperConfig;
exports.toModifierArray = toModifierArray;
exports.toModifierMap = toModifierMap;
function toModifierMap(modifiers) {
    const result = {};
    if (!Array.isArray(modifiers)) {
        return modifiers || result;
    }
    // eslint-disable-next-line no-unused-expressions
    modifiers == null ? void 0 : modifiers.forEach((m)=>{
        result[m.name] = m;
    });
    return result;
}
function toModifierArray(map = {}) {
    if (Array.isArray(map)) return map;
    return Object.keys(map).map((k)=>{
        map[k].name = k;
        return map[k];
    });
}
function mergeOptionsWithPopperConfig({ enabled, enableEvents, placement, flip, offset, fixed, containerPadding, arrowElement, popperConfig = {} }) {
    var _modifiers$eventListe, _modifiers$preventOve, _modifiers$preventOve2, _modifiers$offset, _modifiers$arrow;
    const modifiers = toModifierMap(popperConfig.modifiers);
    return Object.assign({}, popperConfig, {
        placement,
        enabled,
        strategy: fixed ? 'fixed' : popperConfig.strategy,
        modifiers: toModifierArray(Object.assign({}, modifiers, {
            eventListeners: {
                enabled: enableEvents,
                options: (_modifiers$eventListe = modifiers.eventListeners) == null ? void 0 : _modifiers$eventListe.options
            },
            preventOverflow: Object.assign({}, modifiers.preventOverflow, {
                options: containerPadding ? Object.assign({
                    padding: containerPadding
                }, (_modifiers$preventOve = modifiers.preventOverflow) == null ? void 0 : _modifiers$preventOve.options) : (_modifiers$preventOve2 = modifiers.preventOverflow) == null ? void 0 : _modifiers$preventOve2.options
            }),
            offset: {
                options: Object.assign({
                    offset
                }, (_modifiers$offset = modifiers.offset) == null ? void 0 : _modifiers$offset.options)
            },
            arrow: Object.assign({}, modifiers.arrow, {
                enabled: !!arrowElement,
                options: Object.assign({}, (_modifiers$arrow = modifiers.arrow) == null ? void 0 : _modifiers$arrow.options, {
                    element: arrowElement
                })
            }),
            flip: Object.assign({
                enabled: !!flip
            }, modifiers.flip)
        }))
    });
}
}}),
"[project]/node_modules/@restart/ui/cjs/DropdownMenu.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
exports.default = void 0;
exports.useDropdownMenu = useDropdownMenu;
var _react = _interopRequireWildcard(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var React = _react;
var _useCallbackRef = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useCallbackRef.js [app-ssr] (ecmascript)"));
var _DropdownContext = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/DropdownContext.js [app-ssr] (ecmascript)"));
var _usePopper = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/usePopper.js [app-ssr] (ecmascript)"));
var _useClickOutside = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/useClickOutside.js [app-ssr] (ecmascript)"));
var _mergeOptionsWithPopperConfig = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/mergeOptionsWithPopperConfig.js [app-ssr] (ecmascript)"));
var _jsxRuntime = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js [app-ssr] (ecmascript)");
const _excluded = [
    "children",
    "usePopper"
];
function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}
function _getRequireWildcardCache(e) {
    if ("function" != typeof WeakMap) return null;
    var r = new WeakMap(), t = new WeakMap();
    return (_getRequireWildcardCache = function(e) {
        return e ? t : r;
    })(e);
}
function _interopRequireWildcard(e, r) {
    if (!r && e && e.__esModule) return e;
    if (null === e || "object" != typeof e && "function" != typeof e) return {
        default: e
    };
    var t = _getRequireWildcardCache(r);
    if (t && t.has(e)) return t.get(e);
    var n = {
        __proto__: null
    }, a = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var u in e)if ("default" !== u && ({}).hasOwnProperty.call(e, u)) {
        var i = a ? Object.getOwnPropertyDescriptor(e, u) : null;
        i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u];
    }
    return n.default = e, t && t.set(e, n), n;
}
function _objectWithoutPropertiesLoose(r, e) {
    if (null == r) return {};
    var t = {};
    for(var n in r)if (({}).hasOwnProperty.call(r, n)) {
        if (e.indexOf(n) >= 0) continue;
        t[n] = r[n];
    }
    return t;
}
const noop = ()=>{};
/**
 * @memberOf Dropdown
 * @param {object}  options
 * @param {boolean} options.flip Automatically adjust the menu `drop` position based on viewport edge detection
 * @param {[number, number]} options.offset Define an offset distance between the Menu and the Toggle
 * @param {boolean} options.show Display the menu manually, ignored in the context of a `Dropdown`
 * @param {boolean} options.usePopper opt in/out of using PopperJS to position menus. When disabled you must position it yourself.
 * @param {string}  options.rootCloseEvent The pointer event to listen for when determining "clicks outside" the menu for triggering a close.
 * @param {object}  options.popperConfig Options passed to the [`usePopper`](/api/usePopper) hook.
 */ function useDropdownMenu(options = {}) {
    const context = (0, _react.useContext)(_DropdownContext.default);
    const [arrowElement, attachArrowRef] = (0, _useCallbackRef.default)();
    const hasShownRef = (0, _react.useRef)(false);
    const { flip, offset, rootCloseEvent, fixed = false, placement: placementOverride, popperConfig = {}, enableEventListeners = true, usePopper: shouldUsePopper = !!context } = options;
    const show = (context == null ? void 0 : context.show) == null ? !!options.show : context.show;
    if (show && !hasShownRef.current) {
        hasShownRef.current = true;
    }
    const handleClose = (e)=>{
        context == null ? void 0 : context.toggle(false, e);
    };
    const { placement, setMenu, menuElement, toggleElement } = context || {};
    const popper = (0, _usePopper.default)(toggleElement, menuElement, (0, _mergeOptionsWithPopperConfig.default)({
        placement: placementOverride || placement || 'bottom-start',
        enabled: shouldUsePopper,
        enableEvents: enableEventListeners == null ? show : enableEventListeners,
        offset,
        flip,
        fixed,
        arrowElement,
        popperConfig
    }));
    const menuProps = Object.assign({
        ref: setMenu || noop,
        'aria-labelledby': toggleElement == null ? void 0 : toggleElement.id
    }, popper.attributes.popper, {
        style: popper.styles.popper
    });
    const metadata = {
        show,
        placement,
        hasShown: hasShownRef.current,
        toggle: context == null ? void 0 : context.toggle,
        popper: shouldUsePopper ? popper : null,
        arrowProps: shouldUsePopper ? Object.assign({
            ref: attachArrowRef
        }, popper.attributes.arrow, {
            style: popper.styles.arrow
        }) : {}
    };
    (0, _useClickOutside.default)(menuElement, handleClose, {
        clickTrigger: rootCloseEvent,
        disabled: !show
    });
    return [
        menuProps,
        metadata
    ];
}
/**
 * Also exported as `<Dropdown.Menu>` from `Dropdown`.
 *
 * @displayName DropdownMenu
 * @memberOf Dropdown
 */ function DropdownMenu(_ref) {
    let { children, usePopper: usePopperProp = true } = _ref, options = _objectWithoutPropertiesLoose(_ref, _excluded);
    const [props, meta] = useDropdownMenu(Object.assign({}, options, {
        usePopper: usePopperProp
    }));
    return /*#__PURE__*/ (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: children(props, meta)
    });
}
DropdownMenu.displayName = 'DropdownMenu';
/** @component */ var _default = exports.default = DropdownMenu;
}}),
"[project]/node_modules/@restart/ui/cjs/ssr.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
var _ssr = __turbopack_context__.r("[project]/node_modules/@react-aria/ssr/dist/main.js [app-ssr] (ecmascript)");
exports.useSSRSafeId = _ssr.useSSRSafeId;
exports.useIsSSR = _ssr.useIsSSR;
exports.SSRProvider = _ssr.SSRProvider;
}}),
"[project]/node_modules/@restart/ui/cjs/DropdownToggle.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
exports.isRoleMenu = exports.default = void 0;
exports.useDropdownToggle = useDropdownToggle;
var _react = _interopRequireWildcard(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var React = _react;
var _ssr = __turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/ssr.js [app-ssr] (ecmascript)");
var _DropdownContext = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/DropdownContext.js [app-ssr] (ecmascript)"));
var _jsxRuntime = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js [app-ssr] (ecmascript)");
function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}
function _getRequireWildcardCache(e) {
    if ("function" != typeof WeakMap) return null;
    var r = new WeakMap(), t = new WeakMap();
    return (_getRequireWildcardCache = function(e) {
        return e ? t : r;
    })(e);
}
function _interopRequireWildcard(e, r) {
    if (!r && e && e.__esModule) return e;
    if (null === e || "object" != typeof e && "function" != typeof e) return {
        default: e
    };
    var t = _getRequireWildcardCache(r);
    if (t && t.has(e)) return t.get(e);
    var n = {
        __proto__: null
    }, a = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var u in e)if ("default" !== u && ({}).hasOwnProperty.call(e, u)) {
        var i = a ? Object.getOwnPropertyDescriptor(e, u) : null;
        i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u];
    }
    return n.default = e, t && t.set(e, n), n;
}
const isRoleMenu = (el)=>{
    var _el$getAttribute;
    return ((_el$getAttribute = el.getAttribute('role')) == null ? void 0 : _el$getAttribute.toLowerCase()) === 'menu';
};
exports.isRoleMenu = isRoleMenu;
const noop = ()=>{};
/**
 * Wires up Dropdown toggle functionality, returning a set a props to attach
 * to the element that functions as the dropdown toggle (generally a button).
 *
 * @memberOf Dropdown
 */ function useDropdownToggle() {
    const id = (0, _ssr.useSSRSafeId)();
    const { show = false, toggle = noop, setToggle, menuElement } = (0, _react.useContext)(_DropdownContext.default) || {};
    const handleClick = (0, _react.useCallback)((e)=>{
        toggle(!show, e);
    }, [
        show,
        toggle
    ]);
    const props = {
        id,
        ref: setToggle || noop,
        onClick: handleClick,
        'aria-expanded': !!show
    };
    // This is maybe better down in an effect, but
    // the component is going to update anyway when the menu element
    // is set so might return new props.
    if (menuElement && isRoleMenu(menuElement)) {
        props['aria-haspopup'] = true;
    }
    return [
        props,
        {
            show,
            toggle
        }
    ];
}
/**
 * Also exported as `<Dropdown.Toggle>` from `Dropdown`.
 *
 * @displayName DropdownToggle
 * @memberOf Dropdown
 */ function DropdownToggle({ children }) {
    const [props, meta] = useDropdownToggle();
    return /*#__PURE__*/ (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: children(props, meta)
    });
}
DropdownToggle.displayName = 'DropdownToggle';
/** @component */ var _default = exports.default = DropdownToggle;
}}),
"[project]/node_modules/@restart/ui/cjs/NavContext.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
exports.default = void 0;
var React = _interopRequireWildcard(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
function _getRequireWildcardCache(e) {
    if ("function" != typeof WeakMap) return null;
    var r = new WeakMap(), t = new WeakMap();
    return (_getRequireWildcardCache = function(e) {
        return e ? t : r;
    })(e);
}
function _interopRequireWildcard(e, r) {
    if (!r && e && e.__esModule) return e;
    if (null === e || "object" != typeof e && "function" != typeof e) return {
        default: e
    };
    var t = _getRequireWildcardCache(r);
    if (t && t.has(e)) return t.get(e);
    var n = {
        __proto__: null
    }, a = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var u in e)if ("default" !== u && ({}).hasOwnProperty.call(e, u)) {
        var i = a ? Object.getOwnPropertyDescriptor(e, u) : null;
        i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u];
    }
    return n.default = e, t && t.set(e, n), n;
}
const NavContext = /*#__PURE__*/ React.createContext(null);
NavContext.displayName = 'NavContext';
var _default = exports.default = NavContext;
}}),
"[project]/node_modules/@restart/ui/cjs/DropdownItem.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
exports.default = void 0;
exports.useDropdownItem = useDropdownItem;
var _react = _interopRequireWildcard(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var React = _react;
var _useEventCallback = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useEventCallback.js [app-ssr] (ecmascript)"));
var _SelectableContext = _interopRequireWildcard(__turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/SelectableContext.js [app-ssr] (ecmascript)"));
var _NavContext = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/NavContext.js [app-ssr] (ecmascript)"));
var _Button = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/Button.js [app-ssr] (ecmascript)"));
var _DataKey = __turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/DataKey.js [app-ssr] (ecmascript)");
var _jsxRuntime = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js [app-ssr] (ecmascript)");
const _excluded = [
    "eventKey",
    "disabled",
    "onClick",
    "active",
    "as"
];
function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}
function _getRequireWildcardCache(e) {
    if ("function" != typeof WeakMap) return null;
    var r = new WeakMap(), t = new WeakMap();
    return (_getRequireWildcardCache = function(e) {
        return e ? t : r;
    })(e);
}
function _interopRequireWildcard(e, r) {
    if (!r && e && e.__esModule) return e;
    if (null === e || "object" != typeof e && "function" != typeof e) return {
        default: e
    };
    var t = _getRequireWildcardCache(r);
    if (t && t.has(e)) return t.get(e);
    var n = {
        __proto__: null
    }, a = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var u in e)if ("default" !== u && ({}).hasOwnProperty.call(e, u)) {
        var i = a ? Object.getOwnPropertyDescriptor(e, u) : null;
        i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u];
    }
    return n.default = e, t && t.set(e, n), n;
}
function _objectWithoutPropertiesLoose(r, e) {
    if (null == r) return {};
    var t = {};
    for(var n in r)if (({}).hasOwnProperty.call(r, n)) {
        if (e.indexOf(n) >= 0) continue;
        t[n] = r[n];
    }
    return t;
}
/**
 * Create a dropdown item. Returns a set of props for the dropdown item component
 * including an `onClick` handler that prevents selection when the item is disabled
 */ function useDropdownItem({ key, href, active, disabled, onClick }) {
    const onSelectCtx = (0, _react.useContext)(_SelectableContext.default);
    const navContext = (0, _react.useContext)(_NavContext.default);
    const { activeKey } = navContext || {};
    const eventKey = (0, _SelectableContext.makeEventKey)(key, href);
    const isActive = active == null && key != null ? (0, _SelectableContext.makeEventKey)(activeKey) === eventKey : active;
    const handleClick = (0, _useEventCallback.default)((event)=>{
        if (disabled) return;
        onClick == null ? void 0 : onClick(event);
        if (onSelectCtx && !event.isPropagationStopped()) {
            onSelectCtx(eventKey, event);
        }
    });
    return [
        {
            onClick: handleClick,
            'aria-disabled': disabled || undefined,
            'aria-selected': isActive,
            [(0, _DataKey.dataAttr)('dropdown-item')]: ''
        },
        {
            isActive
        }
    ];
}
const DropdownItem = /*#__PURE__*/ React.forwardRef((_ref, ref)=>{
    let { eventKey, disabled, onClick, active, as: Component = _Button.default } = _ref, props = _objectWithoutPropertiesLoose(_ref, _excluded);
    const [dropdownItemProps] = useDropdownItem({
        key: eventKey,
        href: props.href,
        disabled,
        onClick,
        active
    });
    return /*#__PURE__*/ (0, _jsxRuntime.jsx)(Component, Object.assign({}, props, {
        ref: ref
    }, dropdownItemProps));
});
DropdownItem.displayName = 'DropdownItem';
var _default = exports.default = DropdownItem;
}}),
"[project]/node_modules/@restart/ui/cjs/Dropdown.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
exports.__esModule = true;
exports.default = void 0;
var _querySelectorAll = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/dom-helpers/esm/querySelectorAll.js [app-ssr] (ecmascript)"));
var _addEventListener = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/dom-helpers/esm/addEventListener.js [app-ssr] (ecmascript)"));
var _react = _interopRequireWildcard(__turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"));
var React = _react;
var _uncontrollable = __turbopack_context__.r("[project]/node_modules/@restart/ui/node_modules/uncontrollable/lib/esm/index.js [app-ssr] (ecmascript)");
var _usePrevious = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/node_modules/@restart/hooks/cjs/usePrevious.js [app-ssr] (ecmascript)"));
var _useForceUpdate = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useForceUpdate.js [app-ssr] (ecmascript)"));
var _useEventListener = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useEventListener.js [app-ssr] (ecmascript)"));
var _useEventCallback = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useEventCallback.js [app-ssr] (ecmascript)"));
var _DropdownContext = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/DropdownContext.js [app-ssr] (ecmascript)"));
var _DropdownMenu = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/DropdownMenu.js [app-ssr] (ecmascript)"));
var _DropdownToggle = _interopRequireWildcard(__turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/DropdownToggle.js [app-ssr] (ecmascript)"));
var _DropdownItem = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/DropdownItem.js [app-ssr] (ecmascript)"));
var _SelectableContext = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/SelectableContext.js [app-ssr] (ecmascript)"));
var _DataKey = __turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/DataKey.js [app-ssr] (ecmascript)");
var _useWindow = _interopRequireDefault(__turbopack_context__.r("[project]/node_modules/@restart/ui/cjs/useWindow.js [app-ssr] (ecmascript)"));
var _jsxRuntime = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js [app-ssr] (ecmascript)");
function _getRequireWildcardCache(e) {
    if ("function" != typeof WeakMap) return null;
    var r = new WeakMap(), t = new WeakMap();
    return (_getRequireWildcardCache = function(e) {
        return e ? t : r;
    })(e);
}
function _interopRequireWildcard(e, r) {
    if (!r && e && e.__esModule) return e;
    if (null === e || "object" != typeof e && "function" != typeof e) return {
        default: e
    };
    var t = _getRequireWildcardCache(r);
    if (t && t.has(e)) return t.get(e);
    var n = {
        __proto__: null
    }, a = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var u in e)if ("default" !== u && ({}).hasOwnProperty.call(e, u)) {
        var i = a ? Object.getOwnPropertyDescriptor(e, u) : null;
        i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u];
    }
    return n.default = e, t && t.set(e, n), n;
}
function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}
function useRefWithUpdate() {
    const forceUpdate = (0, _useForceUpdate.default)();
    const ref = (0, _react.useRef)(null);
    const attachRef = (0, _react.useCallback)((element)=>{
        ref.current = element;
        // ensure that a menu set triggers an update for consumers
        forceUpdate();
    }, [
        forceUpdate
    ]);
    return [
        ref,
        attachRef
    ];
}
/**
 * @displayName Dropdown
 * @public
 */ function Dropdown({ defaultShow, show: rawShow, onSelect, onToggle: rawOnToggle, itemSelector = `* [${(0, _DataKey.dataAttr)('dropdown-item')}]`, focusFirstItemOnShow, placement = 'bottom-start', children }) {
    const window = (0, _useWindow.default)();
    const [show, onToggle] = (0, _uncontrollable.useUncontrolledProp)(rawShow, defaultShow, rawOnToggle);
    // We use normal refs instead of useCallbackRef in order to populate the
    // the value as quickly as possible, otherwise the effect to focus the element
    // may run before the state value is set
    const [menuRef, setMenu] = useRefWithUpdate();
    const menuElement = menuRef.current;
    const [toggleRef, setToggle] = useRefWithUpdate();
    const toggleElement = toggleRef.current;
    const lastShow = (0, _usePrevious.default)(show);
    const lastSourceEvent = (0, _react.useRef)(null);
    const focusInDropdown = (0, _react.useRef)(false);
    const onSelectCtx = (0, _react.useContext)(_SelectableContext.default);
    const toggle = (0, _react.useCallback)((nextShow, event, source = event == null ? void 0 : event.type)=>{
        onToggle(nextShow, {
            originalEvent: event,
            source
        });
    }, [
        onToggle
    ]);
    const handleSelect = (0, _useEventCallback.default)((key, event)=>{
        onSelect == null ? void 0 : onSelect(key, event);
        toggle(false, event, 'select');
        if (!event.isPropagationStopped()) {
            onSelectCtx == null ? void 0 : onSelectCtx(key, event);
        }
    });
    const context = (0, _react.useMemo)(()=>({
            toggle,
            placement,
            show,
            menuElement,
            toggleElement,
            setMenu,
            setToggle
        }), [
        toggle,
        placement,
        show,
        menuElement,
        toggleElement,
        setMenu,
        setToggle
    ]);
    if (menuElement && lastShow && !show) {
        focusInDropdown.current = menuElement.contains(menuElement.ownerDocument.activeElement);
    }
    const focusToggle = (0, _useEventCallback.default)(()=>{
        if (toggleElement && toggleElement.focus) {
            toggleElement.focus();
        }
    });
    const maybeFocusFirst = (0, _useEventCallback.default)(()=>{
        const type = lastSourceEvent.current;
        let focusType = focusFirstItemOnShow;
        if (focusType == null) {
            focusType = menuRef.current && (0, _DropdownToggle.isRoleMenu)(menuRef.current) ? 'keyboard' : false;
        }
        if (focusType === false || focusType === 'keyboard' && !/^key.+$/.test(type)) {
            return;
        }
        const first = (0, _querySelectorAll.default)(menuRef.current, itemSelector)[0];
        if (first && first.focus) first.focus();
    });
    (0, _react.useEffect)(()=>{
        if (show) maybeFocusFirst();
        else if (focusInDropdown.current) {
            focusInDropdown.current = false;
            focusToggle();
        }
    // only `show` should be changing
    }, [
        show,
        focusInDropdown,
        focusToggle,
        maybeFocusFirst
    ]);
    (0, _react.useEffect)(()=>{
        lastSourceEvent.current = null;
    });
    const getNextFocusedChild = (current, offset)=>{
        if (!menuRef.current) return null;
        const items = (0, _querySelectorAll.default)(menuRef.current, itemSelector);
        let index = items.indexOf(current) + offset;
        index = Math.max(0, Math.min(index, items.length));
        return items[index];
    };
    (0, _useEventListener.default)((0, _react.useCallback)(()=>window.document, [
        window
    ]), 'keydown', (event)=>{
        var _menuRef$current, _toggleRef$current;
        const { key } = event;
        const target = event.target;
        const fromMenu = (_menuRef$current = menuRef.current) == null ? void 0 : _menuRef$current.contains(target);
        const fromToggle = (_toggleRef$current = toggleRef.current) == null ? void 0 : _toggleRef$current.contains(target);
        // Second only to https://github.com/twbs/bootstrap/blob/8cfbf6933b8a0146ac3fbc369f19e520bd1ebdac/js/src/dropdown.js#L400
        // in inscrutability
        const isInput = /input|textarea/i.test(target.tagName);
        if (isInput && (key === ' ' || key !== 'Escape' && fromMenu || key === 'Escape' && target.type === 'search')) {
            return;
        }
        if (!fromMenu && !fromToggle) {
            return;
        }
        if (key === 'Tab' && (!menuRef.current || !show)) {
            return;
        }
        lastSourceEvent.current = event.type;
        const meta = {
            originalEvent: event,
            source: event.type
        };
        switch(key){
            case 'ArrowUp':
                {
                    const next = getNextFocusedChild(target, -1);
                    if (next && next.focus) next.focus();
                    event.preventDefault();
                    return;
                }
            case 'ArrowDown':
                event.preventDefault();
                if (!show) {
                    onToggle(true, meta);
                } else {
                    const next = getNextFocusedChild(target, 1);
                    if (next && next.focus) next.focus();
                }
                return;
            case 'Tab':
                // on keydown the target is the element being tabbed FROM, we need that
                // to know if this event is relevant to this dropdown (e.g. in this menu).
                // On `keyup` the target is the element being tagged TO which we use to check
                // if focus has left the menu
                (0, _addEventListener.default)(target.ownerDocument, 'keyup', (e)=>{
                    var _menuRef$current2;
                    if (e.key === 'Tab' && !e.target || !((_menuRef$current2 = menuRef.current) != null && _menuRef$current2.contains(e.target))) {
                        onToggle(false, meta);
                    }
                }, {
                    once: true
                });
                break;
            case 'Escape':
                if (key === 'Escape') {
                    event.preventDefault();
                    event.stopPropagation();
                }
                onToggle(false, meta);
                break;
            default:
        }
    });
    return /*#__PURE__*/ (0, _jsxRuntime.jsx)(_SelectableContext.default.Provider, {
        value: handleSelect,
        children: /*#__PURE__*/ (0, _jsxRuntime.jsx)(_DropdownContext.default.Provider, {
            value: context,
            children: children
        })
    });
}
Dropdown.displayName = 'Dropdown';
Dropdown.Menu = _DropdownMenu.default;
Dropdown.Toggle = _DropdownToggle.default;
Dropdown.Item = _DropdownItem.default;
var _default = exports.default = Dropdown;
}}),

};

//# sourceMappingURL=node_modules_%40restart_ui_cjs_975ec58c._.js.map