{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/Home/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/Home/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/Home/Marketplace.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/_var.scss\" as *;\r\n\r\n.marketplace {\r\n    padding: 5rem 0;\r\n\r\n    &_inner {\r\n        &_heading {\r\n            border-bottom: 3px solid rgba(0, 173, 239, 0.3);\r\n            margin-bottom: 30px;\r\n            padding-bottom: 1.25rem;\r\n\r\n\r\n            h4 {\r\n                margin-bottom: 1.25rem;\r\n                font-size: 1.65rem;\r\n                line-height: 35px;\r\n                font-weight: 600;\r\n            }\r\n\r\n            .breadcrumb {\r\n                margin: 0;\r\n            }\r\n        }\r\n    }\r\n\r\n    &_heading {\r\n        h1 {\r\n            font-size: 3rem;\r\n            font-weight: 800;\r\n\r\n            @media (max-width: 1199px) {\r\n                font-size: 2.5rem;\r\n            }\r\n\r\n            @media (max-width: 767px) {\r\n                font-size: 1.5rem;\r\n            }\r\n\r\n            @media (max-width: 390px) {\r\n                font-size: 1.30rem;\r\n            }\r\n        }\r\n    }\r\n\r\n    &_shopcart {\r\n        margin: 30px 0;\r\n\r\n        @media screen and (max-width: 767px) {\r\n            flex-wrap: wrap;\r\n        }\r\n\r\n        .education_search {\r\n            margin: 0 50px;\r\n            width: 450px;\r\n\r\n            @media screen and (max-width: 991px) {\r\n                width: 350px;\r\n                margin: 0 30px;\r\n            }\r\n\r\n            @media screen and (max-width: 767px) {\r\n                width: 100%;\r\n                margin: 0;\r\n                padding-top: 1.25rem;\r\n            }\r\n\r\n            .commonSearch {\r\n                max-width: 100%;\r\n                width: 100%;\r\n            }\r\n        }\r\n\r\n        &_btn {\r\n            button {\r\n                background-color: transparent;\r\n                border: 0;\r\n                font-weight: 600;\r\n                font-size: 1.25rem;\r\n                color: $white;\r\n                transition: all ease-in-out 0.3s;\r\n\r\n                svg {\r\n                    margin-right: 0.625rem;\r\n                }\r\n\r\n                &:hover {\r\n                    background-color: transparent;\r\n                    color: $baseclr;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .common_select {\r\n        margin-bottom: 0;\r\n\r\n        .select__control {\r\n            padding: 0;\r\n            border: 0;\r\n            min-width: 80px;\r\n            min-height: auto;\r\n        }\r\n    }\r\n\r\n    &_products {\r\n        &_sellerInfo {\r\n            margin: 20px 0;\r\n            border-top: 3px solid #00adef4d;\r\n\r\n            @media (width <=767px) {\r\n                border-top: none;\r\n            }\r\n\r\n            h4 {\r\n                margin: 20px 0;\r\n            }\r\n\r\n            .sellerProfile {\r\n                display: flex;\r\n                align-items: center;\r\n                gap: 10px;\r\n                margin-bottom: 20px;\r\n\r\n                img {\r\n                    min-height: 50px;\r\n                    max-height: 50px;\r\n                    min-width: 50px;\r\n                    max-width: 50px;\r\n                    border-radius: 50px;\r\n                }\r\n\r\n                p {\r\n                    font-size: 22px;\r\n                    font-weight: 600;\r\n                }\r\n            }\r\n\r\n            .sellerRating {\r\n                display: flex;\r\n                align-items: center;\r\n                gap: 5px;\r\n                margin-bottom: 20px;\r\n\r\n                img {\r\n                    height: 25px;\r\n                }\r\n\r\n                span {\r\n                    font-size: 24px;\r\n                    font-weight: 600;\r\n                }\r\n            }\r\n\r\n            .sellerBtn {\r\n                button {\r\n                    width: 100%;\r\n                    min-height: 50px;\r\n                }\r\n            }\r\n        }\r\n\r\n        &_filter {\r\n            .accordion {\r\n                border-radius: 0;\r\n\r\n                &-item {\r\n                    background-color: transparent;\r\n                    border: 0;\r\n                    border-bottom: 3px solid rgba(0, 173, 239, 0.2);\r\n                    border-radius: 0;\r\n                }\r\n\r\n                &-button {\r\n                    background-color: transparent;\r\n                    border: 0;\r\n                    color: $white;\r\n                    font-weight: 600;\r\n                    font-size: 1.25rem;\r\n                    padding: 1.5rem 0;\r\n                    text-transform: capitalize;\r\n                    border-radius: 0;\r\n\r\n                    @media screen and (max-width: 991px) {\r\n                        font-size: 1rem;\r\n                        padding: 1rem 0;\r\n                    }\r\n\r\n                    &:focus {\r\n                        box-shadow: none;\r\n                    }\r\n\r\n                    &:not(.collapsed) {\r\n                        box-shadow: none;\r\n                        background-color: transparent;\r\n                        color: $white;\r\n\r\n                        &::after {\r\n                            background-image: url(\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-minus.svg\");\r\n                            transform: none;\r\n                        }\r\n                    }\r\n\r\n                    &::after {\r\n                        background-image: url(\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-plus.svg\");\r\n                        transform: none;\r\n                        background-position: center;\r\n                    }\r\n                }\r\n\r\n                &-body {\r\n                    padding: 1rem 0 1.25rem;\r\n\r\n                    button {\r\n                        background-color: transparent;\r\n                        border: 0;\r\n                        display: block;\r\n                        color: $white;\r\n                        font-size: 16px;\r\n                        font-weight: 600;\r\n                        line-height: 26px;\r\n                        padding: 6px 0;\r\n                        width: 100%;\r\n                        transition: all ease-in-out 0.3s;\r\n                        text-align: left;\r\n\r\n                        &:hover {\r\n                            color: $baseclr;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        &_sort {\r\n            @media screen and (max-width: 991px) {\r\n                justify-content: flex-end;\r\n            }\r\n\r\n            h5 {\r\n                font-weight: 500;\r\n                margin-right: 5px;\r\n            }\r\n\r\n            .common_select {\r\n\r\n                .select__control {\r\n                    min-width: 150px;\r\n                }\r\n            }\r\n        }\r\n\r\n        &_card {\r\n            margin-top: 30px;\r\n\r\n            &_rating {\r\n                display: flex;\r\n                align-items: center;\r\n                gap: 5px;\r\n                margin-bottom: 20px;\r\n\r\n                img {\r\n                    height: 25px;\r\n                }\r\n\r\n                span {\r\n                    font-size: 24px;\r\n                    font-weight: 600;\r\n                    color: #fff;\r\n                }\r\n            }\r\n\r\n            &_content {\r\n                h4 {\r\n                    @media screen and (max-width: 991px) {\r\n                        font-size: 20px;\r\n                        line-height: 30px;\r\n                    }\r\n\r\n                    @media screen and (max-width: 767px) {\r\n                        font-size: 16px;\r\n                        line-height: 24px;\r\n                    }\r\n                }\r\n            }\r\n\r\n            &_img {\r\n                position: relative;\r\n\r\n                &::after {\r\n                    content: '';\r\n                    // position: absolute;\r\n                    top: 0;\r\n                    left: 0;\r\n                    width: 100%;\r\n                    height: 100%;\r\n                    padding-top: 90%;\r\n                    display: block;\r\n                }\r\n\r\n                img {\r\n                    width: 100%;\r\n                    height: 100%;\r\n                    object-fit: cover;\r\n                    position: absolute;\r\n                    border-radius: 20px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .custom_breadcrumb {\r\n        .home-item {\r\n            display: none;\r\n        }\r\n\r\n        .secondary_link {\r\n            padding: 0;\r\n\r\n            &::before {\r\n                display: none;\r\n            }\r\n        }\r\n    }\r\n\r\n\r\n    .marketplace_products_card_rating {\r\n        img {\r\n            height: 25px;\r\n            width: 25px;\r\n\r\n            @media screen and (max-width: 576px) {\r\n                height: 18px;\r\n                width: 18px;\r\n            }\r\n        }\r\n\r\n        span {\r\n            font-size: 16px;\r\n\r\n            @media screen and (max-width: 576px) {\r\n                font-size: 14px;\r\n            }\r\n        }\r\n    }\r\n\r\n\r\n    .megaMenu {\r\n        position: relative;\r\n        // z-index: 9999;\r\n\r\n        &__toggle {\r\n            padding: 0.5rem 1rem;\r\n            color: #fff;\r\n            font-family: 'Gilroy-SemiBold';\r\n            font-size: 18px;\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 0.5rem;\r\n            background: none;\r\n            border: none;\r\n        }\r\n\r\n        &__dropdown {\r\n            position: absolute;\r\n            display: flex;\r\n            top: 60px;\r\n            left: -50px;\r\n            width: 300px;\r\n            max-width: 1059px;\r\n            background: #fff;\r\n            border: 1px solid rgba(255, 255, 255, 0.3);\r\n            box-shadow: 0px 4px 10px rgba(255, 255, 255, 0.15);\r\n            border-radius: 15px;\r\n            margin-top: 0.5rem;\r\n            overflow: hidden;\r\n            transition: width 0.3s ease;\r\n            z-index: 9999;\r\n\r\n            &.expanded {\r\n                width: 1059px;\r\n            }\r\n        }\r\n\r\n        &__categories {\r\n            width: 300px;\r\n            padding: 0;\r\n            border-right: 1px solid #d9d9d9;\r\n        }\r\n\r\n        &__category {\r\n            margin: 0;\r\n            padding: 0;\r\n            width: 300px;\r\n            cursor: pointer;\r\n\r\n            &.active,\r\n            &:hover {\r\n                background-color: #e6e6e6 !important;\r\n            }\r\n        }\r\n\r\n        &__category-link {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            padding: 0.75rem 1rem;\r\n            text-decoration: none;\r\n            color: #000;\r\n            font-weight: 600;\r\n            font-size: 20px;\r\n            font-family: 'Gilroy';\r\n            width: 100%;\r\n            height: 100%;\r\n        }\r\n\r\n        &__icon {\r\n            width: 1rem;\r\n            height: 1rem;\r\n        }\r\n\r\n        &__subcategories {\r\n            padding: 1.5rem;\r\n            width: 100%;\r\n            opacity: 0;\r\n            transition: opacity 0.2s ease;\r\n            pointer-events: none;\r\n\r\n            &.visible {\r\n                opacity: 1;\r\n                pointer-events: auto;\r\n            }\r\n        }\r\n\r\n        &__columns {\r\n            display: grid;\r\n            grid-template-columns: repeat(2, 1fr);\r\n            gap: 2rem;\r\n        }\r\n\r\n        &__column {\r\n            min-width: 220px;\r\n        }\r\n\r\n        &__subtitle {\r\n            color: #000;\r\n            font-family: 'Gilroy';\r\n            font-weight: 700;\r\n            font-size: 16px;\r\n            margin-bottom: 0.5rem;\r\n        }\r\n\r\n        &__items {\r\n            list-style: none;\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n\r\n        &__link {\r\n            display: block;\r\n            padding: 0.3rem 0;\r\n            font-size: 16px;\r\n            color: #000000cc;\r\n            font-weight: 500;\r\n            white-space: normal; // <- allow wrapping\r\n            overflow-wrap: break-word; // optional for aggressive breaking\r\n\r\n            &:hover {\r\n                text-decoration: underline;\r\n            }\r\n        }\r\n\r\n        /* ✅ Tablet view: 728px to 1050px */\r\n        @media (max-width: 1050px) and (min-width: 728px) {\r\n            &__dropdown {\r\n                flex-direction: row !important;\r\n                position: absolute;\r\n                top: 60px;\r\n                left: -30px;\r\n                width: 247px;\r\n                max-width: 750px;\r\n                height: auto;\r\n                border-radius: 15px !important;\r\n                overflow: hidden;\r\n            }\r\n\r\n            &__categories {\r\n                width: 100% !important;\r\n                border-right: 1px solid #d9d9d9;\r\n            }\r\n\r\n            &__category {\r\n                width: 247px !important;\r\n            }\r\n\r\n            &__subcategories {\r\n\r\n                width: 100% !important;\r\n                padding: 1rem !important;\r\n                opacity: 1 !important;\r\n                pointer-events: auto !important;\r\n            }\r\n\r\n            &__columns {\r\n                grid-template-columns: repeat(2, 1fr);\r\n                gap: 1rem;\r\n            }\r\n        }\r\n\r\n\r\n        @media (max-width: 727px) {\r\n            &__dropdown {\r\n                flex-direction: column;\r\n                position: fixed;\r\n                top: 8%;\r\n                left: 0;\r\n                right: 0;\r\n                height: 92vh;\r\n                width: 100%;\r\n                background: #FFFFFFE5;\r\n                border-radius: 0;\r\n                backdrop-filter: blur(10px);\r\n                // overflow-y: auto;\r\n                overflow-y: auto;\r\n                max-height: 100vh;\r\n                -webkit-overflow-scrolling: touch;\r\n                z-index: 9999;\r\n            }\r\n\r\n            &__category {\r\n\r\n                width: 100%;\r\n\r\n            }\r\n\r\n\r\n            &__mobile-header {\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: space-between;\r\n                padding: 1rem;\r\n                border-bottom: 1px solid #ccc;\r\n                color: #000;\r\n                font-weight: 700;\r\n                font-size: 20px;\r\n                font-family: 'Gilroy';\r\n                //   background-color: #f8f8f8;\r\n            }\r\n\r\n            &__back,\r\n            &__close {\r\n                background: none;\r\n                border: none;\r\n                font-size: 22px;\r\n                font-weight: bold;\r\n                cursor: pointer;\r\n                margin-right: 8px;\r\n            }\r\n\r\n            &__logo {\r\n                color: #000;\r\n                font-weight: 700;\r\n                font-size: 20px;\r\n                font-family: 'Gilroy';\r\n                margin-left: 8px;\r\n            }\r\n\r\n            &__categories,\r\n            &__subcategories {\r\n                width: 100%;\r\n                padding: 1rem;\r\n            }\r\n\r\n            .megaMenu__link {\r\n                font-size: 15px;\r\n                padding-top: 8px !important;\r\n                padding-bottom: 8px !important;\r\n\r\n                white-space: normal !important; // ✅ allows line wrapping\r\n                word-break: break-word !important; // ✅ breaks long words\r\n                overflow-wrap: break-word !important; // ✅ breaks phrases or long lines\r\n                display: block !important; // ✅ necessary for width-based wrapping\r\n                width: 100% !important; // ✅ prevent overflow\r\n                line-height: 1.4; // ✅ optional: improve readability when wrapping\r\n            }\r\n\r\n            &__columns {\r\n                grid-template-columns: 1fr;\r\n                gap: 1rem;\r\n            }\r\n        }\r\n\r\n        @media (max-width: 388px) {\r\n            &__dropdown {\r\n                flex-direction: column;\r\n                position: fixed;\r\n                top: 11%;\r\n                left: 0;\r\n                right: 0;\r\n                height: 89vh;\r\n                width: 100%;\r\n                background: #FFFFFFE5;\r\n                border-radius: 0;\r\n                backdrop-filter: blur(10px);\r\n                overflow-y: auto;\r\n                max-height: 100vh;\r\n                -webkit-overflow-scrolling: touch;\r\n                z-index: 9999;\r\n            }\r\n\r\n            .megaMenu__column {\r\n                max-width: 310px !important;\r\n            }\r\n\r\n            .megaMenu__link {\r\n                max-width: 100% !important;\r\n            }\r\n\r\n        }\r\n\r\n    }\r\n\r\n\r\n\r\n\r\n}"], "names": [], "mappings": "AAGA;;;;AADA;;;;AAIQ;;;;;;AAMI;;;;;;;AAOA;;;;AAOJ;;;;;AAII;EAJJ;;;;;AAQI;EARJ;;;;;AAYI;EAZJ;;;;;AAkBJ;;;;AAGI;EAHJ;;;;;AAOI;;;;;AAII;EAJJ;;;;;;AASI;EATJ;;;;;;;AAeI;;;;;AAOA;;;;;;;;;AAQI;;;;AAIA;;;;;AAQZ;;;;AAGI;;;;;;;AASA;;;;;AAII;EAJJ;;;;;AAQI;;;;AAIA;;;;;;;AAMI;;;;;;;;AAQA;;;;;AAMJ;;;;;;;AAMI;;;;AAIA;;;;;AAOA;;;;;AAQJ;;;;AAGI;;;;;;;AAOA;;;;;;;;;;;AAUI;EAVJ;;;;;;AAeI;;;;AAIA;;;;;;AAKI;;;;;AAMJ;;;;;;AAOJ;;;;AAGI;;;;;;;;;;;;;;AAaI;;;;AASZ;EADJ;;;;;AAKI;;;;;AAOI;;;;AAMR;;;;AAGI;;;;;;;AAMI;;;;AAIA;;;;;;AASI;EADJ;;;;;;AAMI;EANJ;;;;;;AAaJ;;;;AAGI;;;;;;;;;;AAWA;;;;;;;;AAYR;;;;AAIA;;;;AAGI;;;;AAQJ;;;;;AAII;EAJJ;;;;;;AAUA;;;;AAGI;EAHJ;;;;;AAUJ;;;;AAII;;;;;;;;;;;;AAYA;;;;;;;;;;;;;;;;;AAgBI;;;;AAKJ;;;;;;AAMA;;;;;;;AAMI;;;;AAMJ;;;;;;;;;;;;;;AAcA;;;;;AAKA;;;;;;;;AAOI;;;;;AAMJ;;;;;;AAMA;;;;AAIA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;;;;AASI;;;;AAMJ;EACI;;;;;;;;;;;;EAYA;;;;;EAKA;;;;EAIA;;;;;;;EAQA;;;;;;AAOJ;EACI;;;;;;;;;;;;;;;;;;EAkBA;;;;EAOA;;;;;;;;;;;;EAaA;;;;;;;;;EAUA;;;;;;;;EAQA;;;;;EAMA;;;;;;;;;;;;EAaA;;;;;;AAMJ;EACI;;;;;;;;;;;;;;;;;;EAiBA;;;;EAIA"}}]}