/* [project]/src/css/Home/Marketplace.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

.marketplace {
  padding: 5rem 0;
}

.marketplace_inner_heading {
  border-bottom: 3px solid rgba(0, 173, 239, .3);
  margin-bottom: 30px;
  padding-bottom: 1.25rem;
}

.marketplace_inner_heading h4 {
  margin-bottom: 1.25rem;
  font-size: 1.65rem;
  font-weight: 600;
  line-height: 35px;
}

.marketplace_inner_heading .breadcrumb {
  margin: 0;
}

.marketplace_heading h1 {
  font-size: 3rem;
  font-weight: 800;
}

@media (max-width: 1199px) {
  .marketplace_heading h1 {
    font-size: 2.5rem;
  }
}

@media (max-width: 767px) {
  .marketplace_heading h1 {
    font-size: 1.5rem;
  }
}

@media (max-width: 390px) {
  .marketplace_heading h1 {
    font-size: 1.3rem;
  }
}

.marketplace_shopcart {
  margin: 30px 0;
}

@media screen and (max-width: 767px) {
  .marketplace_shopcart {
    flex-wrap: wrap;
  }
}

.marketplace_shopcart .education_search {
  width: 450px;
  margin: 0 50px;
}

@media screen and (max-width: 991px) {
  .marketplace_shopcart .education_search {
    width: 350px;
    margin: 0 30px;
  }
}

@media screen and (max-width: 767px) {
  .marketplace_shopcart .education_search {
    width: 100%;
    margin: 0;
    padding-top: 1.25rem;
  }
}

.marketplace_shopcart .education_search .commonSearch {
  width: 100%;
  max-width: 100%;
}

.marketplace_shopcart_btn button {
  color: #fff;
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  font-size: 1.25rem;
  font-weight: 600;
  transition: all .3s ease-in-out;
}

.marketplace_shopcart_btn button svg {
  margin-right: .625rem;
}

.marketplace_shopcart_btn button:hover {
  color: #00adef;
  background-color: rgba(0, 0, 0, 0);
}

.marketplace .common_select {
  margin-bottom: 0;
}

.marketplace .common_select .select__control {
  border: 0;
  min-width: 80px;
  min-height: auto;
  padding: 0;
}

.marketplace_products_sellerInfo {
  border-top: 3px solid rgba(0, 173, 239, .3);
  margin: 20px 0;
}

@media (max-width: 767px) {
  .marketplace_products_sellerInfo {
    border-top: none;
  }
}

.marketplace_products_sellerInfo h4 {
  margin: 20px 0;
}

.marketplace_products_sellerInfo .sellerProfile {
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
  display: flex;
}

.marketplace_products_sellerInfo .sellerProfile img {
  border-radius: 50px;
  min-width: 50px;
  max-width: 50px;
  min-height: 50px;
  max-height: 50px;
}

.marketplace_products_sellerInfo .sellerProfile p {
  font-size: 22px;
  font-weight: 600;
}

.marketplace_products_sellerInfo .sellerRating {
  align-items: center;
  gap: 5px;
  margin-bottom: 20px;
  display: flex;
}

.marketplace_products_sellerInfo .sellerRating img {
  height: 25px;
}

.marketplace_products_sellerInfo .sellerRating span {
  font-size: 24px;
  font-weight: 600;
}

.marketplace_products_sellerInfo .sellerBtn button {
  width: 100%;
  min-height: 50px;
}

.marketplace_products_filter .accordion {
  border-radius: 0;
}

.marketplace_products_filter .accordion-item {
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  border-bottom: 3px solid rgba(0, 173, 239, .2);
  border-radius: 0;
}

.marketplace_products_filter .accordion-button {
  color: #fff;
  text-transform: capitalize;
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  border-radius: 0;
  padding: 1.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
}

@media screen and (max-width: 991px) {
  .marketplace_products_filter .accordion-button {
    padding: 1rem 0;
    font-size: 1rem;
  }
}

.marketplace_products_filter .accordion-button:focus {
  box-shadow: none;
}

.marketplace_products_filter .accordion-button:not(.collapsed) {
  box-shadow: none;
  color: #fff;
  background-color: rgba(0, 0, 0, 0);
}

.marketplace_products_filter .accordion-button:not(.collapsed):after {
  background-image: url("https://cdn.tradereply.com/dev/site-assets/icons/tradereply-minus.svg");
  transform: none;
}

.marketplace_products_filter .accordion-button:after {
  background-image: url("https://cdn.tradereply.com/dev/site-assets/icons/tradereply-plus.svg");
  background-position: center;
  transform: none;
}

.marketplace_products_filter .accordion-body {
  padding: 1rem 0 1.25rem;
}

.marketplace_products_filter .accordion-body button {
  color: #fff;
  text-align: left;
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  width: 100%;
  padding: 6px 0;
  font-size: 16px;
  font-weight: 600;
  line-height: 26px;
  transition: all .3s ease-in-out;
  display: block;
}

.marketplace_products_filter .accordion-body button:hover {
  color: #00adef;
}

@media screen and (max-width: 991px) {
  .marketplace_products_sort {
    justify-content: flex-end;
  }
}

.marketplace_products_sort h5 {
  margin-right: 5px;
  font-weight: 500;
}

.marketplace_products_sort .common_select .select__control {
  min-width: 150px;
}

.marketplace_products_card {
  margin-top: 30px;
}

.marketplace_products_card_rating {
  align-items: center;
  gap: 5px;
  margin-bottom: 20px;
  display: flex;
}

.marketplace_products_card_rating img {
  height: 25px;
}

.marketplace_products_card_rating span {
  color: #fff;
  font-size: 24px;
  font-weight: 600;
}

@media screen and (max-width: 991px) {
  .marketplace_products_card_content h4 {
    font-size: 20px;
    line-height: 30px;
  }
}

@media screen and (max-width: 767px) {
  .marketplace_products_card_content h4 {
    font-size: 16px;
    line-height: 24px;
  }
}

.marketplace_products_card_img {
  position: relative;
}

.marketplace_products_card_img:after {
  content: "";
  width: 100%;
  height: 100%;
  padding-top: 90%;
  display: block;
  top: 0;
  left: 0;
}

.marketplace_products_card_img img {
  object-fit: cover;
  border-radius: 20px;
  width: 100%;
  height: 100%;
  position: absolute;
}

.marketplace .custom_breadcrumb .home-item {
  display: none;
}

.marketplace .custom_breadcrumb .secondary_link {
  padding: 0;
}

.marketplace .custom_breadcrumb .secondary_link:before {
  display: none;
}

.marketplace .marketplace_products_card_rating img {
  width: 25px;
  height: 25px;
}

@media screen and (max-width: 576px) {
  .marketplace .marketplace_products_card_rating img {
    width: 18px;
    height: 18px;
  }
}

.marketplace .marketplace_products_card_rating span {
  font-size: 16px;
}

@media screen and (max-width: 576px) {
  .marketplace .marketplace_products_card_rating span {
    font-size: 14px;
  }
}

.marketplace .megaMenu {
  position: relative;
}

.marketplace .megaMenu__toggle {
  color: #fff;
  background: none;
  border: none;
  align-items: center;
  gap: .5rem;
  padding: .5rem 1rem;
  font-family: Gilroy-SemiBold;
  font-size: 18px;
  display: flex;
}

.marketplace .megaMenu__dropdown {
  z-index: 9999;
  background: #fff;
  border: 1px solid rgba(255, 255, 255, .3);
  border-radius: 15px;
  width: 300px;
  max-width: 1059px;
  margin-top: .5rem;
  transition: width .3s;
  display: flex;
  position: absolute;
  top: 60px;
  left: -50px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(255, 255, 255, .15);
}

.marketplace .megaMenu__dropdown.expanded {
  width: 1059px;
}

.marketplace .megaMenu__categories {
  border-right: 1px solid #d9d9d9;
  width: 300px;
  padding: 0;
}

.marketplace .megaMenu__category {
  cursor: pointer;
  width: 300px;
  margin: 0;
  padding: 0;
}

.marketplace .megaMenu__category.active, .marketplace .megaMenu__category:hover {
  background-color: #e6e6e6 !important;
}

.marketplace .megaMenu__category-link {
  color: #000;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: .75rem 1rem;
  font-family: Gilroy;
  font-size: 20px;
  font-weight: 600;
  text-decoration: none;
  display: flex;
}

.marketplace .megaMenu__icon {
  width: 1rem;
  height: 1rem;
}

.marketplace .megaMenu__subcategories {
  opacity: 0;
  pointer-events: none;
  width: 100%;
  padding: 1.5rem;
  transition: opacity .2s;
}

.marketplace .megaMenu__subcategories.visible {
  opacity: 1;
  pointer-events: auto;
}

.marketplace .megaMenu__columns {
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  display: grid;
}

.marketplace .megaMenu__column {
  min-width: 220px;
}

.marketplace .megaMenu__subtitle {
  color: #000;
  margin-bottom: .5rem;
  font-family: Gilroy;
  font-size: 16px;
  font-weight: 700;
}

.marketplace .megaMenu__items {
  margin: 0;
  padding: 0;
  list-style: none;
}

.marketplace .megaMenu__link {
  color: rgba(0, 0, 0, .8);
  white-space: normal;
  overflow-wrap: break-word;
  padding: .3rem 0;
  font-size: 16px;
  font-weight: 500;
  display: block;
}

.marketplace .megaMenu__link:hover {
  text-decoration: underline;
}

@media (max-width: 1050px) and (min-width: 728px) {
  .marketplace .megaMenu__dropdown {
    width: 247px;
    max-width: 750px;
    height: auto;
    position: absolute;
    top: 60px;
    left: -30px;
    overflow: hidden;
    border-radius: 15px !important;
    flex-direction: row !important;
  }

  .marketplace .megaMenu__categories {
    border-right: 1px solid #d9d9d9;
    width: 100% !important;
  }

  .marketplace .megaMenu__category {
    width: 247px !important;
  }

  .marketplace .megaMenu__subcategories {
    opacity: 1 !important;
    pointer-events: auto !important;
    width: 100% !important;
    padding: 1rem !important;
  }

  .marketplace .megaMenu__columns {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}

@media (max-width: 727px) {
  .marketplace .megaMenu__dropdown {
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    -webkit-overflow-scrolling: touch;
    z-index: 9999;
    background: rgba(255, 255, 255, .898);
    border-radius: 0;
    flex-direction: column;
    width: 100%;
    height: 92vh;
    max-height: 100vh;
    position: fixed;
    top: 8%;
    left: 0;
    right: 0;
    overflow-y: auto;
  }

  .marketplace .megaMenu__category {
    width: 100%;
  }

  .marketplace .megaMenu__mobile-header {
    color: #000;
    border-bottom: 1px solid #ccc;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    font-family: Gilroy;
    font-size: 20px;
    font-weight: 700;
    display: flex;
  }

  .marketplace .megaMenu__back, .marketplace .megaMenu__close {
    cursor: pointer;
    background: none;
    border: none;
    margin-right: 8px;
    font-size: 22px;
    font-weight: bold;
  }

  .marketplace .megaMenu__logo {
    color: #000;
    margin-left: 8px;
    font-family: Gilroy;
    font-size: 20px;
    font-weight: 700;
  }

  .marketplace .megaMenu__categories, .marketplace .megaMenu__subcategories {
    width: 100%;
    padding: 1rem;
  }

  .marketplace .megaMenu .megaMenu__link {
    font-size: 15px;
    line-height: 1.4;
    white-space: normal !important;
    word-break: break-word !important;
    overflow-wrap: break-word !important;
    width: 100% !important;
    padding-top: 8px !important;
    padding-bottom: 8px !important;
    display: block !important;
  }

  .marketplace .megaMenu__columns {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 388px) {
  .marketplace .megaMenu__dropdown {
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    -webkit-overflow-scrolling: touch;
    z-index: 9999;
    background: rgba(255, 255, 255, .898);
    border-radius: 0;
    flex-direction: column;
    width: 100%;
    height: 89vh;
    max-height: 100vh;
    position: fixed;
    top: 11%;
    left: 0;
    right: 0;
    overflow-y: auto;
  }

  .marketplace .megaMenu .megaMenu__column {
    max-width: 310px !important;
  }

  .marketplace .megaMenu .megaMenu__link {
    max-width: 100% !important;
  }
}

/*# sourceMappingURL=src_css_Home_Marketplace_scss_css_e59ae46c._.single.css.map*/