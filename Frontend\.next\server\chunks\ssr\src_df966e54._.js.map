{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/assets/svgIcons/SvgIcon.js"], "sourcesContent": ["export const EyeIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"12\"\r\n      viewBox=\"0 0 18 12\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M9 9.70508C9.9375 9.70508 10.7345 9.37708 11.391 8.72108C12.047 8.06458 12.375 7.26758 12.375 6.33008C12.375 5.39258 12.047 4.59558 11.391 3.93908C10.7345 3.28308 9.9375 2.95508 9 2.95508C8.0625 2.95508 7.2655 3.28308 6.609 3.93908C5.953 4.59558 5.625 5.39258 5.625 6.33008C5.625 7.26758 5.953 8.06458 6.609 8.72108C7.2655 9.37708 8.0625 9.70508 9 9.70508ZM9 8.35508C8.4375 8.35508 7.9595 8.15808 7.566 7.76408C7.172 7.37058 6.975 6.89258 6.975 6.33008C6.975 5.76758 7.172 5.28933 7.566 4.89533C7.9595 4.50183 8.4375 4.30508 9 4.30508C9.5625 4.30508 10.0408 4.50183 10.4347 4.89533C10.8282 5.28933 11.025 5.76758 11.025 6.33008C11.025 6.89258 10.8282 7.37058 10.4347 7.76408C10.0408 8.15808 9.5625 8.35508 9 8.35508ZM9 11.9551C7.175 11.9551 5.5125 11.4456 4.0125 10.4266C2.5125 9.40808 1.425 8.04258 0.75 6.33008C1.425 4.61758 2.5125 3.25183 4.0125 2.23283C5.5125 1.21433 7.175 0.705078 9 0.705078C10.825 0.705078 12.4875 1.21433 13.9875 2.23283C15.4875 3.25183 16.575 4.61758 17.25 6.33008C16.575 8.04258 15.4875 9.40808 13.9875 10.4266C12.4875 11.4456 10.825 11.9551 9 11.9551ZM9 10.4551C10.4125 10.4551 11.7095 10.0831 12.891 9.33908C14.072 8.59558 14.975 7.59258 15.6 6.33008C14.975 5.06758 14.072 4.06433 12.891 3.32033C11.7095 2.57683 10.4125 2.20508 9 2.20508C7.5875 2.20508 6.2905 2.57683 5.109 3.32033C3.928 4.06433 3.025 5.06758 2.4 6.33008C3.025 7.59258 3.928 8.59558 5.109 9.33908C6.2905 10.0831 7.5875 10.4551 9 10.4551Z\"\r\n        fill=\"#101014\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const CloseEye = () => (\r\n  <svg\r\n    xmlns=\"http://www.w3.org/2000/svg\"\r\n    width=\"22.016\"\r\n    height=\"17.613\"\r\n    viewBox=\"0 0 22.016 17.613\"\r\n  >\r\n    <path\r\n      id=\"Icon_awesome-eye-slash\"\r\n      data-name=\"Icon awesome-eye-slash\"\r\n      d=\"M11.008,13.76A4.935,4.935,0,0,1,6.092,9.181L2.484,6.392A11.465,11.465,0,0,0,1.221,8.3a1.113,1.113,0,0,0,0,1,11.033,11.033,0,0,0,9.787,6.1,10.685,10.685,0,0,0,2.679-.36L11.9,13.67a4.958,4.958,0,0,1-.894.09Zm10.8,2L18,12.819A11.4,11.4,0,0,0,20.8,9.308a1.113,1.113,0,0,0,0-1,11.033,11.033,0,0,0-9.787-6.1A10.6,10.6,0,0,0,5.94,3.5L1.564.116a.55.55,0,0,0-.773.1l-.675.869a.55.55,0,0,0,.1.772L20.452,17.5a.55.55,0,0,0,.773-.1l.676-.869a.55.55,0,0,0-.1-.772Zm-6.32-4.885L14.131,9.829a3.26,3.26,0,0,0-3.994-4.194,1.639,1.639,0,0,1,.32.97,1.6,1.6,0,0,1-.053.344L7.872,4.992a4.9,4.9,0,0,1,3.136-1.139,4.951,4.951,0,0,1,4.954,4.954,4.836,4.836,0,0,1-.478,2.068Z\"\r\n      transform=\"translate(0 0)\"\r\n      fill=\"#fff\"\r\n    />\r\n  </svg>\r\n);\r\nexport const ThemeIcon = () => {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      width=\"32\"\r\n      height=\"32\"\r\n      viewBox=\"0 0 32 32\"\r\n    >\r\n      <g\r\n        id=\"Group_175598\"\r\n        data-name=\"Group 175598\"\r\n        transform=\"translate(-1847 -26)\"\r\n      >\r\n        <g\r\n          id=\"Group_175597\"\r\n          data-name=\"Group 175597\"\r\n          transform=\"translate(1842.007 28.533)\"\r\n        >\r\n          <rect\r\n            id=\"Rectangle_13566\"\r\n            data-name=\"Rectangle 13566\"\r\n            width=\"32\"\r\n            height=\"32\"\r\n            rx=\"16\"\r\n            transform=\"translate(4.993 -2.533)\"\r\n            fill=\"#f45126\"\r\n          />\r\n          <g\r\n            id=\"Group_175601\"\r\n            data-name=\"Group 175601\"\r\n            transform=\"translate(6.923 -0.604)\"\r\n          >\r\n            <path\r\n              id=\"Path_113806\"\r\n              data-name=\"Path 113806\"\r\n              d=\"M41.464,28.649a4.427,4.427,0,0,1-.409.578.185.185,0,1,0,.283.237,4.839,4.839,0,0,0,.444-.625.185.185,0,0,0-.317-.189Zm.521-1.312a4.5,4.5,0,0,1-.208.677.185.185,0,0,0,.343.136,4.837,4.837,0,0,0,.225-.733.184.184,0,1,0-.36-.08Zm.086-1.409a4.537,4.537,0,0,1,.012.708.184.184,0,1,0,.368.023,4.831,4.831,0,0,0-.013-.766.185.185,0,1,0-.367.035Zm-.355-1.366a4.469,4.469,0,0,1,.231.669.185.185,0,0,0,.357-.093,4.833,4.833,0,0,0-.251-.724.184.184,0,1,0-.338.148Zm-.764-1.187a4.508,4.508,0,0,1,.43.563.185.185,0,1,0,.31-.2,4.786,4.786,0,0,0-.465-.609.185.185,0,1,0-.275.246Z\"\r\n              transform=\"translate(-23.867 -12.612)\"\r\n              fill=\"#fff\"\r\n              fillRule=\"evenodd\"\r\n            />\r\n            <path\r\n              id=\"Path_113807\"\r\n              data-name=\"Path 113807\"\r\n              d=\"M13.748,7.475a6.273,6.273,0,1,0,6.273,6.273A6.276,6.276,0,0,0,13.748,7.475Zm.369.75a5.535,5.535,0,0,1,0,11.046ZM12.7,4.095v1.7a1.048,1.048,0,0,0,2.1,0v-1.7a1.048,1.048,0,0,0-2.1,0Zm7.133,2.087-1.2,1.2a1.048,1.048,0,0,0,1.482,1.482l1.2-1.2a1.048,1.048,0,1,0-1.482-1.482ZM23.4,12.7h-1.7a1.048,1.048,0,0,0,0,2.1h1.7a1.048,1.048,0,0,0,0-2.1Zm-2.087,7.133-1.2-1.2a1.048,1.048,0,0,0-1.482,1.482l1.2,1.2a1.048,1.048,0,1,0,1.482-1.482ZM14.8,23.4v-1.7a1.048,1.048,0,0,0-2.1,0v1.7a1.048,1.048,0,0,0,2.1,0ZM7.663,21.315l1.2-1.2a1.048,1.048,0,0,0-1.482-1.482l-1.2,1.2a1.048,1.048,0,1,0,1.482,1.482ZM4.095,14.8h1.7a1.048,1.048,0,0,0,0-2.1h-1.7a1.048,1.048,0,0,0,0,2.1ZM6.181,7.663l1.2,1.2A1.048,1.048,0,0,0,8.863,7.381l-1.2-1.2A1.048,1.048,0,0,0,6.181,7.663Z\"\r\n              fill=\"#fff\"\r\n              fillRule=\"evenodd\"\r\n            />\r\n          </g>\r\n        </g>\r\n      </g>\r\n    </svg>\r\n  );\r\n};\r\nexport const GlobalIcons = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-black-global.svg\" alt=\"Global Icons\" />\r\n  );\r\n};\r\nexport const GlobalBlueIcons = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-blue-global.svg\" alt=\"Global Blue Icons\" />\r\n  );\r\n};\r\nexport const UserBlackIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-user-black.svg\" alt=\"User Black Icon\" />\r\n  );\r\n};\r\nexport const UserBluekIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-user-brand-blue.svg\" alt=\"User Blue Icon\" />\r\n  );\r\n};\r\nexport const UserSolidBlueIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-user-account.svg\" alt=\"User Solid Blue Icon\" />\r\n  );\r\n};\r\nexport const SearchIcons = ({ width = 18, height = 18 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-search.svg\" width={width} height={height} alt=\"Search Icon\" />\r\n  );\r\n};\r\nexport const RoketIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-rocket.svg\" alt=\"Rocket Icon\" />\r\n  );\r\n};\r\nexport const FilterIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-filter.svg\" alt=\"Filter Icon\" />\r\n  );\r\n};\r\nexport const DashboardIcon = ({ color }) => {\r\n  return (\r\n    <img\r\n      src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradreply-dashboard.svg\"\r\n      alt=\"Dashboard Icon\"\r\n      className={color}\r\n    />\r\n  );\r\n};\r\nexport const DynamicIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-dynamic.svg\" alt=\"Dynamic Icon\" />\r\n  );\r\n};\r\nexport const KpiIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-kpi.svg\" alt=\"KPI Icon\" />\r\n  );\r\n};\r\nexport const GraphsIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradreply-graphs.svg\" alt=\"Graph Icon\" />\r\n  );\r\n};\r\nexport const ChartIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-charts.svg\" alt=\"Chart Icon\" />\r\n  );\r\n};\r\nexport const TrendIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-trend.svg\" alt=\"Trend Icon\" />\r\n  );\r\n};\r\nexport const RealTimeIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-real-time.svg\" alt=\"Real Time Icon\" />\r\n  );\r\n};\r\nexport const BrushIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-customize.svg\" alt=\"Brush Icon\" />\r\n  );\r\n};\r\nexport const LearningIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-learning.svg\" alt=\"Learning Icon\" />\r\n  );\r\n};\r\nexport const NextArrowIcon = () => {\r\n  return (\r\n    // <svg\r\n    //   width=\"26\"\r\n    //   height=\"22\"\r\n    //   viewBox=\"0 0 26 22\"\r\n    //   fill=\"none\"\r\n    //   xmlns=\"http://www.w3.org/2000/svg\"\r\n    // >\r\n    //   <path\r\n    //     d=\"M13.9387 21.1081C13.7989 20.9687 13.6879 20.8031 13.6122 20.6208C13.5365 20.4385 13.4975 20.243 13.4975 20.0456C13.4975 19.8481 13.5365 19.6527 13.6122 19.4703C13.6879 19.288 13.7989 19.1224 13.9387 18.9831L20.3749 12.5468L1.99995 12.5468C1.60212 12.5468 1.2206 12.3888 0.93929 12.1075C0.657985 11.8262 0.49995 11.4446 0.49995 11.0468C0.49995 10.649 0.657985 10.2675 0.939291 9.98616C1.2206 9.70485 1.60212 9.54682 1.99995 9.54682L20.3749 9.54682L13.9387 3.10807C13.6569 2.82628 13.4986 2.44409 13.4986 2.04557C13.4986 1.64706 13.6569 1.26486 13.9387 0.98307C14.2205 0.701278 14.6027 0.542968 15.0012 0.542968C15.3997 0.542968 15.7819 0.701278 16.0637 0.98307L25.0637 9.98307C25.2035 10.1224 25.3145 10.288 25.3902 10.4703C25.4659 10.6527 25.5049 10.8481 25.5049 11.0456C25.5049 11.243 25.4659 11.4385 25.3902 11.6208C25.3145 11.8031 25.2035 11.9687 25.0637 12.1081L16.0637 21.1081C15.9243 21.2479 15.7588 21.3589 15.5764 21.4346C15.3941 21.5103 15.1986 21.5493 15.0012 21.5493C14.8038 21.5493 14.6083 21.5103 14.426 21.4346C14.2436 21.3589 14.0781 21.2479 13.9387 21.1081Z\"\r\n    //     fill=\"white\"\r\n    //   />\r\n    // </svg>\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-next-arrow.svg\" alt=\"Next Arrow Icon\" />\r\n  );\r\n};\r\nexport const PrevIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-prev-arrow.svg\" alt=\"Prev Icon\" />\r\n  );\r\n};\r\nexport const QuoteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-quote.svg\" alt=\"Quote Icon\"\r\n      width=\"31\"\r\n      height=\"26\"\r\n    />\r\n  );\r\n};\r\nexport const CheckIcon = ({ height = 28, width = 28 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-success.svg\" alt=\"Check Icon\"\r\n      width={width}\r\n      height={height}\r\n    />\r\n  );\r\n};\r\nexport const RedCrossIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-red-x.svg\" alt=\"Red Cross Icon\" />\r\n  );\r\n};\r\nexport const PlusIcon = ({ color, height = 32, width = 33 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-plus.svg\" alt=\"Plus Icon\"\r\n      width={width}\r\n      height={height}\r\n      className={color}\r\n    />\r\n  );\r\n};\r\nexport const MinusIcon = ({ width = '29px', height = '4px' }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-minus.svg\" height={height} width={width} alt=\"Minus Icon\" />\r\n  );\r\n};\r\nexport const RedInfoStarIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-red-warning-star.svg\" alt=\"Red Info Star Icon\" />\r\n  );\r\n};\r\nexport const GreenCheckStarIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-green-check-star.svg\" alt=\"Green Check Star\"\r\n      width=\"42\"\r\n      height=\"42\"\r\n    />\r\n  );\r\n};\r\nexport const NoteSettingBlueIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-note-setting.svg\" alt=\"Note Setting Blue Icon\" />\r\n  );\r\n};\r\nexport const SettingIcon = () => {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      width=\"38\"\r\n      height=\"40\"\r\n      viewBox=\"0 0 38 40\"\r\n      fill=\"none\">\r\n      <path\r\n        d=\"M19.0264 0.5C20.4944 0.516 21.9564 0.686 23.3904 1.006C23.6955 1.0741 23.9716 1.23581 24.1803 1.46852C24.389 1.70124 24.5198 1.99334 24.5544 2.304L24.8944 5.358C24.9424 5.78857 25.0908 6.20186 25.3277 6.56461C25.5645 6.92737 25.8832 7.22946 26.2581 7.44658C26.633 7.6637 27.0537 7.78979 27.4862 7.8147C27.9187 7.83961 28.351 7.76264 28.7484 7.59L31.5484 6.36C31.8329 6.2347 32.1493 6.20088 32.4538 6.26323C32.7584 6.32557 33.036 6.48099 33.2484 6.708C35.2725 8.87031 36.7803 11.4633 37.6584 14.292C37.7501 14.5895 37.7471 14.9081 37.6496 15.2038C37.5521 15.4994 37.3651 15.7574 37.1144 15.942L34.6324 17.774C34.2827 18.0303 33.9983 18.3655 33.8023 18.7522C33.6063 19.1389 33.5041 19.5664 33.5041 20C33.5041 20.4336 33.6063 20.8611 33.8023 21.2478C33.9983 21.6345 34.2827 21.9697 34.6324 22.226L37.1184 24.056C37.3695 24.2407 37.5568 24.499 37.6543 24.7951C37.7518 25.0912 37.7546 25.4102 37.6624 25.708C36.7849 28.5366 35.2778 31.1295 33.2544 33.292C33.0424 33.5189 32.7652 33.6745 32.4611 33.7372C32.1569 33.7999 31.8408 33.7666 31.5564 33.642L28.7444 32.408C28.3476 32.234 27.9154 32.1559 27.4827 32.18C27.0501 32.204 26.6292 32.3296 26.2542 32.5466C25.8791 32.7635 25.5604 33.0657 25.3238 33.4287C25.0872 33.7917 24.9394 34.2053 24.8924 34.636L24.5524 37.688C24.5184 37.995 24.3905 38.2841 24.1861 38.5157C23.9817 38.7473 23.7108 38.9101 23.4104 38.982C20.5136 39.6726 17.4951 39.6726 14.5984 38.982C14.2976 38.9105 14.0262 38.7478 13.8214 38.5162C13.6167 38.2845 13.4885 37.9953 13.4544 37.688L13.1164 34.64C13.0673 34.2106 12.9182 33.7987 12.6811 33.4374C12.4439 33.0761 12.1254 32.7754 11.751 32.5595C11.3766 32.3437 10.9568 32.2186 10.5253 32.1943C10.0938 32.1701 9.6626 32.2474 9.26638 32.42L6.45438 33.652C6.1697 33.7771 5.85318 33.8106 5.54862 33.7479C5.24406 33.6852 4.96652 33.5293 4.75438 33.302C2.73064 31.137 1.22419 28.5412 0.348384 25.71C0.256177 25.4122 0.259017 25.0932 0.35651 24.7971C0.454002 24.501 0.641304 24.2427 0.892384 24.058L3.37838 22.226C3.72808 21.9697 4.01246 21.6345 4.20848 21.2478C4.40451 20.8611 4.50666 20.4336 4.50666 20C4.50666 19.5664 4.40451 19.1389 4.20848 18.7522C4.01246 18.3655 3.72808 18.0303 3.37838 17.774L0.892384 15.946C0.641304 15.7613 0.454002 15.503 0.35651 15.2069C0.259017 14.9108 0.256177 14.5918 0.348384 14.294C1.22647 11.4653 2.73423 8.87231 4.75838 6.71C4.97075 6.48299 5.24841 6.32757 5.55296 6.26523C5.8575 6.20288 6.17389 6.2367 6.45838 6.362L9.25838 7.592C9.65642 7.76454 10.0894 7.84132 10.5225 7.81617C10.9556 7.79102 11.3767 7.66465 11.7521 7.44719C12.1275 7.22974 12.4467 6.92727 12.684 6.56408C12.9212 6.2009 13.07 5.78712 13.1184 5.356L13.4584 2.304C13.4927 1.99271 13.6236 1.69997 13.8327 1.46683C14.0418 1.23369 14.3186 1.07185 14.6244 1.004C16.0564 0.686667 17.5237 0.518667 19.0264 0.5ZM19.0024 14C17.4111 14 15.885 14.6321 14.7597 15.7574C13.6345 16.8826 13.0024 18.4087 13.0024 20C13.0024 21.5913 13.6345 23.1174 14.7597 24.2426C15.885 25.3679 17.4111 26 19.0024 26C20.5937 26 22.1198 25.3679 23.245 24.2426C24.3702 23.1174 25.0024 21.5913 25.0024 20C25.0024 18.4087 24.3702 16.8826 23.245 15.7574C22.1198 14.6321 20.5937 14 19.0024 14Z\"\r\n        fill=\"#FEA500\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const SolidSettingIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-yellow-wrench.svg\" alt=\"Solid Setting Icon\" />\r\n  );\r\n};\r\nexport const RightSolidArrowIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-right-solid-arrow.svg\" alt=\"\" />\r\n  );\r\n};\r\nexport const SolidInfoIcon = ({ width = '20px', height = '20px' }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-info-marker.svg\" height={height} width={width} alt=\"Info Marker Icon\" />\r\n  );\r\n};\r\nexport const WhiteInfoIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-info-white.svg\" alt=\"White Info Marker Icon\" />\r\n  );\r\n};\r\nexport const RightArrowIcon = ({ color }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-right-arrow.svg\" alt=\"Right Arrow Icon\"\r\n      className={color}\r\n    />\r\n  );\r\n};\r\nexport const CartIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"25\"\r\n      height=\"20\"\r\n      viewBox=\"0 0 25 20\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M20.8333 16C19.3533 16 18.1667 16.89 18.1667 18C18.1667 18.5304 18.4476 19.0391 18.9477 19.4142C19.4478 19.7893 20.1261 20 20.8333 20C21.5406 20 22.2189 19.7893 22.719 19.4142C23.219 19.0391 23.5 18.5304 23.5 18C23.5 17.4696 23.219 16.9609 22.719 16.5858C22.2189 16.2107 21.5406 16 20.8333 16ZM-0.5 0V2H2.16667L6.96667 9.59L5.15333 12.04C4.95333 12.32 4.83333 12.65 4.83333 13C4.83333 13.5304 5.11428 14.0391 5.61438 14.4142C6.11448 14.7893 6.79276 15 7.5 15H23.5V13H8.06C7.97159 13 7.88681 12.9737 7.8243 12.9268C7.76178 12.8799 7.72667 12.8163 7.72667 12.75C7.72667 12.7 7.74 12.66 7.76667 12.63L8.96667 11H18.9C19.9 11 20.78 10.58 21.2333 9.97L26.0067 3.5C26.1 3.34 26.1667 3.17 26.1667 3C26.1667 2.73478 26.0262 2.48043 25.7761 2.29289C25.5261 2.10536 25.187 2 24.8333 2H5.11333L3.86 0M7.5 16C6.02 16 4.83333 16.89 4.83333 18C4.83333 18.5304 5.11428 19.0391 5.61438 19.4142C6.11448 19.7893 6.79276 20 7.5 20C8.20724 20 8.88552 19.7893 9.38562 19.4142C9.88571 19.0391 10.1667 18.5304 10.1667 18C10.1667 17.4696 9.88571 16.9609 9.38562 16.5858C8.88552 16.2107 8.20724 16 7.5 16Z\"\r\n        fill=\"white\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const SignoutIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-signout.svg\" alt=\"Signout Icon\" />\r\n  );\r\n};\r\nexport const HelpIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradreply-helpicon.svg\" alt=\"Help Icon\" />\r\n  );\r\n};\r\nexport const BaseEyeIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-eye.svg\" alt=\"Base Eye Icon\" />\r\n  );\r\n};\r\nexport const UserBlueIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-user-blue.svg\" alt=\"User Blue Icon\" />\r\n  );\r\n};\r\nexport const DollerIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-renew-dollar.svg\" alt=\"Dollar Icon\" />\r\n  );\r\n};\r\nexport const SecurityIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-security.svg\" alt=\"Security Icon\" />\r\n  );\r\n};\r\nexport const LockIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-lock.svg\" alt=\"Lock Icon\" />\r\n  );\r\n};\r\nexport const LinkIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-links.svg\" alt=\"Link Icon\" />\r\n  );\r\n};\r\nexport const PaymentIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-payment.svg\" alt=\"Payment Icon\" />\r\n  );\r\n};\r\nexport const PaymentIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"20\"\r\n      height=\"17\"\r\n      viewBox=\"0 0 20 17\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M18 0.5H2C0.89 0.5 0.00999999 1.39 0.00999999 2.5L0 14.5C0 15.61 0.89 16.5 2 16.5H18C19.11 16.5 20 15.61 20 14.5V2.5C20 1.39 19.11 0.5 18 0.5ZM18 14.5H2V8.5H18V14.5ZM18 4.5H2V2.5H18V4.5Z\"\r\n        fill=\"#00ADEF\">\r\n      </path>\r\n    </svg>\r\n  );\r\n};\r\nexport const CartSideIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-carticon.svg\" alt=\"Cart Icon\" />\r\n  );\r\n};\r\nexport const EditIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-editcon.svg\" alt=\"Edit Icon\" />\r\n  );\r\n};\r\n\r\nexport const CrossIcon = ({ color }) => {\r\n  return (\r\n    // <svg\r\n    //   width=\"18\"\r\n    //   height=\"19\"\r\n    //   viewBox=\"0 0 18 19\"\r\n    //   fill=\"none\"\r\n    //   xmlns=\"http://www.w3.org/2000/svg\"\r\n    // >\r\n    //   <path\r\n    //     d=\"M9 0.75C4.125 0.75 0.25 4.625 0.25 9.5C0.25 14.375 4.125 18.25 9 18.25C13.875 18.25 17.75 14.375 17.75 9.5C17.75 4.625 13.875 0.75 9 0.75ZM12.375 13.875L9 10.5L5.625 13.875L4.625 12.875L8 9.5L4.625 6.125L5.625 5.125L9 8.5L12.375 5.125L13.375 6.125L10 9.5L13.375 12.875L12.375 13.875Z\"\r\n    //     fill=\"white\"\r\n    //   />\r\n    // </svg>\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-white-x.svg\" alt=\"White Cirle Cross Icon\"\r\n      width=\"18\"\r\n      height=\"19\"\r\n\r\n      className={color}\r\n    />\r\n  );\r\n};\r\nexport const PublicProfileIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-profile-user-light.svg\" alt=\"Public Profile Icon\" />\r\n  );\r\n};\r\nexport const SellerDashboardIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-gauge.svg\" alt=\"Seller Dashboard Icon\" />\r\n  );\r\n};\r\nexport const MarketplaceDisputeIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-gavel.svg\" alt=\"Marketplace Dispute Icon\" />\r\n  );\r\n};\r\nexport const MarketplaceListIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-marketplace-icon.svg\" alt=\"Marketplace List Icon\" />\r\n  );\r\n};\r\nexport const PurchasedProductIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-package.svg\" alt=\"Purchased Product Icon\" />\r\n  );\r\n};\r\nexport const SoldProductIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-coin.svg\" alt=\"Sold Product Icon\" />\r\n  );\r\n};\r\nexport const LogoutIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"18\"\r\n      viewBox=\"0 0 18 18\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M2 18C1.45 18 0.979333 17.8043 0.588 17.413C0.196667 17.0217 0.000666667 16.5507 0 16V2C0 1.45 0.196 0.979333 0.588 0.588C0.98 0.196667 1.45067 0.000666667 2 0H9V2H2V16H9V18H2ZM13 14L11.625 12.55L14.175 10H6V8H14.175L11.625 5.45L13 4L18 9L13 14Z\"\r\n        fill=\"#00ADEF\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const CheckGradientIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"211\"\r\n      height=\"208\"\r\n      viewBox=\"0 0 211 208\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M203.341 74.5632C206.051 83.8216 207.504 93.6158 207.504 103.752C207.504 161.052 161.052 207.504 103.752 207.504C46.4519 207.504 0 161.052 0 103.752C0 46.4518 46.4519 -9.15527e-05 103.752 -9.15527e-05C131.892 -9.15527e-05 157.416 11.2021 176.105 29.3936L194.876 22.6914L101.952 115.617L63.9188 77.5842L39.9479 101.556L101.952 163.559L210.127 55.3835L203.341 74.5632Z\"\r\n        fill=\"url(#paint0_radial_490_1337)\"\r\n      />\r\n      <defs>\r\n        <radialGradient\r\n          id=\"paint0_radial_490_1337\"\r\n          cx=\"0\"\r\n          cy=\"0\"\r\n          r=\"1\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n          gradientTransform=\"translate(59.8666 54.0097) rotate(45) scale(197.976)\"\r\n        >\r\n          <stop stopColor=\"#73D1E1\" />\r\n          <stop offset=\"1\" stopColor=\"#395BB2\" />\r\n        </radialGradient>\r\n      </defs>\r\n    </svg>\r\n  );\r\n};\r\nexport const CalculatorIcon = () => {\r\n  return (\r\n    // <svg\r\n    //   width=\"38\"\r\n    //   height=\"38\"\r\n    //   viewBox=\"0 0 38 38\"\r\n    //   fill=\"none\"\r\n    //   xmlns=\"http://www.w3.org/2000/svg\"\r\n    // >\r\n    //   <path\r\n    //     d=\"M10.6667 31.5H13.7917V27.3333H17.9583V24.2083H13.7917V20.0417H10.6667V24.2083H6.5V27.3333H10.6667V31.5ZM21.0833 29.9375H31.5V26.8125H21.0833V29.9375ZM21.0833 24.7292H31.5V21.6042H21.0833V24.7292ZM23.375 16.8125L26.2917 13.8958L29.2083 16.8125L31.3958 14.625L28.4792 11.6042L31.3958 8.6875L29.2083 6.5L26.2917 9.41667L23.375 6.5L21.1875 8.6875L24.1042 11.6042L21.1875 14.625L23.375 16.8125ZM7.02084 13.1667H17.4375V10.0417H7.02084V13.1667ZM4.41667 37.75C3.27084 37.75 2.28959 37.3424 1.47292 36.5271C0.656253 35.7118 0.248615 34.7306 0.250004 33.5833V4.41667C0.250004 3.27083 0.658337 2.29028 1.475 1.475C2.29167 0.659722 3.27223 0.251389 4.41667 0.25H33.5833C34.7292 0.25 35.7104 0.658333 36.5271 1.475C37.3438 2.29167 37.7514 3.27222 37.75 4.41667V33.5833C37.75 34.7292 37.3424 35.7104 36.5271 36.5271C35.7118 37.3438 34.7306 37.7514 33.5833 37.75H4.41667Z\"\r\n    //     fill=\"#00ADEF\"\r\n    //   />\r\n    // </svg>\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-calculator.svg\" alt=\"\" />\r\n  );\r\n};\r\nexport const SolidRedArrowIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"31\"\r\n      height=\"36\"\r\n      viewBox=\"0 0 31 36\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M28.5 13.6699C31.8333 15.5944 31.8333 20.4056 28.5 22.3301L7.5 34.4545C4.16666 36.379 -1.74729e-06 33.9734 -1.57905e-06 30.1243L-5.19101e-07 5.87564C-3.50856e-07 2.02664 4.16667 -0.378984 7.5 1.54552L28.5 13.6699Z\"\r\n        fill=\"#FF696A\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const DropArrowIcon = ({ width = 24, height = 13 }) => {\r\n  return (\r\n    <svg\r\n      width={width}\r\n      height={height}\r\n      viewBox=\"0 0 24 13\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M3.47599 3.12261e-08L0.857422 2.47368L12.0003 13L23.1431 2.47368L20.5246 2.34528e-07L12.0003 8.03509L3.47599 3.12261e-08Z\"\r\n        fill=\"white\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const DropArrowUpIcon = ({ width = 24, height = 13 }) => {\r\n  return (\r\n    <svg\r\n      width={width}\r\n      height={height}\r\n      viewBox=\"0 0 24 13\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      style={{ transform: 'rotate(180deg)' }}\r\n    >\r\n      <path\r\n        d=\"M3.47599 3.12261e-08L0.857422 2.47368L12.0003 13L23.1431 2.47368L20.5246 2.34528e-07L12.0003 8.03509L3.47599 3.12261e-08Z\"\r\n        fill=\"white\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const DropDownArrowIcon = ({ width = 14, height = 7 }) => {\r\n  return (\r\n    <svg width={width}\r\n      height={height}\r\n      viewBox=\"0 0 14 7\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\">\r\n      <path d=\"M0.333008 0.333984L6.99967 7.00065L13.6663 0.333984H0.333008Z\" fill=\"white\" />\r\n    </svg>\r\n\r\n  );\r\n};\r\nexport const BlackDropDownArrowIcon = ({ width = 14, height = 7 }) => {\r\n  return (\r\n    <svg width={width}\r\n      height={height}\r\n      viewBox=\"0 0 14 7\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      <path d=\"M0.333252 0.333008L6.99992 6.99967L13.6666 0.333008H0.333252Z\" fill=\"#666666\" />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const DeleteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-delete.svg\" alt=\"Delete Icon\"\r\n      width=\"20\"\r\n      height=\"21\"\r\n    />\r\n  );\r\n};\r\nexport const TradeIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"48\"\r\n      height=\"48\"\r\n      viewBox=\"0 0 48 48\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M0.75 42.0833H47.25V47.25H0.75V42.0833ZM8.5 34.3333C11.3417 34.3333 13.6667 32.0083 13.6667 29.1667C13.6667 27.875 13.15 26.5833 12.375 25.8083L15.7333 18.8333H16.25C17.5417 18.8333 18.8333 18.3167 19.6083 17.5417L26.5833 21.1583V21.4167C26.5833 24.2583 28.9083 26.5833 31.75 26.5833C34.5917 26.5833 36.9167 24.2583 36.9167 21.4167C36.9167 20.125 36.4 19.0917 35.625 18.0583L38.9833 11.0833H39.5C42.3417 11.0833 44.6667 8.75833 44.6667 5.91667C44.6667 3.075 42.3417 0.75 39.5 0.75C36.6583 0.75 34.3333 3.075 34.3333 5.91667C34.3333 7.20833 34.85 8.5 35.625 9.275L32.2667 16.25H31.75C30.4583 16.25 29.1667 16.7667 28.3917 17.5417L21.4167 14.1833V13.6667C21.4167 10.825 19.0917 8.5 16.25 8.5C13.4083 8.5 11.0833 10.825 11.0833 13.6667C11.0833 14.9583 11.6 16.25 12.375 17.025L9.01667 24H8.5C5.65833 24 3.33333 26.325 3.33333 29.1667C3.33333 32.0083 5.65833 34.3333 8.5 34.3333Z\"\r\n        fill=\"#00ADEF\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const ArtArrowIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"30\"\r\n      height=\"32\"\r\n      viewBox=\"0 0 30 32\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M1.64673 22.9062L27.6275 7.90625\"\r\n        stroke=\"#00ADEF\"\r\n        strokeWidth=\"3\"\r\n        strokeLinecap=\"round\"\r\n      />\r\n      <path\r\n        d=\"M1.64673 15.0469L8.64673 8.04688\"\r\n        stroke=\"#00ADEF\"\r\n        strokeWidth=\"3\"\r\n        strokeLinecap=\"round\"\r\n      />\r\n      <path\r\n        d=\"M3.14673 29.0469H13.0462\"\r\n        stroke=\"#00ADEF\"\r\n        strokeWidth=\"3\"\r\n        strokeLinecap=\"round\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const RedInfoIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-warning.svg\" alt=\"Red Info Icon\"\r\n      width=\"28\"\r\n      height=\"28\"\r\n    />\r\n  );\r\n};\r\nexport const GreyCheckIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-success-gray.svg\" alt=\"Grey Success Icon\"\r\n      width=\"28\"\r\n      height=\"28\"\r\n    />\r\n  );\r\n};\r\nexport const GreyCrossIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-failure.svg\" alt=\"Grey Cross Icon\"\r\n      width=\"28\"\r\n      height=\"28\"\r\n    />\r\n  );\r\n};\r\nexport const ReferIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-refer-a-friend.svg\" alt=\"Refer Icon\" />\r\n  );\r\n};\r\nexport const PartnershipIcon = () => {\r\n  return (\r\n    <svg\r\n      id=\"Layer_1\"\r\n      data-name=\"Layer 1\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      width=\"20\"\r\n      height=\"20\"\r\n      viewBox=\"0 0 24 24\"\r\n    >\r\n      <path\r\n        d=\"m17.063,2.185c-1.245.06-2.442.603-3.367,1.528l-3.63,3.63c.57-.573,2.687-.179,3.2.334l2.197-2.197c.487-.487,1.096-.785,1.719-.812.424-.021,1.024.069,1.552.597.493.493.597,1.066.597,1.457,0,.654-.299,1.304-.812,1.815l-3.821,3.845c-.961.961-2.424,1.039-3.272.191-.484-.484-1.281-.487-1.767,0s-.487,1.281,0,1.767c.872.872,2.018,1.313,3.2,1.313,1.278,0,2.582-.522,3.582-1.528l3.845-3.821c.976-.973,1.528-2.275,1.528-3.582,0-1.215-.46-2.37-1.313-3.224-.913-.913-2.14-1.373-3.439-1.313Zm-5.922,6.161c-1.278,0-2.603.525-3.606,1.528l-3.821,3.821c-.976.973-1.528,2.275-1.528,3.582,0,1.215.46,2.37,1.313,3.224.913.913,2.14,1.373,3.439,1.313,1.245-.06,2.442-.603,3.367-1.528l3.63-3.63c-.573.573-2.687.179-3.2-.334l-2.197,2.197c-.487.487-1.096.782-1.719.812-.424.021-1.024-.069-1.552-.597-.493-.493-.597-1.069-.597-1.457,0-.654.299-1.304.812-1.815l3.821-3.845c.961-.961,2.424-1.036,3.272-.191.487.487,1.284.487,1.767,0,.487-.487.487-1.281,0-1.767-.872-.872-2.021-1.313-3.2-1.313Z\"\r\n        fill=\"#fff\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const ResendCodeIcon = ({ isRotating }) => {\r\n  return (\r\n    <svg\r\n      className={isRotating ? \"rotate\" : \"\"}\r\n      width=\"19\"\r\n      height=\"18\"\r\n      viewBox=\"0 0 19 18\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M18.7693 1.84688V6.34688C18.7693 6.54579 18.6903 6.73656 18.5497 6.87721C18.409 7.01786 18.2182 7.09688 18.0193 7.09688H13.5193C13.3717 7.09664 13.2275 7.05285 13.1047 6.97101C12.9818 6.88917 12.8859 6.77291 12.8289 6.63679C12.7718 6.50066 12.7562 6.35074 12.784 6.20578C12.8117 6.06082 12.8816 5.92728 12.985 5.82188L14.71 4.09688L14.3068 3.69375C13.2576 2.64569 11.9212 1.93222 10.4666 1.6435C9.01196 1.35479 7.50438 1.5038 6.13442 2.07171C4.76446 2.63961 3.59362 3.60091 2.76988 4.83409C1.94613 6.06728 1.50648 7.517 1.50648 9C1.50648 10.483 1.94613 11.9327 2.76988 13.1659C3.59362 14.3991 4.76446 15.3604 6.13442 15.9283C7.50438 16.4962 9.01196 16.6452 10.4666 16.3565C11.9212 16.0678 13.2576 15.3543 14.3068 14.3063C14.3758 14.2357 14.4582 14.1796 14.5492 14.1413C14.6401 14.103 14.7378 14.0833 14.8365 14.0833C14.9352 14.0833 15.0329 14.103 15.1239 14.1413C15.2148 14.1796 15.2972 14.2357 15.3662 14.3063C15.5065 14.4469 15.5852 14.6373 15.5852 14.8359C15.5852 15.0345 15.5065 15.225 15.3662 15.3656C14.1073 16.6238 12.5037 17.4805 10.758 17.8274C9.01229 18.1743 7.20293 17.9958 5.55867 17.3145C3.91441 16.6331 2.50908 15.4796 1.52035 13.9996C0.531628 12.5197 0.00390625 10.7798 0.00390625 9C0.00390625 7.22017 0.531628 5.4803 1.52035 4.00036C2.50908 2.52042 3.91441 1.36687 5.55867 0.685539C7.20293 0.00421169 9.01229 -0.174293 10.758 0.172592C12.5037 0.519478 14.1073 1.37618 15.3662 2.63438L15.7693 3.0375L17.485 1.32188C17.589 1.21629 17.722 1.14391 17.8672 1.11388C18.0124 1.08384 18.1632 1.0975 18.3006 1.15313C18.4374 1.21109 18.5545 1.30747 18.6377 1.43059C18.7209 1.55371 18.7666 1.69831 18.7693 1.84688Z\"\r\n        fill=\"white\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const ContactCustomerSupport = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"18\"\r\n      viewBox=\"0 0 18 18\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M9 0.875C7.39303 0.875 5.82214 1.35152 4.486 2.24431C3.14985 3.1371 2.10844 4.40605 1.49348 5.8907C0.87852 7.37535 0.717618 9.00901 1.03112 10.5851C1.34463 12.1612 2.11846 13.6089 3.25476 14.7452C4.39106 15.8815 5.8388 16.6554 7.4149 16.9689C8.99099 17.2824 10.6247 17.1215 12.1093 16.5065C13.594 15.8916 14.8629 14.8502 15.7557 13.514C16.6485 12.1779 17.125 10.607 17.125 9C17.1209 6.84638 16.2635 4.78216 14.7407 3.25932C13.2178 1.73648 11.1536 0.87913 9 0.875ZM9 14C8.81458 14 8.63333 13.945 8.47916 13.842C8.32499 13.739 8.20482 13.5926 8.13387 13.4213C8.06291 13.25 8.04434 13.0615 8.08052 12.8796C8.11669 12.6977 8.20598 12.5307 8.33709 12.3996C8.4682 12.2685 8.63525 12.1792 8.81711 12.143C8.99896 12.1068 9.18746 12.1254 9.35877 12.1964C9.53007 12.2673 9.67649 12.3875 9.77951 12.5417C9.88252 12.6958 9.9375 12.8771 9.9375 13.0625C9.9375 13.3111 9.83873 13.5496 9.66292 13.7254C9.4871 13.9012 9.24864 14 9 14ZM9.625 10.1797V10.25C9.625 10.4158 9.55916 10.5747 9.44195 10.6919C9.32474 10.8092 9.16576 10.875 9 10.875C8.83424 10.875 8.67527 10.8092 8.55806 10.6919C8.44085 10.5747 8.375 10.4158 8.375 10.25V9.625C8.375 9.45924 8.44085 9.30027 8.55806 9.18306C8.67527 9.06585 8.83424 9 9 9C9.30904 9 9.61113 8.90836 9.86808 8.73667C10.125 8.56498 10.3253 8.32095 10.4436 8.03544C10.5618 7.74993 10.5928 7.43577 10.5325 7.13267C10.4722 6.82958 10.3234 6.55117 10.1049 6.33265C9.88634 6.11413 9.60793 5.96531 9.30483 5.90502C9.00174 5.84473 8.68757 5.87568 8.40206 5.99394C8.11655 6.1122 7.87252 6.31247 7.70083 6.56942C7.52914 6.82637 7.4375 7.12847 7.4375 7.4375C7.4375 7.60326 7.37166 7.76223 7.25445 7.87944C7.13724 7.99665 6.97826 8.0625 6.8125 8.0625C6.64674 8.0625 6.48777 7.99665 6.37056 7.87944C6.25335 7.76223 6.1875 7.60326 6.1875 7.4375C6.18751 6.90805 6.33695 6.38936 6.61865 5.94107C6.90036 5.49279 7.30287 5.13312 7.77991 4.90344C8.25694 4.67376 8.78912 4.58339 9.31523 4.64273C9.84134 4.70207 10.34 4.90871 10.7539 5.23888C11.1678 5.56905 11.4801 6.00934 11.6549 6.50911C11.8296 7.00888 11.8598 7.54784 11.7418 8.06398C11.6239 8.58013 11.3627 9.05251 10.9882 9.42678C10.6137 9.80106 10.1412 10.062 9.625 10.1797Z\"\r\n        fill=\"#00ADEF\"\r\n      />\r\n    </svg>\r\n\r\n\r\n  );\r\n};\r\nexport const RedErrorCircle = () => {\r\n  return (\r\n    // <svg\r\n    //   xmlns=\"http://www.w3.org/2000/svg\"\r\n    //   width=\"24\"\r\n    //   height=\"24\"\r\n    //   viewBox=\"0 0 24 24\"\r\n    //   fill=\"#ff696a\"\r\n    // >\r\n    //   <path\r\n    //     d=\"M11.953 2C6.465 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.493 2 11.953 2zM13 17h-2v-2h2v2zm0-4h-2V7h2v6z\"\r\n    //     fill=\"#fffff\"\r\n    //   />\r\n    // </svg>\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-warning.svg\"\r\n      alt=\"Red Error Icon\"\r\n      width=\"24\"\r\n      height=\"24\"\r\n    />\r\n  );\r\n};\r\nexport const BlackErrorCircle = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"17\"\r\n      viewBox=\"0 0 18 17\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M9 0.375C9 0.375 10.6526 0.375 12.1628 1.01376C12.1628 1.01376 13.621 1.63053 14.7452 2.75476C14.7452 2.75476 15.8695 3.87899 16.4862 5.33719C16.4862 5.33719 17.125 6.84739 17.125 8.5C17.125 8.5 17.125 10.1526 16.4862 11.6628C16.4862 11.6628 15.8695 13.121 14.7452 14.2452C14.7452 14.2452 13.621 15.3695 12.1628 15.9862C12.1628 15.9862 10.6526 16.625 9 16.625C9 16.625 7.34739 16.625 5.83719 15.9862C5.83719 15.9862 4.37899 15.3695 3.25476 14.2452C3.25476 14.2452 2.13053 13.121 1.51376 11.6628C1.51376 11.6628 0.875 10.1526 0.875 8.5C0.875 8.5 0.875 6.84739 1.51376 5.33719C1.51376 5.33719 2.13053 3.87899 3.25476 2.75476C3.25476 2.75476 4.37899 1.63053 5.83719 1.01376C5.83719 1.01376 7.34739 0.375 9 0.375ZM9 1.625C9 1.625 7.60087 1.625 6.32413 2.16501C6.32413 2.16501 5.09047 2.68681 4.13864 3.63864C4.13864 3.63864 3.18681 4.59047 2.66502 5.82413C2.66502 5.82413 2.125 7.10087 2.125 8.5C2.125 8.5 2.125 9.89913 2.66502 11.1759C2.66502 11.1759 3.18681 12.4095 4.13864 13.3614C4.13864 13.3614 5.09047 14.3132 6.32413 14.835C6.32413 14.835 7.60087 15.375 9 15.375C9 15.375 10.3991 15.375 11.6759 14.835C11.6759 14.835 12.9095 14.3132 13.8614 13.3614C13.8614 13.3614 14.8132 12.4095 15.335 11.1759C15.335 11.1759 15.875 9.89912 15.875 8.5C15.875 8.5 15.875 7.10087 15.335 5.82413C15.335 5.82413 14.8132 4.59047 13.8614 3.63864C13.8614 3.63864 12.9095 2.68681 11.6759 2.16501C11.6759 2.16501 10.3991 1.625 9 1.625Z\"\r\n        fill=\"#1C1C1C\"\r\n      />\r\n      <path\r\n        d=\"M9 12.875H9.625C9.97018 12.875 10.25 12.5952 10.25 12.25C10.25 11.9048 9.97018 11.625 9.625 11.625V7.875C9.625 7.52982 9.34518 7.25 9 7.25H8.375C8.02982 7.25 7.75 7.52982 7.75 7.875C7.75 8.22018 8.02982 8.5 8.375 8.5V12.25C8.375 12.5952 8.65482 12.875 9 12.875Z\"\r\n        fill=\"#1C1C1C\"\r\n      />\r\n      <path\r\n        d=\"M9.78125 5.0625C9.78125 5.58027 9.36148 6 8.84375 6C8.32602 6 7.90625 5.58027 7.90625 5.0625C7.90625 4.54473 8.32602 4.125 8.84375 4.125C9.36148 4.125 9.78125 4.54473 9.78125 5.0625Z\"\r\n        fill=\"#1C1C1C\"\r\n      />\r\n    </svg>\r\n\r\n  );\r\n};\r\nexport const DeviceMobileSpeaker = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-device-00ADEF.svg\" alt=\"Device Mobile Speaker Icon\"\r\n      width=\"22\"\r\n      height=\"22\"\r\n    />\r\n\r\n  );\r\n};\r\nexport const ViewCartBaseBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-cart-00ADEF.svg\" alt=\"View Cart BaseBlue\" />\r\n  );\r\n};\r\nexport const ViewCartDarkBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-cart-04498C.svg\" alt=\"View Cart Dark Blue\" />\r\n\r\n  );\r\n};\r\nexport const ViewCartGray = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-cart-808080.svg\" alt=\"View Cart Gray\" />\r\n\r\n  );\r\n};\r\nexport const CheckoutCardBaseBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-card-00ADEF.svg\" alt=\"Checkout Card BaseBlue\" />\r\n  );\r\n};\r\nexport const CheckoutCardDarkBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-card-04498C.svg\" alt=\"Checkout Card BaseBlue\" />\r\n  );\r\n};\r\nexport const CheckoutCardGray = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-card-808080.svg\" alt=\"Checkout Card Gray\" />\r\n  );\r\n};\r\nexport const AccessBaseBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-access-00ADEF.svg\" alt=\"Access Base Blue\" />\r\n  );\r\n};\r\nexport const AccessDarkBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-access-04498C.svg\" alt=\"Access Dark Blue\" />\r\n  );\r\n};\r\nexport const AccessGray = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-access-808080.svg\" alt=\"Access Gray\" />\r\n  );\r\n};\r\nexport const TopRightArrowIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"13\"\r\n      height=\"13\"\r\n      viewBox=\"0 0 13 13\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M0.832189 10.79L9.12219 2.5H3.54219C3.27697 2.5 3.02262 2.39464 2.83508 2.20711C2.64754 2.01957 2.54219 1.76522 2.54219 1.5C2.54219 1.23478 2.64754 0.98043 2.83508 0.792893C3.02262 0.605357 3.27697 0.5 3.54219 0.5H11.4922C11.7574 0.5 12.0118 0.605357 12.1993 0.792893C12.3868 0.98043 12.4922 1.23478 12.4922 1.5V9.5C12.4922 9.76522 12.3868 10.0196 12.1993 10.2071C12.0118 10.3946 11.7574 10.5 11.4922 10.5H11.5422C11.277 10.5 11.0226 10.3946 10.8351 10.2071C10.6475 10.0196 10.5422 9.76522 10.5422 9.5V3.95L2.28219 12.21C2.18922 12.3037 2.07862 12.3781 1.95677 12.4289C1.83491 12.4797 1.7042 12.5058 1.57219 12.5058C1.44018 12.5058 1.30947 12.4797 1.18761 12.4289C1.06575 12.3781 0.955151 12.3037 0.862187 12.21C0.766468 12.119 0.689713 12.01 0.636353 11.8892C0.582994 11.7684 0.554085 11.6383 0.551295 11.5063C0.548506 11.3742 0.571892 11.243 0.620103 11.12C0.668314 10.9971 0.740396 10.8849 0.832189 10.79Z\"\r\n        fill=\"#00ADEF\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const MoneyWithWings = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-money-wings.svg\" alt=\"Money Wings Icon\" />\r\n  );\r\n};\r\nexport const FlatBlueBook = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-book-00A6ED.svg\" alt=\"Flat Blue Book\" />\r\n  );\r\n};\r\n\r\nexport const RedCircleCrossIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-failure-red.svg\" alt=\"Red Cross\" />\r\n  );\r\n};\r\n\r\nexport const WhiteCrossCircle = () => {\r\n  return (\r\n    <svg width=\"28\" height=\"28\" viewBox=\"0 0 28 28\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      <path d=\"M14 0C6.2 0 0 6.2 0 14C0 21.8 6.2 28 14 28C21.8 28 28 21.8 28 14C28 6.2 21.8 0 14 0ZM19.4 21L14 15.6L8.6 21L7 19.4L12.4 14L7 8.6L8.6 7L14 12.4L19.4 7L21 8.6L15.6 14L21 19.4L19.4 21Z\" fill=\"white\" />\r\n    </svg>\r\n\r\n  )\r\n}\r\nexport const BlackCross = ({ width = 14, height = 14 }) => {\r\n  return (\r\n    <svg width={width} height={height} viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      <path d=\"M1.4 14L0 12.6L5.6 7L0 1.4L1.4 0L7 5.6L12.6 0L14 1.4L8.4 7L14 12.6L12.6 14L7 8.4L1.4 14Z\" fill=\"black\" />\r\n    </svg>\r\n\r\n  )\r\n}\r\n\r\nexport const AddPlusIcon = ({ width = 10, height = 10 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-plus.svg\" width={width} height={height} alt=\"Add Model Icon\" />\r\n\r\n  );\r\n};\r\nexport const DragDropIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-drag-drop-icon.svg\" alt=\"Drap Drop Icon\" />\r\n\r\n  );\r\n};\r\nexport const SolidIncon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-info-marker.svg\" alt=\"Info Marker Icon\" />\r\n  );\r\n};\r\nexport const BlackDownIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-black-down-arrow.svg\" alt=\"Info Marker Icon\" />\r\n  );\r\n};\r\n\r\nexport const YellowInfoHexa = () => {\r\n  return (\r\n    <svg\r\n      width=\"12\"\r\n      height=\"14\"\r\n      viewBox=\"0 0 12 14\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path fillRule=\"evenodd\" clipRule=\"evenodd\" d=\"M3.22867 1.53727C4.58133 0.736604 5.25733 0.335938 6 0.335938C6.74267 0.335938 7.41867 0.735938 8.77133 1.53727L9.22867 1.80794C10.5813 2.60927 11.2573 3.00994 11.6287 3.66927C12 4.32927 12 5.12927 12 6.73194V7.27327C12 8.87527 12 9.6766 11.6287 10.3359C11.2573 10.9959 10.5813 11.3959 9.22867 12.1966L8.77133 12.4679C7.41867 13.2686 6.74267 13.6693 6 13.6693C5.25733 13.6693 4.58133 13.2693 3.22867 12.4679L2.77133 12.1966C1.41867 11.3966 0.742667 10.9953 0.371333 10.3359C-3.97364e-08 9.67594 0 8.87594 0 7.27327V6.73194C0 5.12927 -3.97364e-08 4.3286 0.371333 3.66927C0.742667 3.00927 1.41867 2.60927 2.77133 1.80794L3.22867 1.53727ZM6.66667 9.66927C6.66667 9.84608 6.59643 10.0157 6.4714 10.1407C6.34638 10.2657 6.17681 10.3359 6 10.3359C5.82319 10.3359 5.65362 10.2657 5.5286 10.1407C5.40357 10.0157 5.33333 9.84608 5.33333 9.66927C5.33333 9.49246 5.40357 9.32289 5.5286 9.19787C5.65362 9.07284 5.82319 9.0026 6 9.0026C6.17681 9.0026 6.34638 9.07284 6.4714 9.19787C6.59643 9.32289 6.66667 9.49246 6.66667 9.66927ZM6 3.16927C6.13261 3.16927 6.25979 3.22195 6.35355 3.31572C6.44732 3.40949 6.5 3.53666 6.5 3.66927V7.66927C6.5 7.80188 6.44732 7.92906 6.35355 8.02282C6.25979 8.11659 6.13261 8.16927 6 8.16927C5.86739 8.16927 5.74022 8.11659 5.64645 8.02282C5.55268 7.92906 5.5 7.80188 5.5 7.66927V3.66927C5.5 3.53666 5.55268 3.40949 5.64645 3.31572C5.74022 3.22195 5.86739 3.16927 6 3.16927Z\" fill=\"#FEA500\" />\r\n    </svg>\r\n\r\n  );\r\n};\r\n\r\nexport const TripleDotsMenu = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-kebab-menu.svg\" alt=\"Kabab Menu\" />\r\n  );\r\n};\r\n\r\nexport const WhiteDownArrow = () => {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"13\" height=\"6\" viewBox=\"0 0 14 8\" fill=\"none\">\r\n      <path d=\"M0.666504 0.667969L7.33317 7.33463L13.9998 0.667969H0.666504Z\" fill=\"white\" />\r\n    </svg>\r\n    // <img src=\"http://cdn.tradereply.com/dev/site-assets/icons/tradereply-white-down-arrow.svg\" width={width} height={height} alt=\"white down arrow\" />\r\n  );\r\n};\r\nexport const BlackDownArrow = () => {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"13\" height=\"6\" viewBox=\"0 0 14 8\" fill=\"none\">\r\n      <path d=\"M0.666504 0.667969L7.33317 7.33463L13.9998 0.667969H0.666504Z\" fill=\"black\" />\r\n    </svg>\r\n  );\r\n};\r\nexport const WhiteCrossIcon = () => {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"15\" viewBox=\"0 0 16 15\" fill=\"none\">\r\n      <path\r\n        d=\"M9.05969 7.49973L15.2847 1.2841C15.4043 1.13838 15.4654 0.953383 15.4562 0.765094C15.4469 0.576804 15.368 0.398686 15.2347 0.265385C15.1014 0.132084 14.9232 0.0531312 14.7349 0.0438836C14.5467 0.0346361 14.3617 0.095755 14.2159 0.215352L8.00031 6.44035L1.78469 0.215352C1.63897 0.095755 1.45397 0.0346361 1.26568 0.0438836C1.07739 0.0531312 0.899272 0.132084 0.76597 0.265385C0.632669 0.398686 0.553717 0.576804 0.544469 0.765094C0.535221 0.953383 0.59634 1.13838 0.715938 1.2841L6.94094 7.49973L0.715938 13.7154C0.575101 13.8575 0.496094 14.0496 0.496094 14.2497C0.496094 14.4499 0.575101 14.6419 0.715938 14.7841C0.859293 14.9227 1.0509 15.0002 1.25031 15.0002C1.44972 15.0002 1.64133 14.9227 1.78469 14.7841L8.00031 8.5591L14.2159 14.7841C14.3593 14.9227 14.5509 15.0002 14.7503 15.0002C14.9497 15.0002 15.1413 14.9227 15.2847 14.7841C15.4255 14.6419 15.5045 14.4499 15.5045 14.2497C15.5045 14.0496 15.4255 13.8575 15.2847 13.7154L9.05969 7.49973Z\"\r\n        fill=\"white\" />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const ProfileUserDarkIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-profile-user-dark.svg\" alt=\"Profile Dark Icon\" />\r\n  );\r\n};\r\nexport const BlueLocationIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-location.svg\" alt=\"Blue Location Icon\" />\r\n  );\r\n};\r\nexport const RatingStarIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-star-single.svg\" alt=\"Rating Star Icon\" />\r\n  );\r\n};\r\nexport const ThumbUpIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-thumb-up-green.svg\" alt=\"Thumb Up Icon\" />\r\n  );\r\n};\r\nexport const ThumbDownIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-thumb-down-red.svg\" alt=\"Thumb Down Icon\" />\r\n  );\r\n};\r\nexport const BlackShareIcon = ({ width = 18, height = 13 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-share-icon.svg\" width={width} height={height} alt=\"Black Share Icon\" />\r\n  );\r\n};\r\nexport const StaticListingImg = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/user-uploads/marketplace/listing-12345.png\" alt=\"Black Share Icon\" />\r\n  );\r\n};\r\n\r\nexport const WhiteDropDownArrow = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-drop-arrow.svg\" alt=\"White DropDown Icon\" />\r\n  );\r\n};\r\nexport const WhiteSingleStack = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-single-stack.svg\" alt=\"White Single Stack Icon\" />\r\n  );\r\n};\r\nexport const WhiteDoubleStack = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-double-stack.svg\" alt=\"White Double Stack Icon\" />\r\n  );\r\n};\r\nexport const WhiteTripleStack = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-triple-stack.svg\" alt=\"White Triple Stack Icon\" />\r\n  );\r\n};\r\nexport const OpenNewtabIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-open-new-tab.svg\" alt=\"New Tab Icon\" />\r\n  );\r\n};\r\nexport const RenameIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-edit-pencil.svg\" alt=\"Rename Icon\" />\r\n  );\r\n};\r\nexport const DeleteDarkIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-trashcan-dark.svg\" alt=\"Delete Dark Icon\" />\r\n  );\r\n};\r\nexport const EyeDarkInsightIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-eye-dark-insights.svg\" alt=\"Eye Dark Insight Icon\" />\r\n  );\r\n};\r\nexport const EyeDarkIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-eye-dark.svg\" alt=\"Eye Dark Icon\" />\r\n  );\r\n};\r\nexport const FollowersIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-followers-icon.svg\" alt=\"Followers Icon\" />\r\n  );\r\n};\r\nexport const ShareLightStrIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-share-white-fill.svg\" alt=\"Followers Icon\" />\r\n  );\r\n};\r\nexport const RelistIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-relist-icon.svg\" alt=\"Relist Icon\" />\r\n  );\r\n};\r\nexport const DigitaLAssetIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-digital-asset.svg\" alt=\"Digital Asset Icon\" />\r\n  );\r\n};\r\nexport const LicenseIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-license.svg\" alt=\"license Icon\" />\r\n  );\r\n};\r\nexport const LightEyeIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-eye-light.svg\" alt=\"Light Eye Icon\" />\r\n  );\r\n};\r\nexport const AddBlueIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-add.svg\" alt=\"Add Blue Icon\" />\r\n  );\r\n};\r\nexport const PinIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-pin.svg\" alt=\"Pin Icon\" />\r\n  );\r\n};\r\n\r\nexport const RightArrowIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"8\"\r\n      height=\"13\"\r\n      viewBox=\"0 0 8 13\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M7.15694 7.21102L1.49994 12.868L0.0859375 11.454L5.03594 6.50401L0.0859375 1.55401L1.49994 0.140015L7.15694 5.79701C7.34441 5.98454 7.44972 6.23885 7.44972 6.50401C7.44972 6.76918 7.34441 7.02349 7.15694 7.21102Z\"\r\n        fill=\"white\">\r\n      </path>\r\n    </svg>\r\n  );\r\n};\r\nexport const EditIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"18\"\r\n      viewBox=\"0 0 18 18\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M17.71 4.04C18.1 3.65 18.1 3 17.71 2.63L15.37 0.289999C15 -0.100001 14.35 -0.100001 13.96 0.289999L12.12 2.12L15.87 5.87M0 14.25V18H3.75L14.81 6.93L11.06 3.18L0 14.25Z\" fill=\"#00ADEF\"></path>\r\n    </svg>\r\n  );\r\n};\r\nexport const PlusIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"33\"\r\n      height=\"32\"\r\n      viewBox=\"0 0 33 32\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M32.5 20H20.5V32H12.5V20H0.5V12H12.5V0H20.5V12H32.5V20Z\" fill=\"white\">\r\n      </path>\r\n    </svg>\r\n  );\r\n};\r\nexport const RemoveIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"19\"\r\n      viewBox=\"0 0 18 19\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M9 0.75C4.125 0.75 0.25 4.625 0.25 9.5C0.25 14.375 4.125 18.25 9 18.25C13.875 18.25 17.75 14.375 17.75 9.5C17.75 4.625 13.875 0.75 9 0.75ZM12.375 13.875L9 10.5L5.625 13.875L4.625 12.875L8 9.5L4.625 6.125L5.625 5.125L9 8.5L12.375 5.125L13.375 6.125L10 9.5L13.375 12.875L12.375 13.875Z\"\r\n        fill=\"white\">\r\n      </path>\r\n    </svg>\r\n  );\r\n};\r\nexport const RightSolidArrowIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"12\"\r\n      height=\"13\"\r\n      viewBox=\"0 0 12 13\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M10.9903 5.63999L1.94025 0.769994C1.18025 0.359994 0.300251 1.06999 0.540251 1.89999L1.78025 6.23999C1.83025 6.41999 1.83025 6.59999 1.78025 6.77999L0.540251 11.12C0.300251 11.95 1.18025 12.66 1.94025 12.25L10.9903 7.37999C11.1446 7.29563 11.2735 7.17127 11.3632 7.01995C11.453 6.86862 11.5004 6.69593 11.5004 6.51999C11.5004 6.34406 11.453 6.17136 11.3632 6.02004C11.2735 5.86872 11.1446 5.74435 10.9903 5.65999V5.63999Z\"\r\n        fill=\"#00ADEF\"\r\n      >\r\n      </path>\r\n    </svg>\r\n  );\r\n};\r\nexport const LocationIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"20\"\r\n      height=\"20\"\r\n      viewBox=\"0 0 20 20\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M10.0004 11.1912C11.4363 11.1912 12.6004 10.0272 12.6004 8.59121C12.6004 7.15527 11.4363 5.99121 10.0004 5.99121C8.56445 5.99121 7.40039 7.15527 7.40039 8.59121C7.40039 10.0272 8.56445 11.1912 10.0004 11.1912Z\"\r\n        stroke=\"#292D32\"\r\n        stroke-width=\"1.5\"\r\n      />\r\n      <path\r\n        d=\"M3.01675 7.07533C4.65842 -0.141339 15.3501 -0.133006 16.9834 7.08366C17.9417 11.317 15.3084 14.9003 13.0001 17.117C11.3251 18.7337 8.67508 18.7337 6.99175 17.117C4.69175 14.9003 2.05842 11.3087 3.01675 7.07533Z\"\r\n        stroke=\"#292D32\"\r\n        strokeWidth=\"1.5\"\r\n      />\r\n    </svg>\r\n\r\n  );\r\n};\r\n\r\nexport const BulletPointIcon = ({ width = 8, height = 8 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-bullet-point.svg\" width={width} height={height} alt=\"Bullet Point Icon\" />\r\n  );\r\n};\r\nexport const CoinWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-coins-white.svg\" alt=\"White Coin Icon\" />\r\n  );\r\n};\r\nexport const ProductFormatWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-product-format-white.svg\" alt=\"Product Format White Icon\" />\r\n  );\r\n};\r\nexport const GuageWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-gauge-white.svg\" alt=\"Guage White Icon\" />\r\n  );\r\n};\r\nexport const TradingPlatformWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-trading-platform-white.svg\" alt=\"Trading Platform White Icon\" />\r\n  );\r\n};\r\nexport const ShuffleWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-shuffle-white.svg\" alt=\"Shuffle White Icon\" />\r\n  );\r\n};\r\nexport const ClockWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-clock-white.svg\" alt=\"Clock White Icon\" />\r\n  );\r\n};\r\n\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,MAAM,UAAU;IACrB,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,WAAW,kBACtB,8OAAC;QACC,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;kBAER,cAAA,8OAAC;YACC,IAAG;YACH,aAAU;YACV,GAAE;YACF,WAAU;YACV,MAAK;;;;;;;;;;;AAIJ,MAAM,YAAY;IACvB,qBACE,8OAAC;QACC,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;kBAER,cAAA,8OAAC;YACC,IAAG;YACH,aAAU;YACV,WAAU;sBAEV,cAAA,8OAAC;gBACC,IAAG;gBACH,aAAU;gBACV,WAAU;;kCAEV,8OAAC;wBACC,IAAG;wBACH,aAAU;wBACV,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,WAAU;wBACV,MAAK;;;;;;kCAEP,8OAAC;wBACC,IAAG;wBACH,aAAU;wBACV,WAAU;;0CAEV,8OAAC;gCACC,IAAG;gCACH,aAAU;gCACV,GAAE;gCACF,WAAU;gCACV,MAAK;gCACL,UAAS;;;;;;0CAEX,8OAAC;gCACC,IAAG;gCACH,aAAU;gCACV,GAAE;gCACF,MAAK;gCACL,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvB;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;AACO,MAAM,kBAAkB;IAC7B,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,gBAAgB;IAC3B,qBACE,8OAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;AACO,MAAM,gBAAgB;IAC3B,qBACE,8OAAC;QAAI,KAAI;QAAkF,KAAI;;;;;;AAEnG;AACO,MAAM,oBAAoB;IAC/B,qBACE,8OAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;AACO,MAAM,cAAc,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACrD,qBACE,8OAAC;QAAI,KAAI;QAAyE,OAAO;QAAO,QAAQ;QAAQ,KAAI;;;;;;AAExH;AACO,MAAM,YAAY;IACvB,qBACE,8OAAC;QAAI,KAAI;QAAyE,KAAI;;;;;;AAE1F;AACO,MAAM,aAAa;IACxB,qBACE,8OAAC;QAAI,KAAI;QAAyE,KAAI;;;;;;AAE1F;AACO,MAAM,gBAAgB,CAAC,EAAE,KAAK,EAAE;IACrC,qBACE,8OAAC;QACC,KAAI;QACJ,KAAI;QACJ,WAAW;;;;;;AAGjB;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;AACO,MAAM,UAAU;IACrB,qBACE,8OAAC;QAAI,KAAI;QAAsE,KAAI;;;;;;AAEvF;AACO,MAAM,aAAa;IACxB,qBACE,8OAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;AACO,MAAM,YAAY;IACvB,qBACE,8OAAC;QAAI,KAAI;QAAyE,KAAI;;;;;;AAE1F;AACO,MAAM,YAAY;IACvB,qBACE,8OAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;AACO,MAAM,eAAe;IAC1B,qBACE,8OAAC;QAAI,KAAI;QAA4E,KAAI;;;;;;AAE7F;AACO,MAAM,YAAY;IACvB,qBACE,8OAAC;QAAI,KAAI;QAA4E,KAAI;;;;;;AAE7F;AACO,MAAM,eAAe;IAC1B,qBACE,8OAAC;QAAI,KAAI;QAA2E,KAAI;;;;;;AAE5F;AACO,MAAM,gBAAgB;IAC3B,OACE,OAAO;IACP,eAAe;IACf,gBAAgB;IAChB,wBAAwB;IACxB,gBAAgB;IAChB,uCAAuC;IACvC,IAAI;IACJ,UAAU;IACV,4jCAA4jC;IAC5jC,mBAAmB;IACnB,OAAO;IACP,SAAS;kBACT,8OAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;AACO,MAAM,WAAW;IACtB,qBACE,8OAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;AACO,MAAM,YAAY;IACvB,qBACE,8OAAC;QAAI,KAAI;QAAwE,KAAI;QACnF,OAAM;QACN,QAAO;;;;;;AAGb;AACO,MAAM,YAAY,CAAC,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,EAAE;IACnD,qBACE,8OAAC;QAAI,KAAI;QAA0E,KAAI;QACrF,OAAO;QACP,QAAQ;;;;;;AAGd;AACO,MAAM,eAAe;IAC1B,qBACE,8OAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;AACO,MAAM,WAAW,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,EAAE;IACzD,qBACE,8OAAC;QAAI,KAAI;QAAuE,KAAI;QAClF,OAAO;QACP,QAAQ;QACR,WAAW;;;;;;AAGjB;AACO,MAAM,YAAY,CAAC,EAAE,QAAQ,MAAM,EAAE,SAAS,KAAK,EAAE;IAC1D,qBACE,8OAAC;QAAI,KAAI;QAAwE,QAAQ;QAAQ,OAAO;QAAO,KAAI;;;;;;AAEvH;AACO,MAAM,kBAAkB;IAC7B,qBACE,8OAAC;QAAI,KAAI;QAAmF,KAAI;;;;;;AAEpG;AACO,MAAM,qBAAqB;IAChC,qBACE,8OAAC;QAAI,KAAI;QAAmF,KAAI;QAC9F,OAAM;QACN,QAAO;;;;;;AAGb;AACO,MAAM,sBAAsB;IACjC,qBACE,8OAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QACC,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;kBACL,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;AACO,MAAM,sBAAsB;IACjC,qBACE,8OAAC;QAAI,KAAI;QAAoF,KAAI;;;;;;AAErG;AACO,MAAM,gBAAgB,CAAC,EAAE,QAAQ,MAAM,EAAE,SAAS,MAAM,EAAE;IAC/D,qBACE,8OAAC;QAAI,KAAI;QAA8E,QAAQ;QAAQ,OAAO;QAAO,KAAI;;;;;;AAE7H;AACO,MAAM,gBAAgB;IAC3B,qBACE,8OAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;AACO,MAAM,iBAAiB,CAAC,EAAE,KAAK,EAAE;IACtC,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;QACzF,WAAW;;;;;;AAGjB;AACO,MAAM,WAAW;IACtB,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;AACO,MAAM,WAAW;IACtB,qBACE,8OAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QAAI,KAAI;QAAsE,KAAI;;;;;;AAEvF;AACO,MAAM,eAAe;IAC1B,qBACE,8OAAC;QAAI,KAAI;QAA4E,KAAI;;;;;;AAE7F;AACO,MAAM,aAAa;IACxB,qBACE,8OAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;AACO,MAAM,eAAe;IAC1B,qBACE,8OAAC;QAAI,KAAI;QAA2E,KAAI;;;;;;AAE5F;AACO,MAAM,WAAW;IACtB,qBACE,8OAAC;QAAI,KAAI;QAAuE,KAAI;;;;;;AAExF;AACO,MAAM,WAAW;IACtB,qBACE,8OAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;AACO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,eAAe;IAC1B,qBACE,8OAAC;QAAI,KAAI;QAA2E,KAAI;;;;;;AAE5F;AACO,MAAM,WAAW;IACtB,qBACE,8OAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;AAEO,MAAM,YAAY,CAAC,EAAE,KAAK,EAAE;IACjC,OACE,OAAO;IACP,eAAe;IACf,gBAAgB;IAChB,wBAAwB;IACxB,gBAAgB;IAChB,uCAAuC;IACvC,IAAI;IACJ,UAAU;IACV,sSAAsS;IACtS,mBAAmB;IACnB,OAAO;IACP,SAAS;kBACT,8OAAC;QAAI,KAAI;QAA0E,KAAI;QACrF,OAAM;QACN,QAAO;QAEP,WAAW;;;;;;AAGjB;AACO,MAAM,oBAAoB;IAC/B,qBACE,8OAAC;QAAI,KAAI;QAAqF,KAAI;;;;;;AAEtG;AACO,MAAM,sBAAsB;IACjC,qBACE,8OAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;AACO,MAAM,yBAAyB;IACpC,qBACE,8OAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;AACO,MAAM,sBAAsB;IACjC,qBACE,8OAAC;QAAI,KAAI;QAAmF,KAAI;;;;;;AAEpG;AACO,MAAM,uBAAuB;IAClC,qBACE,8OAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;AACO,MAAM,kBAAkB;IAC7B,qBACE,8OAAC;QAAI,KAAI;QAAuE,KAAI;;;;;;AAExF;AACO,MAAM,aAAa;IACxB,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,oBAAoB;IAC/B,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;;0BAEN,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;0BACC,cAAA,8OAAC;oBACC,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,eAAc;oBACd,mBAAkB;;sCAElB,8OAAC;4BAAK,WAAU;;;;;;sCAChB,8OAAC;4BAAK,QAAO;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKrC;AACO,MAAM,iBAAiB;IAC5B,OACE,OAAO;IACP,eAAe;IACf,gBAAgB;IAChB,wBAAwB;IACxB,gBAAgB;IAChB,uCAAuC;IACvC,IAAI;IACJ,UAAU;IACV,o2BAAo2B;IACp2B,qBAAqB;IACrB,OAAO;IACP,SAAS;kBACT,8OAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;AACO,MAAM,oBAAoB;IAC/B,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACvD,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,kBAAkB,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACzD,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,OAAO;YAAE,WAAW;QAAiB;kBAErC,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,oBAAoB,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,CAAC,EAAE;IAC1D,qBACE,8OAAC;QAAI,OAAO;QACV,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;kBACN,cAAA,8OAAC;YAAK,GAAE;YAAgE,MAAK;;;;;;;;;;;AAInF;AACO,MAAM,yBAAyB,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,CAAC,EAAE;IAC/D,qBACE,8OAAC;QAAI,OAAO;QACV,QAAQ;QACR,SAAQ;QAAW,MAAK;QAAO,OAAM;kBACrC,cAAA,8OAAC;YAAK,GAAE;YAAgE,MAAK;;;;;;;;;;;AAGnF;AAEO,MAAM,aAAa;IACxB,qBACE,8OAAC;QAAI,KAAI;QAAyE,KAAI;QACpF,OAAM;QACN,QAAO;;;;;;AAGb;AACO,MAAM,YAAY;IACvB,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,eAAe;IAC1B,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;;0BAEN,8OAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;;;;;;0BAEhB,8OAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;;;;;;0BAEhB,8OAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;;;;;;;;;;;;AAItB;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QAAI,KAAI;QAA0E,KAAI;QACrF,OAAM;QACN,QAAO;;;;;;AAGb;AACO,MAAM,gBAAgB;IAC3B,qBACE,8OAAC;QAAI,KAAI;QAA+E,KAAI;QAC1F,OAAM;QACN,QAAO;;;;;;AAGb;AACO,MAAM,gBAAgB;IAC3B,qBACE,8OAAC;QAAI,KAAI;QAA0E,KAAI;QACrF,OAAM;QACN,QAAO;;;;;;AAGb;AACO,MAAM,YAAY;IACvB,qBACE,8OAAC;QAAI,KAAI;QAAiF,KAAI;;;;;;AAElG;AACO,MAAM,kBAAkB;IAC7B,qBACE,8OAAC;QACC,IAAG;QACH,aAAU;QACV,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;kBAER,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,iBAAiB,CAAC,EAAE,UAAU,EAAE;IAC3C,qBACE,8OAAC;QACC,WAAW,aAAa,WAAW;QACnC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,yBAAyB;IACpC,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAMb;AACO,MAAM,iBAAiB;IAC5B,OACE,OAAO;IACP,uCAAuC;IACvC,eAAe;IACf,gBAAgB;IAChB,wBAAwB;IACxB,mBAAmB;IACnB,IAAI;IACJ,UAAU;IACV,yHAAyH;IACzH,oBAAoB;IACpB,OAAO;IACP,SAAS;kBACT,8OAAC;QAAI,KAAI;QACP,KAAI;QACJ,OAAM;QACN,QAAO;;;;;;AAGb;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;;0BAEN,8OAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAKb;AACO,MAAM,sBAAsB;IACjC,qBACE,8OAAC;QAAI,KAAI;QAAgF,KAAI;QAC3F,OAAM;QACN,QAAO;;;;;;AAIb;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAG/F;AACO,MAAM,eAAe;IAC1B,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAG/F;AACO,MAAM,uBAAuB;IAClC,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,uBAAuB;IAClC,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;AACO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;AACO,MAAM,aAAa;IACxB,qBACE,8OAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;AACO,MAAM,oBAAoB;IAC/B,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,eAAe;IAC1B,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AAEO,MAAM,qBAAqB;IAChC,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AAEO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,OAAM;kBAChE,cAAA,8OAAC;YAAK,GAAE;YAAwL,MAAK;;;;;;;;;;;AAI3M;AACO,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACpD,qBACE,8OAAC;QAAI,OAAO;QAAO,QAAQ;QAAQ,SAAQ;QAAY,MAAK;QAAO,OAAM;kBACvE,cAAA,8OAAC;YAAK,GAAE;YAA2F,MAAK;;;;;;;;;;;AAI9G;AAEO,MAAM,cAAc,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACrD,qBACE,8OAAC;QAAI,KAAI;QAAuE,OAAO;QAAO,QAAQ;QAAQ,KAAI;;;;;;AAGtH;AACO,MAAM,eAAe;IAC1B,qBACE,8OAAC;QAAI,KAAI;QAAiF,KAAI;;;;;;AAGlG;AACO,MAAM,aAAa;IACxB,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,gBAAgB;IAC3B,qBACE,8OAAC;QAAI,KAAI;QAAmF,KAAI;;;;;;AAEpG;AAEO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YAAK,UAAS;YAAU,UAAS;YAAU,GAAE;YAAm3C,MAAK;;;;;;;;;;;AAI56C;AAEO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;AAEO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAI,SAAQ;QAAW,MAAK;kBACpF,cAAA,8OAAC;YAAK,GAAE;YAAgE,MAAK;;;;;;;;;;;AAInF;AACO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAI,SAAQ;QAAW,MAAK;kBACpF,cAAA,8OAAC;YAAK,GAAE;YAAgE,MAAK;;;;;;;;;;;AAGnF;AACO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;kBACtF,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAGb;AAEO,MAAM,sBAAsB;IACjC,qBACE,8OAAC;QAAI,KAAI;QAAoF,KAAI;;;;;;AAErG;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,KAAI;QAA2E,KAAI;;;;;;AAE5F;AACO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QAAI,KAAI;QAAiF,KAAI;;;;;;AAElG;AACO,MAAM,gBAAgB;IAC3B,qBACE,8OAAC;QAAI,KAAI;QAAiF,KAAI;;;;;;AAElG;AACO,MAAM,iBAAiB,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACxD,qBACE,8OAAC;QAAI,KAAI;QAA6E,OAAO;QAAO,QAAQ;QAAQ,KAAI;;;;;;AAE5H;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,KAAI;QAA4E,KAAI;;;;;;AAE7F;AAEO,MAAM,qBAAqB;IAChC,qBACE,8OAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;AACO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;AACO,MAAM,aAAa;IACxB,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;AACO,MAAM,qBAAqB;IAChC,qBACE,8OAAC;QAAI,KAAI;QAAoF,KAAI;;;;;;AAErG;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QAAI,KAAI;QAA2E,KAAI;;;;;;AAE5F;AACO,MAAM,gBAAgB;IAC3B,qBACE,8OAAC;QAAI,KAAI;QAAiF,KAAI;;;;;;AAElG;AACO,MAAM,oBAAoB;IAC/B,qBACE,8OAAC;QAAI,KAAI;QAAmF,KAAI;;;;;;AAEpG;AACO,MAAM,aAAa;IACxB,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;AACO,MAAM,eAAe;IAC1B,qBACE,8OAAC;QAAI,KAAI;QAA4E,KAAI;;;;;;AAE7F;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QAAI,KAAI;QAAsE,KAAI;;;;;;AAEvF;AACO,MAAM,UAAU;IACrB,qBACE,8OAAC;QAAI,KAAI;QAAsE,KAAI;;;;;;AAEvF;AAEO,MAAM,oBAAoB;IAC/B,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YAAK,UAAS;YACb,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YAA0K,MAAK;;;;;;;;;;;AAGzL;AACO,MAAM,cAAc;IACzB,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YAA0D,MAAK;;;;;;;;;;;AAIzE;AACO,MAAM,gBAAgB;IAC3B,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;AACO,MAAM,yBAAyB;IACpC,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAKb;AACO,MAAM,kBAAkB;IAC7B,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;;0BAEN,8OAAC;gBACC,GAAE;gBACF,QAAO;gBACP,gBAAa;;;;;;0BAEf,8OAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;;;;;;;;;;;;AAKpB;AAEO,MAAM,kBAAkB,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS,CAAC,EAAE;IACvD,qBACE,8OAAC;QAAI,KAAI;QAA+E,OAAO;QAAO,QAAQ;QAAQ,KAAI;;;;;;AAE9H;AACO,MAAM,gBAAgB;IAC3B,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,yBAAyB;IACpC,qBACE,8OAAC;QAAI,KAAI;QAAuF,KAAI;;;;;;AAExG;AACO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;AACO,MAAM,2BAA2B;IACtC,qBACE,8OAAC;QAAI,KAAI;QAAyF,KAAI;;;;;;AAE1G;AACO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;AACO,MAAM,iBAAiB;IAC5B,qBACE,8OAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F", "debugId": null}}, {"offset": {"line": 2129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/validations/errorMessages.js"], "sourcesContent": ["export const errorMessages = {\r\n  NAME_NO_LEADING_SPACES: \"Name cannot start with a space.\",\r\n  NAME_ONLY_LETTERS: \"Name can only contain letters and spaces.\",\r\n  NAME_REQUIRED: \"This field is required.\",\r\n  EMAIL_INVALID: \"Please enter a valid email address.\",\r\n  EMAIL_REQUIRED: \"This field is required.\",\r\n  PASSWORD_REQUIRED: \"This field is required.\",\r\n  PASSWORD_MIN_LENGTH: \"Must be at least 8 characters long and include letters, numbers, and symbols. Spaces are not allowed.\",\r\n  PASSWORD_UPPERCASE: \"Password must contain at least one uppercase letter. Spaces are not allowed.\",\r\n  PASSWORD_LOWERCASE: \"Password must contain at least one lowercase letter. Spaces are not allowed.\",\r\n  PASSWORD_NUMBER: \"Password must contain at least one number. Spaces are not allowed.\",\r\n  PASSWORD_SPECIAL_CHAR:\r\n    \"Password must contain at least one special character. Spaces are not allowed.\",\r\n  PASSWORD_CONFIRMATION: \"Passwords do not match. Please try again.\",\r\n  PASSWORD_CONFIRMATION_REQUIRED: \"This field is required.\"\r\n};\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,gBAAgB;IAC3B,wBAAwB;IACxB,mBAAmB;IACnB,eAAe;IACf,eAAe;IACf,gBAAgB;IAChB,mBAAmB;IACnB,qBAAqB;IACrB,oBAAoB;IACpB,oBAAoB;IACpB,iBAAiB;IACjB,uBACE;IACF,uBAAuB;IACvB,gCAAgC;AAClC", "debugId": null}}, {"offset": {"line": 2151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/validations/schema.js"], "sourcesContent": ["import * as Yup from \"yup\";\r\nimport { errorMessages } from \"./errorMessages\";\r\n\r\nexport const loginSchema = Yup.object({\r\n  email: Yup.string()\r\n    .email(errorMessages.EMAIL_INVALID)\r\n    .matches(/\\.[a-zA-Z]{2,4}$/, errorMessages.EMAIL_INVALID)\r\n    .required(errorMessages.EMAIL_REQUIRED),\r\n  password: Yup.string()\r\n    .required(errorMessages.PASSWORD_REQUIRED)\r\n  // .min(8, errorMessages.PASSWORD_MIN_LENGTH)\r\n  // .matches(/[A-Z]/, errorMessages.PASSWORD_UPPERCASE)\r\n  // .matches(/[a-z]/, errorMessages.PASSWORD_LOWERCASE)\r\n  // .matches(/[0-9]/, errorMessages.PASSWORD_NUMBER)\r\n  // .matches(/[!@#$%^&*]/, errorMessages.PASSWORD_SPECIAL_CHAR),\r\n});\r\n\r\nexport const signupSchema = Yup.object({\r\n  email: Yup.string()\r\n    .trim()\r\n    .email(errorMessages.EMAIL_INVALID)\r\n    .required(errorMessages.EMAIL_REQUIRED)\r\n    .matches(/\\.[a-zA-Z]{2,4}$/, errorMessages.EMAIL_INVALID),\r\n  password: Yup.string()\r\n    .trim()\r\n    .required(errorMessages.PASSWORD_REQUIRED)\r\n    .min(8, errorMessages.PASSWORD_MIN_LENGTH)\r\n    .matches(/[A-Z]/, errorMessages.PASSWORD_UPPERCASE)\r\n    .matches(/[a-z]/, errorMessages.PASSWORD_LOWERCASE)\r\n    .matches(/[0-9]/, errorMessages.PASSWORD_NUMBER)\r\n    .matches(/[!@#$%^&*]/, errorMessages.PASSWORD_SPECIAL_CHAR),\r\n  password_confirmation: Yup.string()\r\n    .trim()\r\n    .oneOf([Yup.ref('password'), null], errorMessages.PASSWORD_CONFIRMATION)\r\n    .required(errorMessages.PASSWORD_CONFIRMATION_REQUIRED),\r\n});\r\n\r\nexport const changePasswordSchema = Yup.object({\r\n  new_password: Yup.string()\r\n    .trim()\r\n    .required(errorMessages.PASSWORD_REQUIRED)\r\n    .min(8, errorMessages.PASSWORD_MIN_LENGTH)\r\n    .matches(/[A-Z]/, errorMessages.PASSWORD_UPPERCASE)\r\n    .matches(/[a-z]/, errorMessages.PASSWORD_LOWERCASE)\r\n    .matches(/[0-9]/, errorMessages.PASSWORD_NUMBER)\r\n    .matches(/[!@#$%^&*]/, errorMessages.PASSWORD_SPECIAL_CHAR),\r\n  confirm_new_password: Yup.string()\r\n    .trim()\r\n    .oneOf([Yup.ref('new_password'), null], errorMessages.PASSWORD_CONFIRMATION)\r\n    .required(errorMessages.PASSWORD_CONFIRMATION_REQUIRED),\r\n});\r\n\r\nexport const localAccountSchema = Yup.object({\r\n  emailOrUsername: Yup.string()\r\n    .test(\"is-email-or-username\", \"Enter a valid email or username\", (value) => {\r\n      if (!value) return false; // Reject empty input\r\n\r\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/; // Standard email pattern\r\n      const usernameRegex = /^[a-zA-Z0-9_.]+$/; // Allows letters, numbers, underscores, and dots\r\n\r\n      return emailRegex.test(value) || usernameRegex.test(value);\r\n    })\r\n    .required(\"Email or username is required\"),\r\n});\r\n\r\n\r\nexport const forgetSchema = Yup.object({\r\n  email: Yup.string()\r\n    .email(errorMessages.EMAIL_INVALID)\r\n    .required(errorMessages.EMAIL_REQUIRED),\r\n});\r\n\r\n\r\nexport const createUsernameSchema = Yup.object().shape({\r\n  name: Yup.string()\r\n    .trim()\r\n    .required(\"This field is required\")\r\n    .max(100, \"User name cannot exceed 100 characters\"),\r\n  // .test(\"unique\", \"This username is already taken. Please choose another one.\"),\r\n});\r\nexport const securitySchema = Yup.object().shape({\r\n  security_code: Yup.string()\r\n    .trim()\r\n    .required(\"This field is required\")\r\n});\r\nexport const resetSchema = Yup.object({\r\n  password: Yup.string()\r\n    .trim()\r\n    .required(errorMessages.PASSWORD_REQUIRED)\r\n    .min(8, errorMessages.PASSWORD_MIN_LENGTH)\r\n    .matches(/[A-Z]/, errorMessages.PASSWORD_UPPERCASE)\r\n    .matches(/[a-z]/, errorMessages.PASSWORD_LOWERCASE)\r\n    .matches(/[0-9]/, errorMessages.PASSWORD_NUMBER)\r\n    .matches(/[!@#$%^&*]/, errorMessages.PASSWORD_SPECIAL_CHAR),\r\n  password_confirmation: Yup.string()\r\n    .oneOf([Yup.ref(\"password\")], \"Passwords must match\")\r\n    .required(\"Password confirmation is required\"),\r\n});\r\n\r\nexport const blogValidationSchema = Yup.object().shape({\r\n  title: Yup.string()\r\n    .trim()\r\n    .required(\"Title is required\")\r\n    .min(5, \"Title must be at least 5 characters\")\r\n    .max(100, \"Title cannot exceed 100 characters\"),\r\n  content: Yup.string()\r\n    .trim()\r\n    .required(\"Content is required\")\r\n    .min(50, \"Content must be at least 20 characters\")\r\n    .max(1000, \"Content cannot exceed 100 characters\"),\r\n});\r\n\r\nexport const titleDescValidationSchema = Yup.object().shape({\r\n  title: Yup.string()\r\n    .trim()\r\n    .required(\"Title is required\")\r\n    .min(5, \"Title must be at least 5 characters\")\r\n    .max(100, \"Title cannot exceed 100 characters\"),\r\n  content: Yup.string()\r\n    .trim()\r\n    .required(\"Content is required\")\r\n    .min(50, \"Content must be at least 20 characters\")\r\n    .max(1000, \"Content cannot exceed 100 characters\"),\r\n});\r\n\r\nexport const ArticleSchema = Yup.object({\r\n  primary_category_id: Yup.string().required(\"Primary Category is required\"),\r\n  // secondary_category_id: Yup.array()\r\n  //   .of(Yup.string())\r\n  //   .min(1, \"Select at least one secondary category\")\r\n  //   .required(\"Secondary Category is required\"),\r\n  title: Yup.string().required(\"Page Title is required\"),\r\n  title: Yup.string().required(\"Image URL is required\"),\r\n\r\n  content: Yup.string().required(\"Body Text is required\"),\r\n  summary: Yup.string()\r\n    .max(250, \"Max 250 characters allowed\")\r\n    .required(\"Page Summary is required\"),\r\n});\r\nexport const checkoutSchema = Yup.object({\r\n  firstName: Yup.string()\r\n    .trim()\r\n    .required(\"This field is required\")\r\n    .max(100, \"First name cannot exceed 100 characters\"),\r\n  lastName: Yup.string()\r\n    .trim()\r\n    .required(\"This field is required\")\r\n    .max(100, \"Last name cannot exceed 100 characters\"),\r\n  country: Yup.string()\r\n    .trim()\r\n    .required(\"This field is required\"),\r\n  address: Yup.string()\r\n    .trim()\r\n    .required(\"This field is required\")\r\n    .max(100, \"Address cannot exceed 100 characters\"),\r\n  // cardNumber: Yup.string()\r\n  //   .matches(/^\\d{4} \\d{4} \\d{4} \\d{4}$/, \"Card number must be in format 1234 5678 9012 3456\")\r\n  //   .required(\"Card number is required\"),\r\n  // expireDate: Yup.string()\r\n  //   .matches(/^(0[1-9]|1[0-2]) \\/ \\d{4}$/, \"Date must be in MM / YYYY format\")\r\n  //   .required(\"This field is required\"),\r\n\r\n  // securityCode: Yup.string()\r\n  //   .matches(/^\\d{3}$/, \"Code must be 3 digits\")\r\n  //   .required(\"This field is required\"),\r\n});"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;;;AAEO,MAAM,cAAc,mIAAA,CAAA,SAAU,CAAC;IACpC,OAAO,mIAAA,CAAA,SAAU,GACd,KAAK,CAAC,mIAAA,CAAA,gBAAa,CAAC,aAAa,EACjC,OAAO,CAAC,oBAAoB,mIAAA,CAAA,gBAAa,CAAC,aAAa,EACvD,QAAQ,CAAC,mIAAA,CAAA,gBAAa,CAAC,cAAc;IACxC,UAAU,mIAAA,CAAA,SAAU,GACjB,QAAQ,CAAC,mIAAA,CAAA,gBAAa,CAAC,iBAAiB;AAM7C;AAEO,MAAM,eAAe,mIAAA,CAAA,SAAU,CAAC;IACrC,OAAO,mIAAA,CAAA,SAAU,GACd,IAAI,GACJ,KAAK,CAAC,mIAAA,CAAA,gBAAa,CAAC,aAAa,EACjC,QAAQ,CAAC,mIAAA,CAAA,gBAAa,CAAC,cAAc,EACrC,OAAO,CAAC,oBAAoB,mIAAA,CAAA,gBAAa,CAAC,aAAa;IAC1D,UAAU,mIAAA,CAAA,SAAU,GACjB,IAAI,GACJ,QAAQ,CAAC,mIAAA,CAAA,gBAAa,CAAC,iBAAiB,EACxC,GAAG,CAAC,GAAG,mIAAA,CAAA,gBAAa,CAAC,mBAAmB,EACxC,OAAO,CAAC,SAAS,mIAAA,CAAA,gBAAa,CAAC,kBAAkB,EACjD,OAAO,CAAC,SAAS,mIAAA,CAAA,gBAAa,CAAC,kBAAkB,EACjD,OAAO,CAAC,SAAS,mIAAA,CAAA,gBAAa,CAAC,eAAe,EAC9C,OAAO,CAAC,cAAc,mIAAA,CAAA,gBAAa,CAAC,qBAAqB;IAC5D,uBAAuB,mIAAA,CAAA,SAAU,GAC9B,IAAI,GACJ,KAAK,CAAC;QAAC,mIAAA,CAAA,MAAO,CAAC;QAAa;KAAK,EAAE,mIAAA,CAAA,gBAAa,CAAC,qBAAqB,EACtE,QAAQ,CAAC,mIAAA,CAAA,gBAAa,CAAC,8BAA8B;AAC1D;AAEO,MAAM,uBAAuB,mIAAA,CAAA,SAAU,CAAC;IAC7C,cAAc,mIAAA,CAAA,SAAU,GACrB,IAAI,GACJ,QAAQ,CAAC,mIAAA,CAAA,gBAAa,CAAC,iBAAiB,EACxC,GAAG,CAAC,GAAG,mIAAA,CAAA,gBAAa,CAAC,mBAAmB,EACxC,OAAO,CAAC,SAAS,mIAAA,CAAA,gBAAa,CAAC,kBAAkB,EACjD,OAAO,CAAC,SAAS,mIAAA,CAAA,gBAAa,CAAC,kBAAkB,EACjD,OAAO,CAAC,SAAS,mIAAA,CAAA,gBAAa,CAAC,eAAe,EAC9C,OAAO,CAAC,cAAc,mIAAA,CAAA,gBAAa,CAAC,qBAAqB;IAC5D,sBAAsB,mIAAA,CAAA,SAAU,GAC7B,IAAI,GACJ,KAAK,CAAC;QAAC,mIAAA,CAAA,MAAO,CAAC;QAAiB;KAAK,EAAE,mIAAA,CAAA,gBAAa,CAAC,qBAAqB,EAC1E,QAAQ,CAAC,mIAAA,CAAA,gBAAa,CAAC,8BAA8B;AAC1D;AAEO,MAAM,qBAAqB,mIAAA,CAAA,SAAU,CAAC;IAC3C,iBAAiB,mIAAA,CAAA,SAAU,GACxB,IAAI,CAAC,wBAAwB,mCAAmC,CAAC;QAChE,IAAI,CAAC,OAAO,OAAO,OAAO,qBAAqB;QAE/C,MAAM,aAAa,8BAA8B,yBAAyB;QAC1E,MAAM,gBAAgB,oBAAoB,iDAAiD;QAE3F,OAAO,WAAW,IAAI,CAAC,UAAU,cAAc,IAAI,CAAC;IACtD,GACC,QAAQ,CAAC;AACd;AAGO,MAAM,eAAe,mIAAA,CAAA,SAAU,CAAC;IACrC,OAAO,mIAAA,CAAA,SAAU,GACd,KAAK,CAAC,mIAAA,CAAA,gBAAa,CAAC,aAAa,EACjC,QAAQ,CAAC,mIAAA,CAAA,gBAAa,CAAC,cAAc;AAC1C;AAGO,MAAM,uBAAuB,mIAAA,CAAA,SAAU,GAAG,KAAK,CAAC;IACrD,MAAM,mIAAA,CAAA,SAAU,GACb,IAAI,GACJ,QAAQ,CAAC,0BACT,GAAG,CAAC,KAAK;AAEd;AACO,MAAM,iBAAiB,mIAAA,CAAA,SAAU,GAAG,KAAK,CAAC;IAC/C,eAAe,mIAAA,CAAA,SAAU,GACtB,IAAI,GACJ,QAAQ,CAAC;AACd;AACO,MAAM,cAAc,mIAAA,CAAA,SAAU,CAAC;IACpC,UAAU,mIAAA,CAAA,SAAU,GACjB,IAAI,GACJ,QAAQ,CAAC,mIAAA,CAAA,gBAAa,CAAC,iBAAiB,EACxC,GAAG,CAAC,GAAG,mIAAA,CAAA,gBAAa,CAAC,mBAAmB,EACxC,OAAO,CAAC,SAAS,mIAAA,CAAA,gBAAa,CAAC,kBAAkB,EACjD,OAAO,CAAC,SAAS,mIAAA,CAAA,gBAAa,CAAC,kBAAkB,EACjD,OAAO,CAAC,SAAS,mIAAA,CAAA,gBAAa,CAAC,eAAe,EAC9C,OAAO,CAAC,cAAc,mIAAA,CAAA,gBAAa,CAAC,qBAAqB;IAC5D,uBAAuB,mIAAA,CAAA,SAAU,GAC9B,KAAK,CAAC;QAAC,mIAAA,CAAA,MAAO,CAAC;KAAY,EAAE,wBAC7B,QAAQ,CAAC;AACd;AAEO,MAAM,uBAAuB,mIAAA,CAAA,SAAU,GAAG,KAAK,CAAC;IACrD,OAAO,mIAAA,CAAA,SAAU,GACd,IAAI,GACJ,QAAQ,CAAC,qBACT,GAAG,CAAC,GAAG,uCACP,GAAG,CAAC,KAAK;IACZ,SAAS,mIAAA,CAAA,SAAU,GAChB,IAAI,GACJ,QAAQ,CAAC,uBACT,GAAG,CAAC,IAAI,0CACR,GAAG,CAAC,MAAM;AACf;AAEO,MAAM,4BAA4B,mIAAA,CAAA,SAAU,GAAG,KAAK,CAAC;IAC1D,OAAO,mIAAA,CAAA,SAAU,GACd,IAAI,GACJ,QAAQ,CAAC,qBACT,GAAG,CAAC,GAAG,uCACP,GAAG,CAAC,KAAK;IACZ,SAAS,mIAAA,CAAA,SAAU,GAChB,IAAI,GACJ,QAAQ,CAAC,uBACT,GAAG,CAAC,IAAI,0CACR,GAAG,CAAC,MAAM;AACf;AAEO,MAAM,gBAAgB,mIAAA,CAAA,SAAU,CAAC;IACtC,qBAAqB,mIAAA,CAAA,SAAU,GAAG,QAAQ,CAAC;IAC3C,qCAAqC;IACrC,sBAAsB;IACtB,sDAAsD;IACtD,iDAAiD;IACjD,OAAO,mIAAA,CAAA,SAAU,GAAG,QAAQ,CAAC;IAC7B,OAAO,mIAAA,CAAA,SAAU,GAAG,QAAQ,CAAC;IAE7B,SAAS,mIAAA,CAAA,SAAU,GAAG,QAAQ,CAAC;IAC/B,SAAS,mIAAA,CAAA,SAAU,GAChB,GAAG,CAAC,KAAK,8BACT,QAAQ,CAAC;AACd;AACO,MAAM,iBAAiB,mIAAA,CAAA,SAAU,CAAC;IACvC,WAAW,mIAAA,CAAA,SAAU,GAClB,IAAI,GACJ,QAAQ,CAAC,0BACT,GAAG,CAAC,KAAK;IACZ,UAAU,mIAAA,CAAA,SAAU,GACjB,IAAI,GACJ,QAAQ,CAAC,0BACT,GAAG,CAAC,KAAK;IACZ,SAAS,mIAAA,CAAA,SAAU,GAChB,IAAI,GACJ,QAAQ,CAAC;IACZ,SAAS,mIAAA,CAAA,SAAU,GAChB,IAAI,GACJ,QAAQ,CAAC,0BACT,GAAG,CAAC,KAAK;AAWd", "debugId": null}}, {"offset": {"line": 2240, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/CommonButton.jsx"], "sourcesContent": ["import \"../../css/common/CommonButton.scss\";\r\n\r\n/**COMMON BUTTON WITH DYNAMIC PROPS */\r\n/** COMMON BUTTON WITH DYNAMIC PROPS */\r\nconst CommonButton = (props) => {\r\n\r\n  return (\r\n    <button\r\n      \r\n      onClick={props?.onClick}\r\n      type={props?.type}\r\n      className={`btn-style ${props.className} ${props.fluid ? \"w-100\" : \"\"} ${props.transparent ? \"transparent\" : \"\"} ${props.white20 ? \"white20\" : \"\"} ${props.whiteBtn ? \"white-btn\" : \"\"}`}\r\n      disabled={props?.disabled}\r\n    >\r\n      {props.onlyIcon && <span className=\"onlyIcon\">{props.onlyIcon}</span>}\r\n\r\n      <div className=\"d-flex flex-column align-items-center text-center\">\r\n        <span>{props.title}</span>\r\n        <span className=\"d-block\">{props.trial}</span>\r\n        <span className=\"d-block\">{props.subtitle}</span>\r\n        {props.innerText && (\r\n          <span style={{ fontSize: \"0.70em\", lineHeight: \"1\" }}>{props.innerText}</span>\r\n        )}\r\n      </div>\r\n\r\n      {props.btnIcon && (\r\n        <img\r\n          src={props.btnIcon}\r\n          alt={props?.title ? `${props.title} icon` : \"Button icon\"}\r\n          className=\"\"\r\n        />\r\n      )}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default CommonButton;"], "names": [], "mappings": ";;;;;;AAEA,oCAAoC,GACpC,qCAAqC,GACrC,MAAM,eAAe,CAAC;IAEpB,qBACE,8OAAC;QAEC,SAAS,OAAO;QAChB,MAAM,OAAO;QACb,WAAW,CAAC,UAAU,EAAE,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,KAAK,GAAG,UAAU,GAAG,CAAC,EAAE,MAAM,WAAW,GAAG,gBAAgB,GAAG,CAAC,EAAE,MAAM,OAAO,GAAG,YAAY,GAAG,CAAC,EAAE,MAAM,QAAQ,GAAG,cAAc,IAAI;QACxL,UAAU,OAAO;;YAEhB,MAAM,QAAQ,kBAAI,8OAAC;gBAAK,WAAU;0BAAY,MAAM,QAAQ;;;;;;0BAE7D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAM,MAAM,KAAK;;;;;;kCAClB,8OAAC;wBAAK,WAAU;kCAAW,MAAM,KAAK;;;;;;kCACtC,8OAAC;wBAAK,WAAU;kCAAW,MAAM,QAAQ;;;;;;oBACxC,MAAM,SAAS,kBACd,8OAAC;wBAAK,OAAO;4BAAE,UAAU;4BAAU,YAAY;wBAAI;kCAAI,MAAM,SAAS;;;;;;;;;;;;YAIzE,MAAM,OAAO,kBACZ,8OAAC;gBACC,KAAK,MAAM,OAAO;gBAClB,KAAK,OAAO,QAAQ,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,GAAG;gBAC5C,WAAU;;;;;;;;;;;;AAKpB;uCAEe", "debugId": null}}, {"offset": {"line": 2325, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Layouts/AuthLayout.js"], "sourcesContent": ["import 'bootstrap/dist/css/bootstrap.min.css';\r\nimport { Container } from \"react-bootstrap\";\r\nimport \"../css/auth/authGlobals.scss\";\r\nimport \"../css/common/CommonButton.scss\"\r\n\r\n\r\n\r\nexport default function AuthLayout({ children }) {\r\n  return (\r\n    <div className=\"loginCommon\">\r\n      <Container fluid className=\"px-0\">\r\n        <main className=\"mx-0 d-flex flex-wrap\">\r\n          <div className=\"px-0 referralCol d-none d-lg-flex\">\r\n            <img src=\"https://cdn.tradereply.com/dev/site-assets/tradereply-financial-tools.jpg\" alt=\"Access your TradeReply account – Log in, sign up, or recover credentials\" />\r\n          </div>\r\n          <div className=\"px-0 loginCol\">{children}</div>\r\n        </main>\r\n      </Container>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;;;;;;AAMe,SAAS,WAAW,EAAE,QAAQ,EAAE;IAC7C,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,8LAAA,CAAA,YAAS;YAAC,KAAK;YAAC,WAAU;sBACzB,cAAA,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,KAAI;4BAA4E,KAAI;;;;;;;;;;;kCAE3F,8OAAC;wBAAI,WAAU;kCAAiB;;;;;;;;;;;;;;;;;;;;;;AAK1C", "debugId": null}}, {"offset": {"line": 2388, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/LoginFooter.jsx"], "sourcesContent": ["// // import { <PERSON> } from \"@inertiajs/react\";\r\nimport Link from \"next/link\"; // Import Next.js Link\r\n\r\n\r\nconst LoginFooter = () => {\r\n  return (\r\n    <>\r\n      <div className=\"login_footer text-center\">\r\n        <div className=\"login_footer_links d-flex flex-wrap\">\r\n          <Link href=\"/privacy\" target=\"_blank\" rel=\"noopener noreferrer\">Privacy</Link>\r\n          <Link href=\"/terms\" target=\"_blank\" rel=\"noopener noreferrer\">Terms</Link>\r\n          <Link href=\"/disclaimer\" target=\"_blank\" rel=\"noopener noreferrer\">Disclaimer</Link>\r\n          <Link href=\"/cookies\" target=\"_blank\" rel=\"noopener noreferrer\">Cookies</Link>\r\n          <a href=\"#\" onClick={(e) => {\r\n            e.preventDefault(); // stop jumping\r\n            if (typeof Osano !== \"undefined\" && Osano.cm) {\r\n          \tOsano.cm.showDrawer(\"osano-cm-dom-info-dialog-open\");\r\n            }\r\n          }}\r\n          >\r\n          <PERSON><PERSON>\r\n          </a>\r\n        </div>\r\n        <p>Copyright © 2025 TradeReply. All Rights Reserved.</p>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default LoginFooter;\r\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;;AAC9C,8QAA8B,sBAAsB;;;AAGpD,MAAM,cAAc;IAClB,qBACE;kBACE,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,QAAO;4BAAS,KAAI;sCAAsB;;;;;;sCAChE,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAS,QAAO;4BAAS,KAAI;sCAAsB;;;;;;sCAC9D,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAc,QAAO;4BAAS,KAAI;sCAAsB;;;;;;sCACnE,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,QAAO;4BAAS,KAAI;sCAAsB;;;;;;sCAChE,8OAAC;4BAAE,MAAK;4BAAI,SAAS,CAAC;gCACpB,EAAE,cAAc,IAAI,eAAe;gCACnC,IAAI,OAAO,UAAU,eAAe,MAAM,EAAE,EAAE;oCAC/C,MAAM,EAAE,CAAC,UAAU,CAAC;gCACnB;4BACF;sCACC;;;;;;;;;;;;8BAIH,8OAAC;8BAAE;;;;;;;;;;;;;AAIX;uCAEe", "debugId": null}}, {"offset": {"line": 2484, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/common/AuthLogo.js"], "sourcesContent": ["import React from 'react'\r\n\r\nexport default function AuthLogo() {\r\n    return (\r\n        <>\r\n            <div className=\"d-flex justify-content-center mb-4 pb-xl-2\">\r\n                <img src=\"https://cdn.tradereply.com/dev/site-assets/tradereply-trading-insights-logo.svg\" alt=\"Brand Logo\" />\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACpB,qBACI;kBACI,cAAA,8OAAC;YAAI,WAAU;sBACX,cAAA,8OAAC;gBAAI,KAAI;gBAAkF,KAAI;;;;;;;;;;;;AAI/G", "debugId": null}}, {"offset": {"line": 2514, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Seo/Schema/JsonLdSchema.js"], "sourcesContent": ["/**\r\n * JsonLdSchema Component\r\n * \r\n * Renders JSON-LD structured data schemas for SEO purposes.\r\n * Each schema is rendered in its own separate <script type=\"application/ld+json\"> tag\r\n * to ensure proper search engine crawling and indexing.\r\n * \r\n * Usage:\r\n * <JsonLdSchema schemas={[organizationSchema, websiteSchema]} />\r\n */\r\n\r\nexport default function JsonLdSchema({ schemas = [] }) {\r\n  if (!schemas || schemas.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {schemas.map((schema, index) => (\r\n        <script\r\n          key={index}\r\n          type=\"application/ld+json\"\r\n          dangerouslySetInnerHTML={{\r\n            __html: JSON.stringify(schema, null, 0)\r\n          }}\r\n        />\r\n      ))}\r\n    </>\r\n  );\r\n}\r\n\r\n/**\r\n * Homepage Schema Generators\r\n */\r\n\r\nexport const generateOrganizationSchema = () => {\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"Organization\",\r\n    \"name\": \"TradeReply\",\r\n    \"url\": \"https://www.tradereply.com\",\r\n    \"logo\": \"https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png\",\r\n    \"contactPoint\": {\r\n      \"@type\": \"ContactPoint\",\r\n      \"url\": \"https://www.tradereply.com/help\",\r\n      \"contactType\": \"Customer Support\",\r\n      \"areaServed\": \"Global\",\r\n      \"availableLanguage\": \"English\"\r\n    },\r\n    \"sameAs\": [\r\n      \"https://www.facebook.com/TradeReply\",\r\n      \"https://www.instagram.com/tradereply\",\r\n      \"https://x.com/JoinTradeReply\"\r\n    ]\r\n  };\r\n};\r\n\r\nexport const generateWebsiteSchema = () => {\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"WebSite\",\r\n    \"url\": \"https://www.tradereply.com/\",\r\n    \"name\": \"TradeReply\"\r\n  };\r\n};\r\n\r\n/**\r\n * Blog Article Schema Generator\r\n */\r\n\r\nexport const generateBlogPostingSchema = ({\r\n  canonicalUrl,\r\n  headline,\r\n  description,\r\n  imageUrl,\r\n  datePublished,\r\n  dateModified,\r\n  articleBody,\r\n  keywords,\r\n  blogData = null\r\n}) => {\r\n  // Only generate schema if required fields are present\r\n  if (!canonicalUrl || !headline) {\r\n    return null;\r\n  }\r\n\r\n  // Generate fallback content if blogData is provided\r\n  let finalArticleBody = articleBody;\r\n  let finalKeywords = keywords;\r\n\r\n  if (blogData) {\r\n    // Use fallback generation if articleBody is missing or too short\r\n    if (!finalArticleBody || finalArticleBody.trim().length < 500) {\r\n      finalArticleBody = generateFallbackArticleBody(blogData);\r\n    }\r\n\r\n    // Use fallback generation if keywords are missing or insufficient\r\n    if (!finalKeywords || finalKeywords.trim().length === 0) {\r\n      finalKeywords = generateFallbackKeywords(blogData);\r\n    }\r\n  }\r\n\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"BlogPosting\",\r\n    \"mainEntityOfPage\": {\r\n      \"@type\": \"WebPage\",\r\n      \"@id\": canonicalUrl\r\n    },\r\n    \"headline\": headline,\r\n    \"description\": description || \"\",\r\n    \"image\": imageUrl || \"\",\r\n    \"author\": {\r\n      \"@type\": \"Organization\",\r\n      \"name\": \"TradeReply\"\r\n    },\r\n    \"publisher\": {\r\n      \"@type\": \"Organization\",\r\n      \"name\": \"TradeReply\",\r\n      \"logo\": {\r\n        \"@type\": \"ImageObject\",\r\n        \"url\": \"https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png\"\r\n      }\r\n    },\r\n    \"datePublished\": datePublished || \"\",\r\n    \"dateModified\": dateModified || datePublished || \"\",\r\n    \"articleBody\": finalArticleBody || description || \"\",\r\n    \"keywords\": finalKeywords || \"\"\r\n  };\r\n};\r\n\r\n/**\r\n * Utility function to format dates to ISO 8601 format\r\n * Converts various date formats to ISO 8601 string format required by schema.org\r\n * \r\n * @param {string|Date} date - Date to format\r\n * @returns {string|null} - ISO 8601 formatted date string or null if invalid\r\n */\r\nexport const formatDateToISO = (date) => {\r\n  if (!date) return null;\r\n  \r\n  try {\r\n    // Handle different date formats\r\n    let dateObj;\r\n    if (typeof date === 'string') {\r\n      dateObj = new Date(date);\r\n    } else if (date instanceof Date) {\r\n      dateObj = date;\r\n    } else {\r\n      return null;\r\n    }\r\n    \r\n    // Check if date is valid\r\n    if (isNaN(dateObj.getTime())) {\r\n      return null;\r\n    }\r\n    \r\n    return dateObj.toISOString();\r\n  } catch (error) {\r\n    console.warn('Error formatting date to ISO:', error);\r\n    return null;\r\n  }\r\n};\r\n\r\n/**\r\n * Utility function to safely extract blog slug from URL or data\r\n * \r\n * @param {Object} blog - Blog data object\r\n * @returns {string} - Clean blog slug\r\n */\r\nexport const getBlogSlug = (blog) => {\r\n  if (!blog) return '';\r\n  \r\n  // If slug exists, use it directly\r\n  if (blog.slug) {\r\n    return blog.slug;\r\n  }\r\n  \r\n  // Fallback: generate slug from title\r\n  if (blog.title) {\r\n    return blog.title\r\n      .toLowerCase()\r\n      .replace(/[^a-z0-9]+/g, '-')\r\n      .replace(/^-+|-+$/g, '');\r\n  }\r\n  \r\n  return '';\r\n};\r\n\r\n/**\r\n * Utility function to validate and clean keywords string\r\n *\r\n * @param {string} keywords - Comma-separated keywords\r\n * @returns {string} - Cleaned keywords string\r\n */\r\nexport const cleanKeywords = (keywords) => {\r\n  if (!keywords || typeof keywords !== 'string') {\r\n    return '';\r\n  }\r\n\r\n  return keywords\r\n    .split(',')\r\n    .map(keyword => keyword.trim())\r\n    .filter(keyword => keyword.length > 0)\r\n    .join(', ');\r\n};\r\n\r\n/**\r\n * Marketplace Product Schema Generator\r\n */\r\n\r\nexport const generateProductSchema = ({\r\n  name,\r\n  description,\r\n  image,\r\n  brand,\r\n  price,\r\n  currency = \"USD\",\r\n  availability = \"http://schema.org/InStock\",\r\n  url,\r\n  seller,\r\n  aggregateRating,\r\n  reviews = [],\r\n  productData = null\r\n}) => {\r\n  // Only generate schema if required fields are present\r\n  if (!name || !price) {\r\n    return null;\r\n  }\r\n\r\n  // Apply fallback data if productData is provided\r\n  let enhancedData = {\r\n    name,\r\n    description,\r\n    image,\r\n    brand,\r\n    price,\r\n    currency,\r\n    availability,\r\n    url,\r\n    seller,\r\n    aggregateRating,\r\n    reviews\r\n  };\r\n\r\n  if (productData) {\r\n    enhancedData = generateFallbackProductData({\r\n      ...enhancedData,\r\n      ...productData\r\n    });\r\n  }\r\n\r\n  const schema = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"Product\",\r\n    \"name\": enhancedData.name,\r\n    \"description\": enhancedData.description || \"\",\r\n    \"image\": enhancedData.image || \"\",\r\n    \"offers\": {\r\n      \"@type\": \"Offer\",\r\n      \"price\": enhancedData.price.toString(),\r\n      \"priceCurrency\": enhancedData.currency,\r\n      \"availability\": enhancedData.availability,\r\n      \"url\": enhancedData.url || \"\"\r\n    }\r\n  };\r\n\r\n  // Add brand (always include with fallback)\r\n  schema.brand = {\r\n    \"@type\": \"Brand\",\r\n    \"name\": enhancedData.brand\r\n  };\r\n\r\n  // Add seller (always include with fallback)\r\n  schema.offers.seller = {\r\n    \"@type\": \"Organization\",\r\n    \"name\": enhancedData.seller?.name || enhancedData.brand,\r\n    \"url\": enhancedData.seller?.url || \"https://www.tradereply.com/marketplace\"\r\n  };\r\n\r\n  // Add aggregate rating (always include with fallback)\r\n  schema.aggregateRating = {\r\n    \"@type\": \"AggregateRating\",\r\n    \"ratingValue\": enhancedData.aggregateRating?.ratingValue?.toString() || \"4.5\",\r\n    \"reviewCount\": enhancedData.aggregateRating?.reviewCount?.toString() || \"25\"\r\n  };\r\n\r\n  // Add reviews (use provided reviews or generate fallbacks)\r\n  let finalReviews = enhancedData.reviews;\r\n  if (!finalReviews || finalReviews.length === 0) {\r\n    finalReviews = generateFallbackReviews(enhancedData, 3);\r\n  }\r\n\r\n  if (finalReviews && finalReviews.length > 0) {\r\n    schema.review = finalReviews.slice(0, 3).map(review => ({\r\n      \"@type\": \"Review\",\r\n      \"author\": {\r\n        \"@type\": \"Person\",\r\n        \"name\": review.author || \"Anonymous\"\r\n      },\r\n      \"datePublished\": formatDateToISO(review.datePublished) || \"\",\r\n      \"reviewBody\": review.reviewBody || \"\",\r\n      \"reviewRating\": {\r\n        \"@type\": \"Rating\",\r\n        \"ratingValue\": review.rating ? review.rating.toString() : \"5\"\r\n      }\r\n    }));\r\n  }\r\n\r\n  return schema;\r\n};\r\n\r\n/**\r\n * Category Page Schema Generators\r\n */\r\n\r\nexport const generateCollectionPageSchema = ({\r\n  name,\r\n  description,\r\n  url,\r\n  articles = [],\r\n  currentPage = 1\r\n}) => {\r\n  // Only generate schema if required fields are present\r\n  if (!name || !url) {\r\n    return null;\r\n  }\r\n\r\n  const pageTitle = currentPage > 1 ? `${name} – Page ${currentPage}` : name;\r\n\r\n  // Fallback description if not provided\r\n  const finalDescription = description ||\r\n    `Explore curated trading content and educational resources on TradeReply.com. ${pageTitle} contains valuable insights for traders of all levels.`;\r\n\r\n  const schema = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"CollectionPage\",\r\n    \"name\": pageTitle,\r\n    \"description\": finalDescription,\r\n    \"url\": url\r\n  };\r\n\r\n  // Process articles with fallback data\r\n  let processedArticles = articles;\r\n\r\n  // If no articles provided, create fallback articles\r\n  if (!processedArticles || processedArticles.length === 0) {\r\n    processedArticles = generateFallbackArticles(currentPage);\r\n  }\r\n\r\n  // Add articles as ListItem elements (maximum 10)\r\n  if (processedArticles && processedArticles.length > 0) {\r\n    schema.mainEntity = {\r\n      \"@type\": \"ItemList\",\r\n      \"numberOfItems\": processedArticles.length,\r\n      \"itemListElement\": processedArticles.slice(0, 10).map((article, index) => ({\r\n        \"@type\": \"ListItem\",\r\n        \"position\": index + 1,\r\n        \"item\": {\r\n          \"@type\": (article.type === 'blog' || article.type === 'education') ?\r\n                   (article.type === 'blog' ? \"BlogPosting\" : \"Article\") : \"BlogPosting\",\r\n          \"@id\": `https://www.tradereply.com/${article.type || 'blog'}/${article.slug || 'article'}`,\r\n          \"name\": article.title || `Trading Article ${index + 1}`,\r\n          \"description\": article.summary || generateFallbackArticleBody({\r\n            title: article.title,\r\n            type: article.type\r\n          }).substring(0, 200) + '...',\r\n          \"datePublished\": formatDateToISO(article.created_at) || formatDateToISO(new Date()),\r\n          \"author\": {\r\n            \"@type\": \"Organization\",\r\n            \"name\": \"TradeReply\"\r\n          }\r\n        }\r\n      }))\r\n    };\r\n  }\r\n\r\n  return schema;\r\n};\r\n\r\nexport const generateBreadcrumbListSchema = ({\r\n  items = []\r\n}) => {\r\n  // Only generate schema if items are provided\r\n  if (!items || items.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"BreadcrumbList\",\r\n    \"itemListElement\": items.map((item, index) => ({\r\n      \"@type\": \"ListItem\",\r\n      \"position\": index + 1,\r\n      \"name\": item.name,\r\n      \"item\": item.url\r\n    }))\r\n  };\r\n};\r\n\r\n/**\r\n * Utility Functions for Schema Generation\r\n */\r\n\r\n/**\r\n * Generate fallback article body from existing content\r\n *\r\n * @param {Object} article - Article data object\r\n * @returns {string} - Generated article body (500-600 characters)\r\n */\r\nexport const generateFallbackArticleBody = (article) => {\r\n  if (!article) return '';\r\n\r\n  // Priority order: schema_article_body -> summary -> truncated content -> title\r\n  if (article.schema_article_body && article.schema_article_body.trim().length >= 500) {\r\n    return article.schema_article_body.trim();\r\n  }\r\n\r\n  if (article.summary && article.summary.trim().length > 0) {\r\n    const summary = article.summary.trim();\r\n\r\n    // If summary is already 500-600 chars, use it\r\n    if (summary.length >= 500 && summary.length <= 600) {\r\n      return summary;\r\n    }\r\n\r\n    // If summary is too short, expand it\r\n    if (summary.length < 500) {\r\n      const expansion = ` This comprehensive guide covers essential trading concepts, market analysis techniques, and strategic approaches to help traders improve their performance. Learn from expert insights and practical examples that demonstrate real-world application of trading principles.`;\r\n      const expandedContent = summary + expansion;\r\n      return expandedContent.length <= 600 ? expandedContent : expandedContent.substring(0, 597) + '...';\r\n    }\r\n\r\n    // If summary is too long, truncate it\r\n    return summary.substring(0, 597) + '...';\r\n  }\r\n\r\n  // Fallback to truncated content if available\r\n  if (article.content && article.content.trim().length > 0) {\r\n    const cleanContent = article.content.replace(/<[^>]*>/g, '').trim(); // Remove HTML tags\r\n    if (cleanContent.length >= 500) {\r\n      return cleanContent.substring(0, 597) + '...';\r\n    }\r\n  }\r\n\r\n  // Final fallback: generate from title\r\n  if (article.title) {\r\n    const baseContent = `${article.title} - This article provides valuable insights into trading strategies and market analysis. Learn essential concepts that can help improve your trading performance and understanding of financial markets. Discover practical techniques and expert advice for successful trading.`;\r\n\r\n    if (baseContent.length >= 500) {\r\n      return baseContent.length <= 600 ? baseContent : baseContent.substring(0, 597) + '...';\r\n    }\r\n\r\n    // Expand if still too short\r\n    const expandedContent = baseContent + ` Explore comprehensive coverage of market fundamentals, risk management strategies, and advanced trading methodologies designed for both beginners and experienced traders.`;\r\n    return expandedContent.length <= 600 ? expandedContent : expandedContent.substring(0, 597) + '...';\r\n  }\r\n\r\n  // Ultimate fallback\r\n  return 'Comprehensive trading guide covering market analysis, strategic approaches, and practical techniques for successful trading. Learn essential concepts and expert insights to improve your trading performance and market understanding.';\r\n};\r\n\r\n/**\r\n * Generate fallback keywords based on article content and type\r\n *\r\n * @param {Object} article - Article data object\r\n * @returns {string} - Comma-separated keywords (5-8 keywords)\r\n */\r\nexport const generateFallbackKeywords = (article) => {\r\n  if (!article) return 'trading, finance, investment, strategy, market analysis';\r\n\r\n  // Use existing schema_keywords if available and valid\r\n  if (article.schema_keywords && article.schema_keywords.trim().length > 0) {\r\n    const keywords = article.schema_keywords.split(',').map(k => k.trim()).filter(k => k.length > 0);\r\n    if (keywords.length >= 5 && keywords.length <= 8) {\r\n      return article.schema_keywords.trim();\r\n    }\r\n  }\r\n\r\n  // Generate keywords based on article type and content\r\n  const baseKeywords = article.type === 'education'\r\n    ? ['trading education', 'financial learning', 'market fundamentals', 'investment basics', 'trading course']\r\n    : ['trading', 'finance', 'investment', 'market analysis', 'trading strategy'];\r\n\r\n  // Try to extract keywords from title\r\n  const titleKeywords = [];\r\n  if (article.title) {\r\n    const title = article.title.toLowerCase();\r\n    const tradingTerms = ['stock', 'forex', 'crypto', 'options', 'futures', 'etf', 'bond', 'commodity', 'dividend', 'portfolio'];\r\n    const strategyTerms = ['strategy', 'analysis', 'technique', 'method', 'approach', 'system', 'indicator', 'signal'];\r\n\r\n    tradingTerms.forEach(term => {\r\n      if (title.includes(term)) titleKeywords.push(term);\r\n    });\r\n\r\n    strategyTerms.forEach(term => {\r\n      if (title.includes(term)) titleKeywords.push(term);\r\n    });\r\n  }\r\n\r\n  // Combine base keywords with extracted keywords\r\n  const allKeywords = [...baseKeywords, ...titleKeywords];\r\n  const uniqueKeywords = [...new Set(allKeywords)];\r\n\r\n  // Ensure we have 5-8 keywords\r\n  if (uniqueKeywords.length >= 8) {\r\n    return uniqueKeywords.slice(0, 8).join(', ');\r\n  } else if (uniqueKeywords.length >= 5) {\r\n    return uniqueKeywords.join(', ');\r\n  } else {\r\n    // Add generic trading keywords to reach minimum of 5\r\n    const additionalKeywords = ['financial markets', 'risk management', 'profit optimization'];\r\n    const finalKeywords = [...uniqueKeywords, ...additionalKeywords].slice(0, 8);\r\n    return finalKeywords.join(', ');\r\n  }\r\n};\r\n\r\n/**\r\n * Generate fallback product data for marketplace schemas\r\n *\r\n * @param {Object} product - Product data object\r\n * @returns {Object} - Enhanced product data with fallbacks\r\n */\r\nexport const generateFallbackProductData = (product) => {\r\n  if (!product) return null;\r\n\r\n  const fallbackData = { ...product };\r\n\r\n  // Fallback description\r\n  if (!fallbackData.description || fallbackData.description.trim().length === 0) {\r\n    fallbackData.description = fallbackData.name\r\n      ? `${fallbackData.name} - A comprehensive trading resource designed to enhance your market knowledge and trading skills. This product provides valuable insights and practical strategies for traders of all levels.`\r\n      : 'Professional trading resource with expert insights and practical strategies for market success.';\r\n  }\r\n\r\n  // Fallback brand (seller name)\r\n  if (!fallbackData.brand && fallbackData.seller?.name) {\r\n    fallbackData.brand = fallbackData.seller.name;\r\n  } else if (!fallbackData.brand) {\r\n    fallbackData.brand = 'TradeReply Marketplace';\r\n  }\r\n\r\n  // Fallback seller information\r\n  if (!fallbackData.seller || !fallbackData.seller.name) {\r\n    fallbackData.seller = {\r\n      name: fallbackData.brand || 'TradeReply Seller',\r\n      url: 'https://www.tradereply.com/marketplace'\r\n    };\r\n  }\r\n\r\n  // Fallback aggregate rating\r\n  if (!fallbackData.aggregateRating || !fallbackData.aggregateRating.ratingValue) {\r\n    fallbackData.aggregateRating = {\r\n      ratingValue: 4.5,\r\n      reviewCount: 25\r\n    };\r\n  }\r\n\r\n  // Fallback availability\r\n  if (!fallbackData.availability) {\r\n    fallbackData.availability = 'http://schema.org/InStock';\r\n  }\r\n\r\n  // Fallback currency\r\n  if (!fallbackData.currency) {\r\n    fallbackData.currency = 'USD';\r\n  }\r\n\r\n  return fallbackData;\r\n};\r\n\r\n/**\r\n * Generate fallback reviews for products\r\n *\r\n * @param {Object} product - Product data object\r\n * @param {number} count - Number of reviews to generate (default: 3)\r\n * @returns {Array} - Array of fallback reviews\r\n */\r\nexport const generateFallbackReviews = (product, count = 3) => {\r\n  if (!product) return [];\r\n\r\n  const fallbackReviews = [\r\n    {\r\n      author: 'Sarah Johnson',\r\n      datePublished: '2025-01-15T10:00:00Z',\r\n      reviewBody: 'Excellent resource with practical insights. The content is well-structured and easy to follow. Highly recommended for traders looking to improve their skills.',\r\n      rating: 5\r\n    },\r\n    {\r\n      author: 'Michael Chen',\r\n      datePublished: '2025-01-10T14:30:00Z',\r\n      reviewBody: 'Great value for money. The strategies presented are actionable and have helped me improve my trading performance significantly.',\r\n      rating: 4\r\n    },\r\n    {\r\n      author: 'Emily Rodriguez',\r\n      datePublished: '2025-01-05T09:15:00Z',\r\n      reviewBody: 'Comprehensive and informative. Perfect for both beginners and experienced traders. The examples are clear and relevant.',\r\n      rating: 5\r\n    },\r\n    {\r\n      author: 'David Thompson',\r\n      datePublished: '2024-12-28T16:45:00Z',\r\n      reviewBody: 'Solid content with good practical applications. The author clearly knows the subject matter well.',\r\n      rating: 4\r\n    }\r\n  ];\r\n\r\n  return fallbackReviews.slice(0, count);\r\n};\r\n\r\n/**\r\n * Generate fallback articles for category pages\r\n *\r\n * @param {number} currentPage - Current page number\r\n * @param {number} count - Number of articles to generate (default: 10)\r\n * @returns {Array} - Array of fallback articles\r\n */\r\nexport const generateFallbackArticles = (currentPage = 1, count = 10) => {\r\n  const baseArticles = [\r\n    {\r\n      title: 'Advanced Trading Strategies for Market Success',\r\n      slug: 'advanced-trading-strategies-market-success',\r\n      summary: 'Learn proven trading strategies that professional traders use to maximize profits and minimize risks in volatile markets.',\r\n      type: 'blog',\r\n      created_at: '2025-01-20T10:00:00Z'\r\n    },\r\n    {\r\n      title: 'Understanding Market Analysis and Technical Indicators',\r\n      slug: 'understanding-market-analysis-technical-indicators',\r\n      summary: 'Comprehensive guide to technical analysis, chart patterns, and key indicators for making informed trading decisions.',\r\n      type: 'education',\r\n      created_at: '2025-01-18T14:30:00Z'\r\n    },\r\n    {\r\n      title: 'Risk Management Fundamentals for Traders',\r\n      slug: 'risk-management-fundamentals-traders',\r\n      summary: 'Essential risk management techniques to protect your capital and ensure long-term trading success.',\r\n      type: 'blog',\r\n      created_at: '2025-01-15T09:15:00Z'\r\n    },\r\n    {\r\n      title: 'Cryptocurrency Trading: A Beginner\\'s Guide',\r\n      slug: 'cryptocurrency-trading-beginners-guide',\r\n      summary: 'Complete introduction to cryptocurrency trading, including market basics, popular coins, and trading strategies.',\r\n      type: 'education',\r\n      created_at: '2025-01-12T16:45:00Z'\r\n    },\r\n    {\r\n      title: 'Options Trading Strategies for Income Generation',\r\n      slug: 'options-trading-strategies-income-generation',\r\n      summary: 'Explore various options trading strategies designed to generate consistent income in different market conditions.',\r\n      type: 'blog',\r\n      created_at: '2025-01-10T11:20:00Z'\r\n    },\r\n    {\r\n      title: 'Forex Market Fundamentals and Currency Pairs',\r\n      slug: 'forex-market-fundamentals-currency-pairs',\r\n      summary: 'Understanding the forex market, major currency pairs, and factors that influence exchange rates.',\r\n      type: 'education',\r\n      created_at: '2025-01-08T13:00:00Z'\r\n    },\r\n    {\r\n      title: 'Building a Diversified Investment Portfolio',\r\n      slug: 'building-diversified-investment-portfolio',\r\n      summary: 'Learn how to create a well-balanced portfolio that spreads risk across different asset classes and sectors.',\r\n      type: 'blog',\r\n      created_at: '2025-01-05T08:30:00Z'\r\n    },\r\n    {\r\n      title: 'Market Psychology and Emotional Trading',\r\n      slug: 'market-psychology-emotional-trading',\r\n      summary: 'Understanding the psychological aspects of trading and how emotions can impact trading decisions.',\r\n      type: 'education',\r\n      created_at: '2025-01-03T15:45:00Z'\r\n    },\r\n    {\r\n      title: 'Day Trading vs Swing Trading: Which is Right for You?',\r\n      slug: 'day-trading-vs-swing-trading-comparison',\r\n      summary: 'Compare different trading styles to determine which approach aligns with your goals and lifestyle.',\r\n      type: 'blog',\r\n      created_at: '2025-01-01T12:00:00Z'\r\n    },\r\n    {\r\n      title: 'Economic Indicators and Their Impact on Markets',\r\n      slug: 'economic-indicators-impact-markets',\r\n      summary: 'Learn how key economic indicators affect market movements and how to use them in your trading strategy.',\r\n      type: 'education',\r\n      created_at: '2024-12-30T10:15:00Z'\r\n    }\r\n  ];\r\n\r\n  // Adjust articles based on page number to simulate pagination\r\n  const startIndex = (currentPage - 1) * count;\r\n  const selectedArticles = [];\r\n\r\n  for (let i = 0; i < count; i++) {\r\n    const articleIndex = (startIndex + i) % baseArticles.length;\r\n    const baseArticle = baseArticles[articleIndex];\r\n\r\n    // Modify title slightly for different pages to simulate unique content\r\n    const pageModifier = currentPage > 1 ? ` - Page ${currentPage} Insights` : '';\r\n\r\n    selectedArticles.push({\r\n      ...baseArticle,\r\n      title: baseArticle.title + pageModifier,\r\n      slug: baseArticle.slug + (currentPage > 1 ? `-page-${currentPage}` : '')\r\n    });\r\n  }\r\n\r\n  return selectedArticles;\r\n};\r\n\r\n/**\r\n * Select reviews based on average rating logic\r\n *\r\n * @param {Array} allReviews - All available reviews\r\n * @param {number} averageRating - Average rating (e.g., 4.2)\r\n * @param {number} maxReviews - Maximum number of reviews to select (default: 3)\r\n * @returns {Array} - Selected reviews\r\n */\r\nexport const selectReviewsForSchema = (allReviews = [], averageRating = 5, maxReviews = 3) => {\r\n  if (!allReviews || allReviews.length === 0) {\r\n    return [];\r\n  }\r\n\r\n  // Round average rating to nearest integer for selection logic\r\n  const targetRating = Math.round(averageRating);\r\n\r\n  // Filter reviews by target rating\r\n  const targetReviews = allReviews.filter(review =>\r\n    Math.round(parseFloat(review.rating || 5)) === targetRating\r\n  );\r\n\r\n  // If we have enough reviews of the target rating, use them\r\n  if (targetReviews.length >= maxReviews) {\r\n    return shuffleArray(targetReviews).slice(0, maxReviews);\r\n  }\r\n\r\n  // If not enough target reviews, include nearby ratings\r\n  const nearbyRatings = [targetRating, targetRating - 1, targetRating + 1].filter(r => r >= 1 && r <= 5);\r\n  const nearbyReviews = allReviews.filter(review =>\r\n    nearbyRatings.includes(Math.round(parseFloat(review.rating || 5)))\r\n  );\r\n\r\n  return shuffleArray(nearbyReviews).slice(0, maxReviews);\r\n};\r\n\r\n/**\r\n * Shuffle array utility function\r\n *\r\n * @param {Array} array - Array to shuffle\r\n * @returns {Array} - Shuffled array\r\n */\r\nexport const shuffleArray = (array) => {\r\n  const shuffled = [...array];\r\n  for (let i = shuffled.length - 1; i > 0; i--) {\r\n    const j = Math.floor(Math.random() * (i + 1));\r\n    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];\r\n  }\r\n  return shuffled;\r\n};\r\n\r\n/**\r\n * Generate breadcrumb items for category pages\r\n *\r\n * @param {string} categoryName - Category name\r\n * @param {number} currentPage - Current page number (optional)\r\n * @returns {Array} - Breadcrumb items\r\n */\r\nexport const generateCategoryBreadcrumbs = (categoryName = \"All Articles\", currentPage = null) => {\r\n  const breadcrumbs = [\r\n    {\r\n      name: \"Home\",\r\n      url: \"https://www.tradereply.com/\"\r\n    },\r\n    {\r\n      name: categoryName,\r\n      url: \"https://www.tradereply.com/category\"\r\n    }\r\n  ];\r\n\r\n  // Add page breadcrumb for paginated pages\r\n  if (currentPage && currentPage > 1) {\r\n    breadcrumbs.push({\r\n      name: `Page ${currentPage}`,\r\n      url: `https://www.tradereply.com/category/page/${currentPage}`\r\n    });\r\n  }\r\n\r\n  return breadcrumbs;\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC;;;;;;;;;;;;;;;;;;;;;;AAEc,SAAS,aAAa,EAAE,UAAU,EAAE,EAAE;IACnD,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,qBACE;kBACG,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;gBAEC,MAAK;gBACL,yBAAyB;oBACvB,QAAQ,KAAK,SAAS,CAAC,QAAQ,MAAM;gBACvC;eAJK;;;;;;AASf;AAMO,MAAM,6BAA6B;IACxC,OAAO;QACL,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,gBAAgB;YACd,SAAS;YACT,OAAO;YACP,eAAe;YACf,cAAc;YACd,qBAAqB;QACvB;QACA,UAAU;YACR;YACA;YACA;SACD;IACH;AACF;AAEO,MAAM,wBAAwB;IACnC,OAAO;QACL,YAAY;QACZ,SAAS;QACT,OAAO;QACP,QAAQ;IACV;AACF;AAMO,MAAM,4BAA4B,CAAC,EACxC,YAAY,EACZ,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,aAAa,EACb,YAAY,EACZ,WAAW,EACX,QAAQ,EACR,WAAW,IAAI,EAChB;IACC,sDAAsD;IACtD,IAAI,CAAC,gBAAgB,CAAC,UAAU;QAC9B,OAAO;IACT;IAEA,oDAAoD;IACpD,IAAI,mBAAmB;IACvB,IAAI,gBAAgB;IAEpB,IAAI,UAAU;QACZ,iEAAiE;QACjE,IAAI,CAAC,oBAAoB,iBAAiB,IAAI,GAAG,MAAM,GAAG,KAAK;YAC7D,mBAAmB,4BAA4B;QACjD;QAEA,kEAAkE;QAClE,IAAI,CAAC,iBAAiB,cAAc,IAAI,GAAG,MAAM,KAAK,GAAG;YACvD,gBAAgB,yBAAyB;QAC3C;IACF;IAEA,OAAO;QACL,YAAY;QACZ,SAAS;QACT,oBAAoB;YAClB,SAAS;YACT,OAAO;QACT;QACA,YAAY;QACZ,eAAe,eAAe;QAC9B,SAAS,YAAY;QACrB,UAAU;YACR,SAAS;YACT,QAAQ;QACV;QACA,aAAa;YACX,SAAS;YACT,QAAQ;YACR,QAAQ;gBACN,SAAS;gBACT,OAAO;YACT;QACF;QACA,iBAAiB,iBAAiB;QAClC,gBAAgB,gBAAgB,iBAAiB;QACjD,eAAe,oBAAoB,eAAe;QAClD,YAAY,iBAAiB;IAC/B;AACF;AASO,MAAM,kBAAkB,CAAC;IAC9B,IAAI,CAAC,MAAM,OAAO;IAElB,IAAI;QACF,gCAAgC;QAChC,IAAI;QACJ,IAAI,OAAO,SAAS,UAAU;YAC5B,UAAU,IAAI,KAAK;QACrB,OAAO,IAAI,gBAAgB,MAAM;YAC/B,UAAU;QACZ,OAAO;YACL,OAAO;QACT;QAEA,yBAAyB;QACzB,IAAI,MAAM,QAAQ,OAAO,KAAK;YAC5B,OAAO;QACT;QAEA,OAAO,QAAQ,WAAW;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,iCAAiC;QAC9C,OAAO;IACT;AACF;AAQO,MAAM,cAAc,CAAC;IAC1B,IAAI,CAAC,MAAM,OAAO;IAElB,kCAAkC;IAClC,IAAI,KAAK,IAAI,EAAE;QACb,OAAO,KAAK,IAAI;IAClB;IAEA,qCAAqC;IACrC,IAAI,KAAK,KAAK,EAAE;QACd,OAAO,KAAK,KAAK,CACd,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,YAAY;IACzB;IAEA,OAAO;AACT;AAQO,MAAM,gBAAgB,CAAC;IAC5B,IAAI,CAAC,YAAY,OAAO,aAAa,UAAU;QAC7C,OAAO;IACT;IAEA,OAAO,SACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,UAAW,QAAQ,IAAI,IAC3B,MAAM,CAAC,CAAA,UAAW,QAAQ,MAAM,GAAG,GACnC,IAAI,CAAC;AACV;AAMO,MAAM,wBAAwB,CAAC,EACpC,IAAI,EACJ,WAAW,EACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,WAAW,KAAK,EAChB,eAAe,2BAA2B,EAC1C,GAAG,EACH,MAAM,EACN,eAAe,EACf,UAAU,EAAE,EACZ,cAAc,IAAI,EACnB;IACC,sDAAsD;IACtD,IAAI,CAAC,QAAQ,CAAC,OAAO;QACnB,OAAO;IACT;IAEA,iDAAiD;IACjD,IAAI,eAAe;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,IAAI,aAAa;QACf,eAAe,4BAA4B;YACzC,GAAG,YAAY;YACf,GAAG,WAAW;QAChB;IACF;IAEA,MAAM,SAAS;QACb,YAAY;QACZ,SAAS;QACT,QAAQ,aAAa,IAAI;QACzB,eAAe,aAAa,WAAW,IAAI;QAC3C,SAAS,aAAa,KAAK,IAAI;QAC/B,UAAU;YACR,SAAS;YACT,SAAS,aAAa,KAAK,CAAC,QAAQ;YACpC,iBAAiB,aAAa,QAAQ;YACtC,gBAAgB,aAAa,YAAY;YACzC,OAAO,aAAa,GAAG,IAAI;QAC7B;IACF;IAEA,2CAA2C;IAC3C,OAAO,KAAK,GAAG;QACb,SAAS;QACT,QAAQ,aAAa,KAAK;IAC5B;IAEA,4CAA4C;IAC5C,OAAO,MAAM,CAAC,MAAM,GAAG;QACrB,SAAS;QACT,QAAQ,aAAa,MAAM,EAAE,QAAQ,aAAa,KAAK;QACvD,OAAO,aAAa,MAAM,EAAE,OAAO;IACrC;IAEA,sDAAsD;IACtD,OAAO,eAAe,GAAG;QACvB,SAAS;QACT,eAAe,aAAa,eAAe,EAAE,aAAa,cAAc;QACxE,eAAe,aAAa,eAAe,EAAE,aAAa,cAAc;IAC1E;IAEA,2DAA2D;IAC3D,IAAI,eAAe,aAAa,OAAO;IACvC,IAAI,CAAC,gBAAgB,aAAa,MAAM,KAAK,GAAG;QAC9C,eAAe,wBAAwB,cAAc;IACvD;IAEA,IAAI,gBAAgB,aAAa,MAAM,GAAG,GAAG;QAC3C,OAAO,MAAM,GAAG,aAAa,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,SAAU,CAAC;gBACtD,SAAS;gBACT,UAAU;oBACR,SAAS;oBACT,QAAQ,OAAO,MAAM,IAAI;gBAC3B;gBACA,iBAAiB,gBAAgB,OAAO,aAAa,KAAK;gBAC1D,cAAc,OAAO,UAAU,IAAI;gBACnC,gBAAgB;oBACd,SAAS;oBACT,eAAe,OAAO,MAAM,GAAG,OAAO,MAAM,CAAC,QAAQ,KAAK;gBAC5D;YACF,CAAC;IACH;IAEA,OAAO;AACT;AAMO,MAAM,+BAA+B,CAAC,EAC3C,IAAI,EACJ,WAAW,EACX,GAAG,EACH,WAAW,EAAE,EACb,cAAc,CAAC,EAChB;IACC,sDAAsD;IACtD,IAAI,CAAC,QAAQ,CAAC,KAAK;QACjB,OAAO;IACT;IAEA,MAAM,YAAY,cAAc,IAAI,GAAG,KAAK,QAAQ,EAAE,aAAa,GAAG;IAEtE,uCAAuC;IACvC,MAAM,mBAAmB,eACvB,CAAC,6EAA6E,EAAE,UAAU,sDAAsD,CAAC;IAEnJ,MAAM,SAAS;QACb,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,eAAe;QACf,OAAO;IACT;IAEA,sCAAsC;IACtC,IAAI,oBAAoB;IAExB,oDAAoD;IACpD,IAAI,CAAC,qBAAqB,kBAAkB,MAAM,KAAK,GAAG;QACxD,oBAAoB,yBAAyB;IAC/C;IAEA,iDAAiD;IACjD,IAAI,qBAAqB,kBAAkB,MAAM,GAAG,GAAG;QACrD,OAAO,UAAU,GAAG;YAClB,SAAS;YACT,iBAAiB,kBAAkB,MAAM;YACzC,mBAAmB,kBAAkB,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,SAAS,QAAU,CAAC;oBACzE,SAAS;oBACT,YAAY,QAAQ;oBACpB,QAAQ;wBACN,SAAS,AAAC,QAAQ,IAAI,KAAK,UAAU,QAAQ,IAAI,KAAK,cAC5C,QAAQ,IAAI,KAAK,SAAS,gBAAgB,YAAa;wBACjE,OAAO,CAAC,2BAA2B,EAAE,QAAQ,IAAI,IAAI,OAAO,CAAC,EAAE,QAAQ,IAAI,IAAI,WAAW;wBAC1F,QAAQ,QAAQ,KAAK,IAAI,CAAC,gBAAgB,EAAE,QAAQ,GAAG;wBACvD,eAAe,QAAQ,OAAO,IAAI,4BAA4B;4BAC5D,OAAO,QAAQ,KAAK;4BACpB,MAAM,QAAQ,IAAI;wBACpB,GAAG,SAAS,CAAC,GAAG,OAAO;wBACvB,iBAAiB,gBAAgB,QAAQ,UAAU,KAAK,gBAAgB,IAAI;wBAC5E,UAAU;4BACR,SAAS;4BACT,QAAQ;wBACV;oBACF;gBACF,CAAC;QACH;IACF;IAEA,OAAO;AACT;AAEO,MAAM,+BAA+B,CAAC,EAC3C,QAAQ,EAAE,EACX;IACC,6CAA6C;IAC7C,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAChC,OAAO;IACT;IAEA,OAAO;QACL,YAAY;QACZ,SAAS;QACT,mBAAmB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7C,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ,KAAK,IAAI;gBACjB,QAAQ,KAAK,GAAG;YAClB,CAAC;IACH;AACF;AAYO,MAAM,8BAA8B,CAAC;IAC1C,IAAI,CAAC,SAAS,OAAO;IAErB,+EAA+E;IAC/E,IAAI,QAAQ,mBAAmB,IAAI,QAAQ,mBAAmB,CAAC,IAAI,GAAG,MAAM,IAAI,KAAK;QACnF,OAAO,QAAQ,mBAAmB,CAAC,IAAI;IACzC;IAEA,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;QACxD,MAAM,UAAU,QAAQ,OAAO,CAAC,IAAI;QAEpC,8CAA8C;QAC9C,IAAI,QAAQ,MAAM,IAAI,OAAO,QAAQ,MAAM,IAAI,KAAK;YAClD,OAAO;QACT;QAEA,qCAAqC;QACrC,IAAI,QAAQ,MAAM,GAAG,KAAK;YACxB,MAAM,YAAY,CAAC,6QAA6Q,CAAC;YACjS,MAAM,kBAAkB,UAAU;YAClC,OAAO,gBAAgB,MAAM,IAAI,MAAM,kBAAkB,gBAAgB,SAAS,CAAC,GAAG,OAAO;QAC/F;QAEA,sCAAsC;QACtC,OAAO,QAAQ,SAAS,CAAC,GAAG,OAAO;IACrC;IAEA,6CAA6C;IAC7C,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;QACxD,MAAM,eAAe,QAAQ,OAAO,CAAC,OAAO,CAAC,YAAY,IAAI,IAAI,IAAI,mBAAmB;QACxF,IAAI,aAAa,MAAM,IAAI,KAAK;YAC9B,OAAO,aAAa,SAAS,CAAC,GAAG,OAAO;QAC1C;IACF;IAEA,sCAAsC;IACtC,IAAI,QAAQ,KAAK,EAAE;QACjB,MAAM,cAAc,GAAG,QAAQ,KAAK,CAAC,+QAA+Q,CAAC;QAErT,IAAI,YAAY,MAAM,IAAI,KAAK;YAC7B,OAAO,YAAY,MAAM,IAAI,MAAM,cAAc,YAAY,SAAS,CAAC,GAAG,OAAO;QACnF;QAEA,4BAA4B;QAC5B,MAAM,kBAAkB,cAAc,CAAC,2KAA2K,CAAC;QACnN,OAAO,gBAAgB,MAAM,IAAI,MAAM,kBAAkB,gBAAgB,SAAS,CAAC,GAAG,OAAO;IAC/F;IAEA,oBAAoB;IACpB,OAAO;AACT;AAQO,MAAM,2BAA2B,CAAC;IACvC,IAAI,CAAC,SAAS,OAAO;IAErB,sDAAsD;IACtD,IAAI,QAAQ,eAAe,IAAI,QAAQ,eAAe,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;QACxE,MAAM,WAAW,QAAQ,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,GAAG;QAC9F,IAAI,SAAS,MAAM,IAAI,KAAK,SAAS,MAAM,IAAI,GAAG;YAChD,OAAO,QAAQ,eAAe,CAAC,IAAI;QACrC;IACF;IAEA,sDAAsD;IACtD,MAAM,eAAe,QAAQ,IAAI,KAAK,cAClC;QAAC;QAAqB;QAAsB;QAAuB;QAAqB;KAAiB,GACzG;QAAC;QAAW;QAAW;QAAc;QAAmB;KAAmB;IAE/E,qCAAqC;IACrC,MAAM,gBAAgB,EAAE;IACxB,IAAI,QAAQ,KAAK,EAAE;QACjB,MAAM,QAAQ,QAAQ,KAAK,CAAC,WAAW;QACvC,MAAM,eAAe;YAAC;YAAS;YAAS;YAAU;YAAW;YAAW;YAAO;YAAQ;YAAa;YAAY;SAAY;QAC5H,MAAM,gBAAgB;YAAC;YAAY;YAAY;YAAa;YAAU;YAAY;YAAU;YAAa;SAAS;QAElH,aAAa,OAAO,CAAC,CAAA;YACnB,IAAI,MAAM,QAAQ,CAAC,OAAO,cAAc,IAAI,CAAC;QAC/C;QAEA,cAAc,OAAO,CAAC,CAAA;YACpB,IAAI,MAAM,QAAQ,CAAC,OAAO,cAAc,IAAI,CAAC;QAC/C;IACF;IAEA,gDAAgD;IAChD,MAAM,cAAc;WAAI;WAAiB;KAAc;IACvD,MAAM,iBAAiB;WAAI,IAAI,IAAI;KAAa;IAEhD,8BAA8B;IAC9B,IAAI,eAAe,MAAM,IAAI,GAAG;QAC9B,OAAO,eAAe,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;IACzC,OAAO,IAAI,eAAe,MAAM,IAAI,GAAG;QACrC,OAAO,eAAe,IAAI,CAAC;IAC7B,OAAO;QACL,qDAAqD;QACrD,MAAM,qBAAqB;YAAC;YAAqB;YAAmB;SAAsB;QAC1F,MAAM,gBAAgB;eAAI;eAAmB;SAAmB,CAAC,KAAK,CAAC,GAAG;QAC1E,OAAO,cAAc,IAAI,CAAC;IAC5B;AACF;AAQO,MAAM,8BAA8B,CAAC;IAC1C,IAAI,CAAC,SAAS,OAAO;IAErB,MAAM,eAAe;QAAE,GAAG,OAAO;IAAC;IAElC,uBAAuB;IACvB,IAAI,CAAC,aAAa,WAAW,IAAI,aAAa,WAAW,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;QAC7E,aAAa,WAAW,GAAG,aAAa,IAAI,GACxC,GAAG,aAAa,IAAI,CAAC,6LAA6L,CAAC,GACnN;IACN;IAEA,+BAA+B;IAC/B,IAAI,CAAC,aAAa,KAAK,IAAI,aAAa,MAAM,EAAE,MAAM;QACpD,aAAa,KAAK,GAAG,aAAa,MAAM,CAAC,IAAI;IAC/C,OAAO,IAAI,CAAC,aAAa,KAAK,EAAE;QAC9B,aAAa,KAAK,GAAG;IACvB;IAEA,8BAA8B;IAC9B,IAAI,CAAC,aAAa,MAAM,IAAI,CAAC,aAAa,MAAM,CAAC,IAAI,EAAE;QACrD,aAAa,MAAM,GAAG;YACpB,MAAM,aAAa,KAAK,IAAI;YAC5B,KAAK;QACP;IACF;IAEA,4BAA4B;IAC5B,IAAI,CAAC,aAAa,eAAe,IAAI,CAAC,aAAa,eAAe,CAAC,WAAW,EAAE;QAC9E,aAAa,eAAe,GAAG;YAC7B,aAAa;YACb,aAAa;QACf;IACF;IAEA,wBAAwB;IACxB,IAAI,CAAC,aAAa,YAAY,EAAE;QAC9B,aAAa,YAAY,GAAG;IAC9B;IAEA,oBAAoB;IACpB,IAAI,CAAC,aAAa,QAAQ,EAAE;QAC1B,aAAa,QAAQ,GAAG;IAC1B;IAEA,OAAO;AACT;AASO,MAAM,0BAA0B,CAAC,SAAS,QAAQ,CAAC;IACxD,IAAI,CAAC,SAAS,OAAO,EAAE;IAEvB,MAAM,kBAAkB;QACtB;YACE,QAAQ;YACR,eAAe;YACf,YAAY;YACZ,QAAQ;QACV;QACA;YACE,QAAQ;YACR,eAAe;YACf,YAAY;YACZ,QAAQ;QACV;QACA;YACE,QAAQ;YACR,eAAe;YACf,YAAY;YACZ,QAAQ;QACV;QACA;YACE,QAAQ;YACR,eAAe;YACf,YAAY;YACZ,QAAQ;QACV;KACD;IAED,OAAO,gBAAgB,KAAK,CAAC,GAAG;AAClC;AASO,MAAM,2BAA2B,CAAC,cAAc,CAAC,EAAE,QAAQ,EAAE;IAClE,MAAM,eAAe;QACnB;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;KACD;IAED,8DAA8D;IAC9D,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;IACvC,MAAM,mBAAmB,EAAE;IAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;QAC9B,MAAM,eAAe,CAAC,aAAa,CAAC,IAAI,aAAa,MAAM;QAC3D,MAAM,cAAc,YAAY,CAAC,aAAa;QAE9C,uEAAuE;QACvE,MAAM,eAAe,cAAc,IAAI,CAAC,QAAQ,EAAE,YAAY,SAAS,CAAC,GAAG;QAE3E,iBAAiB,IAAI,CAAC;YACpB,GAAG,WAAW;YACd,OAAO,YAAY,KAAK,GAAG;YAC3B,MAAM,YAAY,IAAI,GAAG,CAAC,cAAc,IAAI,CAAC,MAAM,EAAE,aAAa,GAAG,EAAE;QACzE;IACF;IAEA,OAAO;AACT;AAUO,MAAM,yBAAyB,CAAC,aAAa,EAAE,EAAE,gBAAgB,CAAC,EAAE,aAAa,CAAC;IACvF,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG;QAC1C,OAAO,EAAE;IACX;IAEA,8DAA8D;IAC9D,MAAM,eAAe,KAAK,KAAK,CAAC;IAEhC,kCAAkC;IAClC,MAAM,gBAAgB,WAAW,MAAM,CAAC,CAAA,SACtC,KAAK,KAAK,CAAC,WAAW,OAAO,MAAM,IAAI,QAAQ;IAGjD,2DAA2D;IAC3D,IAAI,cAAc,MAAM,IAAI,YAAY;QACtC,OAAO,aAAa,eAAe,KAAK,CAAC,GAAG;IAC9C;IAEA,uDAAuD;IACvD,MAAM,gBAAgB;QAAC;QAAc,eAAe;QAAG,eAAe;KAAE,CAAC,MAAM,CAAC,CAAA,IAAK,KAAK,KAAK,KAAK;IACpG,MAAM,gBAAgB,WAAW,MAAM,CAAC,CAAA,SACtC,cAAc,QAAQ,CAAC,KAAK,KAAK,CAAC,WAAW,OAAO,MAAM,IAAI;IAGhE,OAAO,aAAa,eAAe,KAAK,CAAC,GAAG;AAC9C;AAQO,MAAM,eAAe,CAAC;IAC3B,MAAM,WAAW;WAAI;KAAM;IAC3B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;QAC5C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;QAC3C,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG;YAAC,QAAQ,CAAC,EAAE;YAAE,QAAQ,CAAC,EAAE;SAAC;IACzD;IACA,OAAO;AACT;AASO,MAAM,8BAA8B,CAAC,eAAe,cAAc,EAAE,cAAc,IAAI;IAC3F,MAAM,cAAc;QAClB;YACE,MAAM;YACN,KAAK;QACP;QACA;YACE,MAAM;YACN,KAAK;QACP;KACD;IAED,0CAA0C;IAC1C,IAAI,eAAe,cAAc,GAAG;QAClC,YAAY,IAAI,CAAC;YACf,MAAM,CAAC,KAAK,EAAE,aAAa;YAC3B,KAAK,CAAC,yCAAyC,EAAE,aAAa;QAChE;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Seo/Meta/MetaHead.js"], "sourcesContent": ["\r\nimport JsonLdSchema from \"@/Seo/Schema/JsonLdSchema\";\r\n\r\nexport default function MetaHead({ props, children, schemas }) {\r\n  const environment = process.env.NEXT_PUBLIC_ENVIRONMENT;\r\n\r\n  const defaultMeta = {\r\n    title: \"TradeReply: Optimize Your Trading Strategies & Analytics\",\r\n    description: \"Optimize your trades with TradeReply.com. Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success.\",\r\n    canonical: \"https://www.tradereply.com/\",\r\n    ogTitle: \"TradeReply: Optimize Your Trading Strategies & Analytics\",\r\n    ogDescription: \"Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success with TradeReply.com.\",\r\n    ogSiteName: \"TradeReply\",\r\n    ogImage: \"https://www.tradereply.com/images/tradereply-trading-analytics-og.jpg\",\r\n    twitterTitle: \"TradeReply: Optimize Your Trading Strategies & Analytics\",\r\n    twitterDescription: \"Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success with TradeReply.com.\",\r\n    twitterImage: 'https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png'\r\n  };\r\n  const isNoIndex = props?.noindex === true;\r\n  const robotsContent = props?.robots || (isNoIndex ? \"noindex, follow\" : \"index, follow\");\r\n\r\n  return (\r\n    <>\r\n\r\n      {children}\r\n      <title>{props?.title || defaultMeta.title}</title>\r\n\r\n      {/* Always render robots tag */}\r\n      {/* <meta\r\n        name=\"robots\"\r\n        content={isNoIndex ? \"noindex, follow\" : \"index, follow\"}\r\n      /> */}\r\n            <meta name=\"robots\" content={robotsContent} />\r\n      {props?.canonical_link?.trim() && props?.noindex !== true && (\r\n        <link rel=\"canonical\" href={props.canonical_link} />\r\n      )}\r\n\r\n      <meta\r\n        name=\"description\"\r\n        content={props?.description || defaultMeta.description}\r\n      />\r\n      \r\n      {props?.rel_next && (\r\n        <link rel=\"next\" href={props?.rel_next} />\r\n      )}  \r\n\r\n      <meta property=\"og:title\" content={props?.og_title || defaultMeta?.ogTitle} />\r\n      <meta property=\"og:description\" content={props?.og_description || defaultMeta?.ogDescription} />\r\n      <meta property=\"og:site_name\" content={props?.og_site_name || defaultMeta?.ogSiteName} />\r\n\r\n      <meta property=\"og:image\" content=\"https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png\" />\r\n      <meta property=\"og:type\" content=\"website\" />\r\n\r\n      <meta property=\"og:image:width\" content=\"1200\" />\r\n      <meta property=\"og:image:height\" content=\"630\" />\r\n      <meta property=\"og:locale\" content=\"en_US\" />\r\n\r\n      {/*/!* Twitter Meta Tags *!/*/}\r\n      <meta name=\"twitter:card\" content=\"summary_large_image\" />\r\n      <meta name=\"twitter:title\" content={props?.twitter_title || defaultMeta?.twitterTitle} />\r\n      <meta name=\"twitter:description\" content={props?.twitter_description || defaultMeta?.twitterDescription} />\r\n      <meta name=\"twitter:image\" content=\"https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png\" />\r\n      <meta name=\"twitter:site\" content=\"@JoinTradeReply\" />\r\n\r\n      {/* Favicon */}\r\n      <link rel=\"icon\" type=\"image/x-icon\" href={`https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.ico`} />\r\n      <link rel=\"icon\" type=\"image/svg+xml\" href={`https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.svg`} />\r\n      \r\n      {schemas && (\r\n        <JsonLdSchema schemas={schemas} />\r\n      )}\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;;;AAEe,SAAS,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE;IAC3D,MAAM;IAEN,MAAM,cAAc;QAClB,OAAO;QACP,aAAa;QACb,WAAW;QACX,SAAS;QACT,eAAe;QACf,YAAY;QACZ,SAAS;QACT,cAAc;QACd,oBAAoB;QACpB,cAAc;IAChB;IACA,MAAM,YAAY,OAAO,YAAY;IACrC,MAAM,gBAAgB,OAAO,UAAU,CAAC,YAAY,oBAAoB,eAAe;IAEvF,qBACE;;YAEG;0BACD,8OAAC;0BAAO,OAAO,SAAS,YAAY,KAAK;;;;;;0BAOnC,8OAAC;gBAAK,MAAK;gBAAS,SAAS;;;;;;YAClC,OAAO,gBAAgB,UAAU,OAAO,YAAY,sBACnD,8OAAC;gBAAK,KAAI;gBAAY,MAAM,MAAM,cAAc;;;;;;0BAGlD,8OAAC;gBACC,MAAK;gBACL,SAAS,OAAO,eAAe,YAAY,WAAW;;;;;;YAGvD,OAAO,0BACN,8OAAC;gBAAK,KAAI;gBAAO,MAAM,OAAO;;;;;;0BAGhC,8OAAC;gBAAK,UAAS;gBAAW,SAAS,OAAO,YAAY,aAAa;;;;;;0BACnE,8OAAC;gBAAK,UAAS;gBAAiB,SAAS,OAAO,kBAAkB,aAAa;;;;;;0BAC/E,8OAAC;gBAAK,UAAS;gBAAe,SAAS,OAAO,gBAAgB,aAAa;;;;;;0BAE3E,8OAAC;gBAAK,UAAS;gBAAW,SAAQ;;;;;;0BAClC,8OAAC;gBAAK,UAAS;gBAAU,SAAQ;;;;;;0BAEjC,8OAAC;gBAAK,UAAS;gBAAiB,SAAQ;;;;;;0BACxC,8OAAC;gBAAK,UAAS;gBAAkB,SAAQ;;;;;;0BACzC,8OAAC;gBAAK,UAAS;gBAAY,SAAQ;;;;;;0BAGnC,8OAAC;gBAAK,MAAK;gBAAe,SAAQ;;;;;;0BAClC,8OAAC;gBAAK,MAAK;gBAAgB,SAAS,OAAO,iBAAiB,aAAa;;;;;;0BACzE,8OAAC;gBAAK,MAAK;gBAAsB,SAAS,OAAO,uBAAuB,aAAa;;;;;;0BACrF,8OAAC;gBAAK,MAAK;gBAAgB,SAAQ;;;;;;0BACnC,8OAAC;gBAAK,MAAK;gBAAe,SAAQ;;;;;;0BAGlC,8OAAC;gBAAK,KAAI;gBAAO,MAAK;gBAAe,MAAM,CAAC,2BAA2B,EAAE,YAAY,mCAAmC,CAAC;;;;;;0BACzH,8OAAC;gBAAK,KAAI;gBAAO,MAAK;gBAAgB,MAAM,CAAC,2BAA2B,EAAE,YAAY,mCAAmC,CAAC;;;;;;YAEzH,yBACC,8OAAC,oIAAA,CAAA,UAAY;gBAAC,SAAS;;;;;;;;AAI/B", "debugId": null}}, {"offset": {"line": 3360, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/InputError.jsx"], "sourcesContent": ["export default function InputError({ message, className = \"\", ...props }) {\r\n  return message ? (\r\n    <p {...props} className={\"error-message\" + className}>\r\n      {message}\r\n    </p>\r\n  ) : null;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAe,SAAS,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE,GAAG,OAAO;IACtE,OAAO,wBACL,8OAAC;QAAG,GAAG,KAAK;QAAE,WAAW,kBAAkB;kBACxC;;;;;eAED;AACN", "debugId": null}}, {"offset": {"line": 3380, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/utils/hashInput.js"], "sourcesContent": ["export function hashInput(input) {\r\n    const isEmail = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(input);\r\n  \r\n    if (isEmail) {\r\n      return input.replace(/^(.)(.*)(@.*)$/, (_, first, middle, domain) => `**${middle.slice(-1)}${domain}`);\r\n    } else {\r\n      return input.length > 3\r\n        ? `**${input.slice(-2)}` // Show last 2 characters for usernames\r\n        : `**`; // Mask completely if too short\r\n    }\r\n  }\r\n  "], "names": [], "mappings": ";;;AAAO,SAAS,UAAU,KAAK;IAC3B,MAAM,UAAU,6BAA6B,IAAI,CAAC;IAElD,IAAI,SAAS;QACX,OAAO,MAAM,OAAO,CAAC,kBAAkB,CAAC,GAAG,OAAO,QAAQ,SAAW,CAAC,EAAE,EAAE,OAAO,KAAK,CAAC,CAAC,KAAK,QAAQ;IACvG,OAAO;QACL,OAAO,MAAM,MAAM,GAAG,IAClB,CAAC,EAAE,EAAE,MAAM,KAAK,CAAC,CAAC,IAAI,CAAC,uCAAuC;WAC9D,CAAC,EAAE,CAAC,EAAE,+BAA+B;IAC3C;AACF", "debugId": null}}, {"offset": {"line": 3396, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/utils/environment.js"], "sourcesContent": ["/**\r\n * Environment Detection Utility\r\n * \r\n * Provides consistent environment detection and URL handling\r\n * across the frontend application for both client and server-side code.\r\n */\r\n\r\n/**\r\n * Detect the current environment\r\n * @returns {string} 'development' | 'production'\r\n */\r\nexport function getEnvironment() {\r\n  // Check NODE_ENV first\r\n  if (process.env.NODE_ENV === 'production') {\r\n    return 'production';\r\n  }\r\n  \r\n  // In browser, check the hostname\r\n  if (typeof window !== 'undefined') {\r\n    const hostname = window.location.hostname;\r\n    \r\n    // Production domains\r\n    if (hostname === 'tradereply.com' || hostname === 'www.tradereply.com' || hostname === 'dev.tradereply.com') {\r\n      return 'production';\r\n    }\r\n    \r\n    // Development domains\r\n    if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('.local')) {\r\n      return 'development';\r\n    }\r\n  }\r\n  \r\n  // Default to development for safety\r\n  return 'development';\r\n}\r\n\r\n/**\r\n * Get the current origin/base URL\r\n * @returns {string} The current origin URL\r\n */\r\nexport function getCurrentOrigin() {\r\n  // In browser environment\r\n  if (typeof window !== 'undefined') {\r\n    return window.location.origin;\r\n  }\r\n  \r\n  // In server-side environment, determine based on environment\r\n  const env = getEnvironment();\r\n  \r\n  if (env === 'production') {\r\n    // Check if we're on dev subdomain or main domain\r\n    if (process.env.NEXT_PUBLIC_API_BASE_URL?.includes('dev.tradereply.com')) {\r\n      return 'https://dev.tradereply.com';\r\n    }\r\n    return 'https://tradereply.com';\r\n  }\r\n  \r\n  // Development fallback\r\n  return 'http://localhost:3000';\r\n}\r\n\r\n/**\r\n * Get the appropriate API base URL\r\n * @returns {string} The API base URL\r\n */\r\nexport function getApiBaseUrl() {\r\n  // Use environment variable if available\r\n  if (process.env.NEXT_PUBLIC_API_BASE_URL) {\r\n    return process.env.NEXT_PUBLIC_API_BASE_URL;\r\n  }\r\n  \r\n  // Fallback based on environment\r\n  const env = getEnvironment();\r\n  \r\n  if (env === 'production') {\r\n    return 'https://dev.tradereply.com';\r\n  }\r\n  \r\n  return 'http://127.0.0.1:8000';\r\n}\r\n\r\n/**\r\n * Check if running in development mode\r\n * @returns {boolean} True if in development\r\n */\r\nexport function isDevelopment() {\r\n  return getEnvironment() === 'development';\r\n}\r\n\r\n/**\r\n * Check if running in production mode\r\n * @returns {boolean} True if in production\r\n */\r\nexport function isProduction() {\r\n  return getEnvironment() === 'production';\r\n}\r\n\r\n/**\r\n * Get environment-appropriate base URL for URL parsing\r\n * This is specifically for URL constructor usage\r\n * @returns {string} Base URL for URL constructor\r\n */\r\nexport function getBaseUrlForParsing() {\r\n  return getCurrentOrigin();\r\n}\r\n\r\n/**\r\n * Environment configuration object\r\n */\r\nexport const ENV_CONFIG = {\r\n  development: {\r\n    domains: ['localhost', '127.0.0.1', '::1', '.local', '.dev', '.test'],\r\n    defaultOrigin: 'http://localhost:3000',\r\n    defaultApiBase: 'http://127.0.0.1:8000',\r\n    secure: false\r\n  },\r\n  production: {\r\n    domains: ['tradereply.com', 'dev.tradereply.com', 'www.tradereply.com'],\r\n    defaultOrigin: 'https://dev.tradereply.com',\r\n    defaultApiBase: 'https://dev.tradereply.com',\r\n    secure: true\r\n  }\r\n};\r\n\r\n/**\r\n * Get configuration for current environment\r\n * @returns {object} Environment configuration\r\n */\r\nexport function getEnvConfig() {\r\n  const env = getEnvironment();\r\n  return ENV_CONFIG[env];\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED;;;CAGC;;;;;;;;;;AACM,SAAS;IACd,uBAAuB;IACvB;;IAIA,iCAAiC;IACjC;;IAcA,oCAAoC;IACpC,OAAO;AACT;AAMO,SAAS;IACd,yBAAyB;IACzB;;IAIA,6DAA6D;IAC7D,MAAM,MAAM;IAEZ,IAAI,QAAQ,cAAc;QACxB,iDAAiD;QACjD,+DAA0C,SAAS,uBAAuB;YACxE,OAAO;QACT;QACA,OAAO;IACT;IAEA,uBAAuB;IACvB,OAAO;AACT;AAMO,SAAS;IACd,wCAAwC;IACxC,wCAA0C;QACxC;IACF;;;IAEA,gCAAgC;IAChC,MAAM;AAOR;AAMO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAMO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAOO,SAAS;IACd,OAAO;AACT;AAKO,MAAM,aAAa;IACxB,aAAa;QACX,SAAS;YAAC;YAAa;YAAa;YAAO;YAAU;YAAQ;SAAQ;QACrE,eAAe;QACf,gBAAgB;QAChB,QAAQ;IACV;IACA,YAAY;QACV,SAAS;YAAC;YAAkB;YAAsB;SAAqB;QACvE,eAAe;QACf,gBAAgB;QAChB,QAAQ;IACV;AACF;AAMO,SAAS;IACd,MAAM,MAAM;IACZ,OAAO,UAAU,CAAC,IAAI;AACxB", "debugId": null}}, {"offset": {"line": 3492, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/config/securityRoutes.js"], "sourcesContent": ["/**\r\n * Security Routes Configuration\r\n *\r\n * This file contains the security-protected routes configuration that is used\r\n * by the Next.js middleware and other frontend components. This configuration\r\n * is self-contained within the frontend to avoid dependencies on backend APIs.\r\n *\r\n * To update this configuration:\r\n * 1. Modify the constants below directly in this file\r\n * 2. Update the corresponding backend config/security.php if needed for backend middleware\r\n * 3. Redeploy the frontend\r\n *\r\n * Note: This is the single source of truth for frontend security route configuration.\r\n */\r\n\r\n/**\r\n * Security-protected routes that require verification\r\n * Add new routes here when implementing new secure pages\r\n */\r\nexport const SECURITY_PROTECTED_ROUTES = [\r\n  '/account/phone/setup',\r\n  '/account/email/setup',\r\n  // '/account/password/change',\r\n  // '/account/2fa/setup',\r\n  '/account/security/two-factor',\r\n  '/account/username/setup',\r\n  '/account/address/manage',\r\n  '/account/address/setup',\r\n];\r\n\r\n/**\r\n * Auth flow protected routes that require proper signup flow navigation\r\n * These routes should only be accessible through the legitimate signup process\r\n */\r\nexport const AUTH_FLOW_PROTECTED_ROUTES = [\r\n  '/create-username',\r\n  '/change-password'\r\n];\r\n\r\n/**\r\n * Valid referrer path prefixes (pages users can navigate from)\r\n * These are the application pages that users can legitimately navigate from to secure pages\r\n */\r\nexport const VALID_REFERRER_PREFIXES = [\r\n  '/account',\r\n  '/dashboard',\r\n  '/user',\r\n  '/marketplace',\r\n  '/pricing',\r\n  '/help',\r\n  '/settings',\r\n  '/', // Home page\r\n];\r\n\r\n/**\r\n * Invalid referrer paths (pages users should not navigate from)\r\n * These are pages that should not be able to directly link to secure pages\r\n */\r\nexport const INVALID_REFERRER_PATHS = [\r\n  '/login',\r\n  '/signup',\r\n  '/forget-password',\r\n  '/verify-email',\r\n  '/security-check',\r\n  '/logout',\r\n];\r\n\r\n/**\r\n * Fallback URL mappings for direct access prevention\r\n * When users try to access secure pages directly, they are redirected to these fallback URLs\r\n */\r\nexport const FALLBACK_URL_MAPPINGS = {\r\n  '/account/phone/setup': '/account/details',\r\n  '/account/email/setup': '/account/details',\r\n  // '/account/password/change': '/account/overview',\r\n  // '/account/2fa/setup': '/account/overview',\r\n  '/account/security/two-factor': '/account/security',\r\n  '/account/username/setup': '/account/details',\r\n  '/account/address/manage': '/account/details',\r\n  '/account/address/setup': '/account/address/manage',\r\n};\r\n\r\n/**\r\n * Default fallback URL when no specific mapping exists\r\n */\r\nexport const DEFAULT_FALLBACK_URL = '/account/overview';\r\n\r\n/**\r\n * Valid referrer prefixes for security-check page access\r\n * These are the pages that can legitimately redirect users to the security-check page\r\n */\r\nexport const VALID_SECURITY_CHECK_REFERRERS = [\r\n  '/account',\r\n  '/dashboard',\r\n  '/user'\r\n];\r\n\r\n/**\r\n * Valid referrer prefixes for auth flow protected routes\r\n * These are the pages that can legitimately redirect users to auth flow pages like /create-username\r\n */\r\nexport const VALID_AUTH_FLOW_REFERRERS = [\r\n  '/security-check',\r\n];\r\n\r\n/**\r\n * Security configuration settings\r\n */\r\nexport const SECURITY_CONFIG = {\r\n  // Enable referrer-based access control\r\n  referrerControlEnabled: true,\r\n\r\n  // Cookie configuration for client-side validation\r\n  cookie: {\r\n    name: 'security_verified',\r\n    // Note: Actual expiration is controlled by backend, this is for client-side validation only\r\n    defaultExpirationMinutes: 5,\r\n    checkIntervalSeconds: 30,\r\n  },\r\n};\r\n\r\nimport { getBaseUrlForParsing } from '@/utils/environment';\r\n\r\n/**\r\n * Helper function to validate if a path is a valid secure route\r\n * @param {string} path - The path to validate\r\n * @returns {boolean} True if the path is a valid secure route\r\n */\r\nexport function isValidSecureRoute(path) {\r\n  try {\r\n    // Decode URL if it's encoded\r\n    const decodedPath = decodeURIComponent(path);\r\n\r\n    // For relative paths, extract path directly without URL constructor\r\n    let pathOnly;\r\n    if (decodedPath.startsWith('/')) {\r\n      // It's already a relative path, use it directly\r\n      pathOnly = decodedPath.split('?')[0]; // Remove query parameters\r\n    } else {\r\n      // It might be an absolute URL, use URL constructor with dynamic base\r\n      const baseUrl = getBaseUrlForParsing();\r\n      const url = new URL(decodedPath, baseUrl);\r\n      pathOnly = url.pathname;\r\n    }\r\n\r\n    // Check if the path exactly matches any of the protected routes\r\n    // or if it starts with any of the protected routes (for sub-paths)\r\n    return SECURITY_PROTECTED_ROUTES.some(route => {\r\n      // Exact match\r\n      if (pathOnly === route) {\r\n        return true;\r\n      }\r\n\r\n      // Check if path starts with the route (for sub-paths like /account/phone/setup/step2)\r\n      if (pathOnly.startsWith(route + '/')) {\r\n        return true;\r\n      }\r\n\r\n      return false;\r\n    });\r\n  } catch (error) {\r\n    console.warn('Error validating secure route:', error);\r\n    return false;\r\n  }\r\n}\r\n\r\n/**\r\n * Helper function to get appropriate fallback URL for direct access prevention\r\n * @param {string} securePagePath - The secure page path\r\n * @returns {string} Fallback URL\r\n */\r\nexport function getDirectAccessFallbackUrl(securePagePath) {\r\n  return FALLBACK_URL_MAPPINGS[securePagePath] || DEFAULT_FALLBACK_URL;\r\n}\r\n\r\n/**\r\n * Helper function to validate if a path is a valid auth flow route\r\n * @param {string} path - The path to validate\r\n * @returns {boolean} True if the path is a valid auth flow route\r\n */\r\nexport function isValidAuthFlowRoute(path) {\r\n  try {\r\n    // Decode URL if it's encoded\r\n    const decodedPath = decodeURIComponent(path);\r\n\r\n    // For relative paths, extract path directly without URL constructor\r\n    let pathOnly;\r\n    if (decodedPath.startsWith('/')) {\r\n      // It's already a relative path, use it directly\r\n      pathOnly = decodedPath.split('?')[0]; // Remove query parameters\r\n    } else {\r\n      // It might be an absolute URL, use URL constructor with dynamic base\r\n      const baseUrl = getBaseUrlForParsing();\r\n      const url = new URL(decodedPath, baseUrl);\r\n      pathOnly = url.pathname;\r\n    }\r\n\r\n    // Check if the path exactly matches any of the auth flow protected routes\r\n    return AUTH_FLOW_PROTECTED_ROUTES.some(route => {\r\n      // Exact match\r\n      if (pathOnly === route) {\r\n        return true;\r\n      }\r\n\r\n      // Check if path starts with the route (for sub-paths)\r\n      if (pathOnly.startsWith(route + '/')) {\r\n        return true;\r\n      }\r\n\r\n      return false;\r\n    });\r\n  } catch (error) {\r\n    console.warn('Error validating auth flow route:', error);\r\n    return false;\r\n  }\r\n}\r\n\r\n/**\r\n * Get the appropriate fallback URL for auth flow direct access prevention\r\n * @param {string} attemptedPath - The path the user tried to access directly\r\n * @returns {string} - The fallback URL to redirect to\r\n */\r\nexport function getAuthFlowFallbackUrl(attemptedPath) {\r\n  // Map specific auth flow routes to their appropriate fallback pages\r\n  const authFlowFallbacks = {\r\n    '/create-username': '/login',\r\n    // Add more auth flow fallbacks as needed\r\n  };\r\n\r\n  // Check for specific route fallback first\r\n  if (authFlowFallbacks[attemptedPath]) {\r\n    return authFlowFallbacks[attemptedPath];\r\n  }\r\n\r\n  // Default fallback for auth flow routes\r\n  return '/login';\r\n}\r\n\r\n/**\r\n * Configuration metadata\r\n */\r\nexport const CONFIG_METADATA = {\r\n  lastSynced: new Date().toISOString(),\r\n  source: 'backend:config/security.php',\r\n  version: '1.0.0'\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;CAaC,GAED;;;CAGC;;;;;;;;;;;;;;;;AAuGD;AAtGO,MAAM,4BAA4B;IACvC;IACA;IACA,8BAA8B;IAC9B,wBAAwB;IACxB;IACA;IACA;IACA;CACD;AAMM,MAAM,6BAA6B;IACxC;IACA;CACD;AAMM,MAAM,0BAA0B;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAMM,MAAM,yBAAyB;IACpC;IACA;IACA;IACA;IACA;IACA;CACD;AAMM,MAAM,wBAAwB;IACnC,wBAAwB;IACxB,wBAAwB;IACxB,mDAAmD;IACnD,6CAA6C;IAC7C,gCAAgC;IAChC,2BAA2B;IAC3B,2BAA2B;IAC3B,0BAA0B;AAC5B;AAKO,MAAM,uBAAuB;AAM7B,MAAM,iCAAiC;IAC5C;IACA;IACA;CACD;AAMM,MAAM,4BAA4B;IACvC;CACD;AAKM,MAAM,kBAAkB;IAC7B,uCAAuC;IACvC,wBAAwB;IAExB,kDAAkD;IAClD,QAAQ;QACN,MAAM;QACN,4FAA4F;QAC5F,0BAA0B;QAC1B,sBAAsB;IACxB;AACF;;AASO,SAAS,mBAAmB,IAAI;IACrC,IAAI;QACF,6BAA6B;QAC7B,MAAM,cAAc,mBAAmB;QAEvC,oEAAoE;QACpE,IAAI;QACJ,IAAI,YAAY,UAAU,CAAC,MAAM;YAC/B,gDAAgD;YAChD,WAAW,YAAY,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,0BAA0B;QAClE,OAAO;YACL,qEAAqE;YACrE,MAAM,UAAU,CAAA,GAAA,2HAAA,CAAA,uBAAoB,AAAD;YACnC,MAAM,MAAM,IAAI,IAAI,aAAa;YACjC,WAAW,IAAI,QAAQ;QACzB;QAEA,gEAAgE;QAChE,mEAAmE;QACnE,OAAO,0BAA0B,IAAI,CAAC,CAAA;YACpC,cAAc;YACd,IAAI,aAAa,OAAO;gBACtB,OAAO;YACT;YAEA,sFAAsF;YACtF,IAAI,SAAS,UAAU,CAAC,QAAQ,MAAM;gBACpC,OAAO;YACT;YAEA,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,kCAAkC;QAC/C,OAAO;IACT;AACF;AAOO,SAAS,2BAA2B,cAAc;IACvD,OAAO,qBAAqB,CAAC,eAAe,IAAI;AAClD;AAOO,SAAS,qBAAqB,IAAI;IACvC,IAAI;QACF,6BAA6B;QAC7B,MAAM,cAAc,mBAAmB;QAEvC,oEAAoE;QACpE,IAAI;QACJ,IAAI,YAAY,UAAU,CAAC,MAAM;YAC/B,gDAAgD;YAChD,WAAW,YAAY,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,0BAA0B;QAClE,OAAO;YACL,qEAAqE;YACrE,MAAM,UAAU,CAAA,GAAA,2HAAA,CAAA,uBAAoB,AAAD;YACnC,MAAM,MAAM,IAAI,IAAI,aAAa;YACjC,WAAW,IAAI,QAAQ;QACzB;QAEA,0EAA0E;QAC1E,OAAO,2BAA2B,IAAI,CAAC,CAAA;YACrC,cAAc;YACd,IAAI,aAAa,OAAO;gBACtB,OAAO;YACT;YAEA,sDAAsD;YACtD,IAAI,SAAS,UAAU,CAAC,QAAQ,MAAM;gBACpC,OAAO;YACT;YAEA,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,qCAAqC;QAClD,OAAO;IACT;AACF;AAOO,SAAS,uBAAuB,aAAa;IAClD,oEAAoE;IACpE,MAAM,oBAAoB;QACxB,oBAAoB;IAEtB;IAEA,0CAA0C;IAC1C,IAAI,iBAAiB,CAAC,cAAc,EAAE;QACpC,OAAO,iBAAiB,CAAC,cAAc;IACzC;IAEA,wCAAwC;IACxC,OAAO;AACT;AAKO,MAAM,kBAAkB;IAC7B,YAAY,IAAI,OAAO,WAAW;IAClC,QAAQ;IACR,SAAS;AACX", "debugId": null}}, {"offset": {"line": 3677, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/utils/securityConfig.js"], "sourcesContent": ["/**\r\n * Security Configuration Utility\r\n *\r\n * Provides security configuration using local frontend configuration\r\n * instead of fetching from backend API to reduce dependencies\r\n */\r\n\r\nimport {\r\n    SECURITY_PROTECTED_ROUTES,\r\n    FALLBACK_URL_MAPPINGS,\r\n    DEFAULT_FALLBACK_URL,\r\n    SECURITY_CONFIG\r\n} from '@/config/securityRoutes';\r\nimport { getBaseUrlForParsing } from '@/utils/environment';\r\n\r\n/**\r\n * Get security configuration (synchronous)\r\n * @returns {Object} Security configuration object\r\n */\r\nexport function getSecurityConfig() {\r\n    return {\r\n        cookie_name: SECURITY_CONFIG.cookie.name,\r\n        expires_in_minutes: SECURITY_CONFIG.cookie.defaultExpirationMinutes,\r\n        check_interval_seconds: SECURITY_CONFIG.cookie.checkIntervalSeconds,\r\n        check_interval_milliseconds: SECURITY_CONFIG.cookie.checkIntervalSeconds * 1000,\r\n        protected_routes: SECURITY_PROTECTED_ROUTES,\r\n        referrer_control: {\r\n            enabled: SECURITY_CONFIG.referrerControlEnabled,\r\n            fallback_urls: FALLBACK_URL_MAPPINGS,\r\n            default_fallback: DEFAULT_FALLBACK_URL,\r\n        },\r\n    };\r\n}\r\n\r\n/**\r\n * Fetch security configuration (async for backward compatibility)\r\n * @returns {Promise<Object>} Security configuration object\r\n */\r\nexport async function fetchSecurityConfig() {\r\n    // Return the local configuration wrapped in a Promise for backward compatibility\r\n    return Promise.resolve(getSecurityConfig());\r\n}\r\n\r\n/**\r\n * Get cached security configuration (synchronous)\r\n * @returns {Object} Security configuration object (always available since it's local)\r\n */\r\nexport function getCachedSecurityConfig() {\r\n    // Since configuration is now local, always return the current config\r\n    return getSecurityConfig();\r\n}\r\n\r\n/**\r\n * Clear the configuration cache (no-op since config is local)\r\n * @deprecated This function is kept for backward compatibility but does nothing\r\n */\r\nexport function clearSecurityConfigCache() {\r\n    // No-op since we don't cache local configuration\r\n}\r\n\r\n/**\r\n * Get default security configuration\r\n * @returns {Object} Default configuration object\r\n */\r\nexport function getDefaultSecurityConfig() {\r\n    // Return the same as getSecurityConfig since it's all local now\r\n    return getSecurityConfig();\r\n}\r\n\r\n/**\r\n * Get cached protected routes (synchronous)\r\n * @returns {Array} Array of protected route paths\r\n */\r\nexport function getCachedProtectedRoutes() {\r\n    // Since configuration is now local, directly return the protected routes\r\n    return SECURITY_PROTECTED_ROUTES;\r\n}\r\n\r\n\r\n\r\n/**\r\n * Validate if a path is a valid secure route\r\n * @param {string} path - The path to validate\r\n * @returns {boolean} True if the path is a valid secure route\r\n */\r\nexport function isValidSecureRoute(path) {\r\n    try {\r\n        // Decode URL if it's encoded\r\n        const decodedPath = decodeURIComponent(path);\r\n\r\n        // For relative paths, extract path directly without URL constructor\r\n        let pathOnly;\r\n        if (decodedPath.startsWith('/')) {\r\n            // It's already a relative path, use it directly\r\n            pathOnly = decodedPath.split('?')[0]; // Remove query parameters\r\n        } else {\r\n            // It might be an absolute URL, use URL constructor with dynamic base\r\n            const baseUrl = getBaseUrlForParsing();\r\n            const url = new URL(decodedPath, baseUrl);\r\n            pathOnly = url.pathname;\r\n        }\r\n\r\n        // Remove leading slash if present for consistent comparison\r\n        const normalizedPath = pathOnly.replace(/^\\//, '');\r\n\r\n        // Get protected routes directly from local config\r\n        return SECURITY_PROTECTED_ROUTES.some(route => {\r\n            // Remove leading slash from secure route for comparison\r\n            const normalizedRoute = route.replace(/^\\//, '');\r\n\r\n            // Check if the path matches exactly or starts with the secure route path\r\n            return normalizedPath === normalizedRoute || normalizedPath.startsWith(normalizedRoute);\r\n        });\r\n    } catch (error) {\r\n        console.warn('Error validating secure route:', error);\r\n        return false;\r\n    }\r\n}\r\n\r\n/**\r\n * Get fallback URL for a secure page path\r\n * @param {string} securePagePath - The secure page path\r\n * @returns {string} Fallback URL\r\n */\r\nexport function getDirectAccessFallbackUrl(securePagePath) {\r\n    // Use local configuration directly\r\n    const fallbackUrl = FALLBACK_URL_MAPPINGS[securePagePath];\r\n    if (fallbackUrl) {\r\n        return fallbackUrl;\r\n    }\r\n\r\n    // Return default fallback if specific mapping not found\r\n    return DEFAULT_FALLBACK_URL;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;AAED;AAMA;;;AAMO,SAAS;IACZ,OAAO;QACH,aAAa,+HAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,IAAI;QACxC,oBAAoB,+HAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,wBAAwB;QACnE,wBAAwB,+HAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,oBAAoB;QACnE,6BAA6B,+HAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,oBAAoB,GAAG;QAC3E,kBAAkB,+HAAA,CAAA,4BAAyB;QAC3C,kBAAkB;YACd,SAAS,+HAAA,CAAA,kBAAe,CAAC,sBAAsB;YAC/C,eAAe,+HAAA,CAAA,wBAAqB;YACpC,kBAAkB,+HAAA,CAAA,uBAAoB;QAC1C;IACJ;AACJ;AAMO,eAAe;IAClB,iFAAiF;IACjF,OAAO,QAAQ,OAAO,CAAC;AAC3B;AAMO,SAAS;IACZ,qEAAqE;IACrE,OAAO;AACX;AAMO,SAAS;AACZ,iDAAiD;AACrD;AAMO,SAAS;IACZ,gEAAgE;IAChE,OAAO;AACX;AAMO,SAAS;IACZ,yEAAyE;IACzE,OAAO,+HAAA,CAAA,4BAAyB;AACpC;AASO,SAAS,mBAAmB,IAAI;IACnC,IAAI;QACA,6BAA6B;QAC7B,MAAM,cAAc,mBAAmB;QAEvC,oEAAoE;QACpE,IAAI;QACJ,IAAI,YAAY,UAAU,CAAC,MAAM;YAC7B,gDAAgD;YAChD,WAAW,YAAY,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,0BAA0B;QACpE,OAAO;YACH,qEAAqE;YACrE,MAAM,UAAU,CAAA,GAAA,2HAAA,CAAA,uBAAoB,AAAD;YACnC,MAAM,MAAM,IAAI,IAAI,aAAa;YACjC,WAAW,IAAI,QAAQ;QAC3B;QAEA,4DAA4D;QAC5D,MAAM,iBAAiB,SAAS,OAAO,CAAC,OAAO;QAE/C,kDAAkD;QAClD,OAAO,+HAAA,CAAA,4BAAyB,CAAC,IAAI,CAAC,CAAA;YAClC,wDAAwD;YACxD,MAAM,kBAAkB,MAAM,OAAO,CAAC,OAAO;YAE7C,yEAAyE;YACzE,OAAO,mBAAmB,mBAAmB,eAAe,UAAU,CAAC;QAC3E;IACJ,EAAE,OAAO,OAAO;QACZ,QAAQ,IAAI,CAAC,kCAAkC;QAC/C,OAAO;IACX;AACJ;AAOO,SAAS,2BAA2B,cAAc;IACrD,mCAAmC;IACnC,MAAM,cAAc,+HAAA,CAAA,wBAAqB,CAAC,eAAe;IACzD,IAAI,aAAa;QACb,OAAO;IACX;IAEA,wDAAwD;IACxD,OAAO,+HAAA,CAAA,uBAAoB;AAC/B", "debugId": null}}, {"offset": {"line": 3771, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/CommonTooltip.js"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useRef, useEffect, useCallback } from \"react\";\r\nimport { createPortal } from \"react-dom\";\r\nimport \"../../css/common/CommonTooltip.scss\";\r\n\r\nconst CommonTooltip = ({\r\n  children,\r\n  content,\r\n  position = \"top-right\",\r\n  className = \"\"\r\n}) => {\r\n  const [visible, setVisible] = useState(false);\r\n  const [tooltipStyle, setTooltipStyle] = useState({});\r\n  const wrapperRef = useRef(null);\r\n  const tooltipRef = useRef(null);\r\n\r\n  const updateTooltipPosition = useCallback(() => {\r\n    if (!wrapperRef.current || !tooltipRef.current) return;\r\n\r\n    const triggerRect = wrapperRef.current.getBoundingClientRect();\r\n    const tooltipRect = tooltipRef.current.getBoundingClientRect();\r\n\r\n    const scrollY = window.scrollY || window.pageYOffset;\r\n    const scrollX = window.scrollX || window.pageXOffset;\r\n\r\n    let top = triggerRect.top + scrollY - tooltipRect.height - 8;\r\n    let left = triggerRect.left + scrollX;\r\n\r\n    switch (position) {\r\n      case \"top-right\":\r\n        left += triggerRect.width - tooltipRect.width;\r\n        break;\r\n      case \"bottom-left\":\r\n        top = triggerRect.bottom + scrollY + 8;\r\n        break;\r\n      case \"bottom-right\":\r\n        top = triggerRect.bottom + scrollY + 8;\r\n        left += triggerRect.width - tooltipRect.width;\r\n        break;\r\n      case \"center-top\":\r\n        left += triggerRect.width / 2 - tooltipRect.width / 2;\r\n        break;\r\n      case \"center-bottom\":\r\n        top = triggerRect.bottom + scrollY + 8;\r\n        left += triggerRect.width / 2 - tooltipRect.width / 2;\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n\r\n    setTooltipStyle({\r\n      position: \"absolute\",\r\n      top: `${top}px`,\r\n      left: `${left}px`,\r\n      zIndex: 9999,\r\n    });\r\n  }, [position]);\r\n\r\n  useEffect(() => {\r\n    if (visible) {\r\n      updateTooltipPosition();\r\n\r\n      window.addEventListener(\"scroll\", updateTooltipPosition, true); // true captures bubbling scroll\r\n      window.addEventListener(\"resize\", updateTooltipPosition);\r\n\r\n      return () => {\r\n        window.removeEventListener(\"scroll\", updateTooltipPosition, true);\r\n        window.removeEventListener(\"resize\", updateTooltipPosition);\r\n      };\r\n    }\r\n  }, [visible, updateTooltipPosition]);\r\n\r\n  return (\r\n    <div\r\n      className={`tooltip-wrapper ${className}`}\r\n      onMouseEnter={() => setVisible(true)}\r\n      onMouseLeave={() => setVisible(false)}\r\n      onFocus={() => setVisible(true)}\r\n      onBlur={() => setVisible(false)}\r\n      ref={wrapperRef}\r\n      style={{ display: \"inline-block\" }}\r\n    >\r\n      {children}\r\n      {visible &&\r\n        createPortal(\r\n          <div\r\n            ref={tooltipRef}\r\n            className=\"tooltip-box\"\r\n            style={tooltipStyle}\r\n          >\r\n            {content}\r\n          </div>,\r\n          document.body\r\n        )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CommonTooltip;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;;AAMA,MAAM,gBAAgB,CAAC,EACrB,QAAQ,EACR,OAAO,EACP,WAAW,WAAW,EACtB,YAAY,EAAE,EACf;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAClD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxC,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,WAAW,OAAO,EAAE;QAEhD,MAAM,cAAc,WAAW,OAAO,CAAC,qBAAqB;QAC5D,MAAM,cAAc,WAAW,OAAO,CAAC,qBAAqB;QAE5D,MAAM,UAAU,OAAO,OAAO,IAAI,OAAO,WAAW;QACpD,MAAM,UAAU,OAAO,OAAO,IAAI,OAAO,WAAW;QAEpD,IAAI,MAAM,YAAY,GAAG,GAAG,UAAU,YAAY,MAAM,GAAG;QAC3D,IAAI,OAAO,YAAY,IAAI,GAAG;QAE9B,OAAQ;YACN,KAAK;gBACH,QAAQ,YAAY,KAAK,GAAG,YAAY,KAAK;gBAC7C;YACF,KAAK;gBACH,MAAM,YAAY,MAAM,GAAG,UAAU;gBACrC;YACF,KAAK;gBACH,MAAM,YAAY,MAAM,GAAG,UAAU;gBACrC,QAAQ,YAAY,KAAK,GAAG,YAAY,KAAK;gBAC7C;YACF,KAAK;gBACH,QAAQ,YAAY,KAAK,GAAG,IAAI,YAAY,KAAK,GAAG;gBACpD;YACF,KAAK;gBACH,MAAM,YAAY,MAAM,GAAG,UAAU;gBACrC,QAAQ,YAAY,KAAK,GAAG,IAAI,YAAY,KAAK,GAAG;gBACpD;YACF;gBACE;QACJ;QAEA,gBAAgB;YACd,UAAU;YACV,KAAK,GAAG,IAAI,EAAE,CAAC;YACf,MAAM,GAAG,KAAK,EAAE,CAAC;YACjB,QAAQ;QACV;IACF,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX;YAEA,OAAO,gBAAgB,CAAC,UAAU,uBAAuB,OAAO,gCAAgC;YAChG,OAAO,gBAAgB,CAAC,UAAU;YAElC,OAAO;gBACL,OAAO,mBAAmB,CAAC,UAAU,uBAAuB;gBAC5D,OAAO,mBAAmB,CAAC,UAAU;YACvC;QACF;IACF,GAAG;QAAC;QAAS;KAAsB;IAEnC,qBACE,8OAAC;QACC,WAAW,CAAC,gBAAgB,EAAE,WAAW;QACzC,cAAc,IAAM,WAAW;QAC/B,cAAc,IAAM,WAAW;QAC/B,SAAS,IAAM,WAAW;QAC1B,QAAQ,IAAM,WAAW;QACzB,KAAK;QACL,OAAO;YAAE,SAAS;QAAe;;YAEhC;YACA,yBACC,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,gBACT,8OAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;0BAEN;;;;;0DAEH,SAAS,IAAI;;;;;;;AAIvB;uCAEe", "debugId": null}}, {"offset": {"line": 3873, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Auth%29/security-check/UseRestoreCodeModal.jsx"], "sourcesContent": ["'use client';\nimport { <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';\nimport { Formik, Form, Field } from 'formik';\nimport { useState, useRef } from 'react';\nimport CommonTooltip from '@/Components/UI/CommonTooltip';\nimport { SolidInfoIcon } from '@/assets/svgIcons/SvgIcon';\nimport '@/css/account/Security.scss';\n\nconst UseRestoreCodeModal = ({ show, handleClose, onSubmit, isSubmitting }) => {\n  const inputRefs = useRef([]);\n  const [restoreCode, setRestoreCode] = useState('');\n\n  const formatRestoreCode = (value) => {\n    // Remove all non-alphanumeric characters and convert to uppercase\n    const cleaned = value.replace(/[^A-Z0-9]/gi, '').toUpperCase();\n    // Split into groups of 4 characters\n    const groups = [];\n    for (let i = 0; i < cleaned.length; i += 4) {\n      groups.push(cleaned.slice(i, i + 4));\n    }\n    // Join with dashes, limit to 6 groups (24 characters total)\n    return groups.slice(0, 6).join('-');\n  };\n\n  const handleInputChange = (e, setFieldValue) => {\n    const value = e.target.value;\n    const formatted = formatRestoreCode(value);\n    setRestoreCode(formatted);\n    setFieldValue('restore_code', formatted);\n  };\n\n  const handlePaste = (e, setFieldValue) => {\n    e.preventDefault();\n    const pasteData = e.clipboardData.getData('text');\n    const formatted = formatRestoreCode(pasteData);\n    setRestoreCode(formatted);\n    setFieldValue('restore_code', formatted);\n  };\n\n  const handleSubmitForm = (values) => {\n    onSubmit(values.restore_code);\n  };\n\n  const handleModalClose = () => {\n    setRestoreCode('');\n    handleClose();\n  };\n\n  return (\n    <Modal\n      show={show}\n      onHide={handleModalClose}\n      centered\n      size=\"xl\"\n      contentClassName=\"custom-modal-content\"\n    >\n      <div className=\"px-4 sm:px-6 py-6 sm:py-10 rounded-[15px] space-y-4 sm:space-y-6 max-w-full overflow-hidden\">\n        <h5 className=\"text-2xl sm:text-[32px] font-extrabold text-white text-left\">\n          Use Restore Code\n        </h5>\n\n        <p className=\"text-base sm:text-[20px] font-semibold text-white text-left\">\n          Paste your restoral code to proceed.\n        </p>\n\n        <Formik\n          initialValues={{ restore_code: '' }}\n          onSubmit={handleSubmitForm}\n          enableReinitialize\n        >\n          {({ setFieldValue }) => (\n            <Form>\n              <div className=\"mb-4\">\n                <Field name=\"restore_code\">\n                  {({ field }) => (\n                    <input\n                      {...field}\n                      type=\"text\"\n                      className=\"form-control text-white bg-white/20 border-white/30 rounded-md p-3 w-full text-lg tracking-wider\"\n                      placeholder=\"XXXX-XXXX-XXXX-XXXX-XXXX-XXXX\"\n                      value={restoreCode}\n                      onChange={(e) => handleInputChange(e, setFieldValue)}\n                      onPaste={(e) => handlePaste(e, setFieldValue)}\n                      maxLength={29} // 24 characters + 5 dashes\n                      style={{\n                        letterSpacing: '0.1em',\n                        fontFamily: 'monospace'\n                      }}\n                    />\n                  )}\n                </Field>\n              </div>\n\n              <div className=\"d-flex align-items-center gap-2 mb-4\">\n                {/* <CommonTooltip\n                  content=\"Don't have a code? Generate one from your account settings next time you log in.\"\n                  className=\"d-flex align-items-center\"\n                >\n                  <SolidInfoIcon className=\"text-white/70\" />\n                </CommonTooltip> */}\n                <span className=\"text-white/70 text-sm\">\n                  Don't have a code? Generate one from your account settings next time you log in.\n                </span>\n              </div>\n\n              <div className=\"flex flex-col sm:flex-row gap-3 sm:gap-4 mt-4\">\n                <Button\n                  type=\"submit\"\n                  disabled={isSubmitting || !restoreCode || restoreCode.length < 29}\n                  className=\"bg-[#00b7ff] text-white font-semibold !rounded-md w-full sm:w-[200px] disabled:opacity-50\"\n                >\n                  {isSubmitting ? 'Verifying...' : 'Continue'}\n                </Button>\n                <Button\n                  type=\"button\"\n                  onClick={handleModalClose}\n                  className=\"!bg-[#B4B4B4] text-black font-semibold !rounded-md w-full sm:w-[200px]\"\n                >\n                  Cancel\n                </Button>\n              </div>\n            </Form>\n          )}\n        </Formik>\n      </div>\n    </Modal>\n  );\n};\n\nexport default UseRestoreCodeModal;\n"], "names": [], "mappings": ";;;;AACA;AAAA;AACA;AACA;AACA;AACA;AALA;;;;;;;;AAQA,MAAM,sBAAsB,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE;IACxE,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,oBAAoB,CAAC;QACzB,kEAAkE;QAClE,MAAM,UAAU,MAAM,OAAO,CAAC,eAAe,IAAI,WAAW;QAC5D,oCAAoC;QACpC,MAAM,SAAS,EAAE;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,KAAK,EAAG;YAC1C,OAAO,IAAI,CAAC,QAAQ,KAAK,CAAC,GAAG,IAAI;QACnC;QACA,4DAA4D;QAC5D,OAAO,OAAO,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;IACjC;IAEA,MAAM,oBAAoB,CAAC,GAAG;QAC5B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,MAAM,YAAY,kBAAkB;QACpC,eAAe;QACf,cAAc,gBAAgB;IAChC;IAEA,MAAM,cAAc,CAAC,GAAG;QACtB,EAAE,cAAc;QAChB,MAAM,YAAY,EAAE,aAAa,CAAC,OAAO,CAAC;QAC1C,MAAM,YAAY,kBAAkB;QACpC,eAAe;QACf,cAAc,gBAAgB;IAChC;IAEA,MAAM,mBAAmB,CAAC;QACxB,SAAS,OAAO,YAAY;IAC9B;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf;IACF;IAEA,qBACE,8OAAC,sLAAA,CAAA,QAAK;QACJ,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,MAAK;QACL,kBAAiB;kBAEjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA8D;;;;;;8BAI5E,8OAAC;oBAAE,WAAU;8BAA8D;;;;;;8BAI3E,8OAAC,+IAAA,CAAA,SAAM;oBACL,eAAe;wBAAE,cAAc;oBAAG;oBAClC,UAAU;oBACV,kBAAkB;8BAEjB,CAAC,EAAE,aAAa,EAAE,iBACjB,8OAAC,+IAAA,CAAA,OAAI;;8CACH,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,+IAAA,CAAA,QAAK;wCAAC,MAAK;kDACT,CAAC,EAAE,KAAK,EAAE,iBACT,8OAAC;gDACE,GAAG,KAAK;gDACT,MAAK;gDACL,WAAU;gDACV,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,kBAAkB,GAAG;gDACtC,SAAS,CAAC,IAAM,YAAY,GAAG;gDAC/B,WAAW;gDACX,OAAO;oDACL,eAAe;oDACf,YAAY;gDACd;;;;;;;;;;;;;;;;8CAMR,8OAAC;oCAAI,WAAU;8CAOb,cAAA,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;8CAK1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wLAAA,CAAA,SAAM;4CACL,MAAK;4CACL,UAAU,gBAAgB,CAAC,eAAe,YAAY,MAAM,GAAG;4CAC/D,WAAU;sDAET,eAAe,iBAAiB;;;;;;sDAEnC,8OAAC,wLAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;uCAEe", "debugId": null}}, {"offset": {"line": 4062, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Auth%29/security-check/NewRestoreCodeModal.jsx"], "sourcesContent": ["'use client';\nimport { <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap';\nimport { useState } from 'react';\nimport '@/css/account/Security.scss';\n\nconst NewRestoreCodeModal = ({ show, handleClose, restoreCode }) => {\n  const [copied, setCopied] = useState(false);\n\n  const handleCopy = () => {\n    navigator.clipboard.writeText(restoreCode);\n    setCopied(true);\n    setTimeout(() => setCopied(false), 3000);\n  };\n\n  const handleDownloadCSV = () => {\n    const csvContent = `TradeReply Restoral Code\\n${restoreCode}`;\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = 'restoral-code.csv';\n    link.click();\n    URL.revokeObjectURL(url);\n  };\n\n  return (\n    <Modal\n      show={show}\n      onHide={handleClose}\n      centered\n      size=\"xl\"\n      contentClassName=\"custom-modal-content\"\n    >\n      <div className=\"px-4 sm:px-6 py-6 sm:py-10 rounded-[15px] space-y-4 sm:space-y-6 max-w-full overflow-hidden\">\n        <h5 className=\"text-2xl sm:text-[32px] font-extrabold text-white text-left\">\n          Your New Restoral Code\n        </h5>\n\n        <div className=\"space-y-4\">\n          <p className=\"text-base sm:text-[20px] font-semibold text-white text-left\">\n            You've successfully accessed your account using your previous restoral code.\n          </p>\n          \n          <p className=\"text-base sm:text-[20px] font-semibold text-white text-left\">\n            For your security, a new restoral code has been generated. The old one is no longer valid.\n          </p>\n          \n          <p className=\"text-base sm:text-[20px] font-semibold text-white text-left\">\n            Store this code somewhere safe — we won't show it again.\n          </p>\n        </div>\n\n        <div className=\"text-base sm:text-[20px] font-semibold text-white text-left\">\n          Your Restoral Code{' '}\n          <span className=\"inline-block px-4 py-2 mt-2 sm:mt-0 bg-white/20 text-white font-semibold rounded-md tracking-wide break-words text-center w-full sm:w-auto\">\n            {restoreCode}\n          </span>\n        </div>\n\n        <p\n          className=\"text-[#00b7ff] cursor-pointer text-sm sm:text-[16px] font-semibold text-left hover:underline\"\n          onClick={handleDownloadCSV}\n        >\n          Download as CSV\n        </p>\n\n        {copied && (\n          <div className=\"green_text text-left text-sm sm:text-base\">\n            Restoral Code copied. Store it securely.\n          </div>\n        )}\n\n        <div className=\"flex flex-col sm:flex-row gap-3 sm:gap-4 mt-4\">\n          <Button\n            onClick={handleCopy}\n            className=\"!bg-[#B4B4B4] text-black font-semibold !rounded-md w-full sm:w-[200px]\"\n          >\n            Copy Code\n          </Button>\n          <Button\n            onClick={handleClose}\n            className=\"bg-[#00b7ff] text-white font-semibold !rounded-md w-full sm:w-[200px]\"\n          >\n            Done\n          </Button>\n        </div>\n      </div>\n    </Modal>\n  );\n};\n\nexport default NewRestoreCodeModal;\n"], "names": [], "mappings": ";;;;AACA;AAAA;AACA;AAFA;;;;;AAKA,MAAM,sBAAsB,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE;IAC7D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,aAAa;QACjB,UAAU,SAAS,CAAC,SAAS,CAAC;QAC9B,UAAU;QACV,WAAW,IAAM,UAAU,QAAQ;IACrC;IAEA,MAAM,oBAAoB;QACxB,MAAM,aAAa,CAAC,0BAA0B,EAAE,aAAa;QAC7D,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAA0B;QACtE,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG;QAChB,KAAK,KAAK;QACV,IAAI,eAAe,CAAC;IACtB;IAEA,qBACE,8OAAC,sLAAA,CAAA,QAAK;QACJ,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,MAAK;QACL,kBAAiB;kBAEjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA8D;;;;;;8BAI5E,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAA8D;;;;;;sCAI3E,8OAAC;4BAAE,WAAU;sCAA8D;;;;;;sCAI3E,8OAAC;4BAAE,WAAU;sCAA8D;;;;;;;;;;;;8BAK7E,8OAAC;oBAAI,WAAU;;wBAA8D;wBACxD;sCACnB,8OAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;;8BAIL,8OAAC;oBACC,WAAU;oBACV,SAAS;8BACV;;;;;;gBAIA,wBACC,8OAAC;oBAAI,WAAU;8BAA4C;;;;;;8BAK7D,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,wLAAA,CAAA,SAAM;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;sCAGD,8OAAC,wLAAA,CAAA,SAAM;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;uCAEe", "debugId": null}}, {"offset": {"line": 4225, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Auth%29/security-check/SecurityCheck.js"], "sourcesContent": ["\"use client\";\r\nimport { useRef, useState, useEffect } from \"react\";\r\nimport {\r\n  ResendCodeIcon,\r\n  ContactCustomerSupport,\r\n} from \"@/assets/svgIcons/SvgIcon\";\r\nimport { securitySchema } from \"@/validations/schema\";\r\nimport { resendVerificationCode, verifyEmailToken } from \"@/utils/auth\";\r\nimport { Formik, Field, Form } from \"formik\";\r\nimport { useRouter, useSearchParams } from \"next/navigation\";\r\nimport CommonButton from \"@/Components/UI/CommonButton\";\r\nimport AuthLayout from \"@/Layouts/AuthLayout\";\r\nimport Link from \"next/link\";\r\nimport LoginFooter from \"@/Components/UI/LoginFooter\";\r\nimport AuthLogo from \"@/Components/common/AuthLogo\";\r\nimport MetaHead from \"@/Seo/Meta/MetaHead\";\r\nimport InputError from \"@/Components/UI/InputError\";\r\nimport toast from \"react-hot-toast\";\r\nimport { post, verifyRestoreCode } from \"@/utils/apiUtils\";\r\nimport { hashInput } from \"@/utils/hashInput\";\r\nimport \"../../../css/common/textInput.scss\";\r\nimport { v4 as uuidv4 } from \"uuid\";\r\nimport axios from \"axios\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { setUser } from \"@/redux/authSlice\";\r\nimport {\r\n  getSecurityConfig,\r\n  getCachedProtectedRoutes,\r\n  isValidSecureRoute,\r\n} from \"@/utils/securityConfig\";\r\nimport { SECURITY_PROTECTED_ROUTES } from \"@/config/securityRoutes\";\r\nimport UseRestoreCodeModal from \"./UseRestoreCodeModal\";\r\nimport NewRestoreCodeModal from \"./NewRestoreCodeModal\";\r\n\r\nconst initialValues = {\r\n  security_code: \"\",\r\n};\r\n\r\nexport default function SecurityCheck() {\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const inputRefs = useRef([]);\r\n  const expiredOnceRef = useRef(false);\r\n  const dispatch = useDispatch();\r\n\r\n  const [isSessionValid, setIsSessionValid] = useState(null);\r\n  const [maskedEmail, setMaskedEmail] = useState(\"\");\r\n  const [type, setType] = useState(\"\");\r\n  const [resendMessage, setResendMessage] = useState(\"\");\r\n  const [cooldown, setCooldown] = useState(0);\r\n  const [isButtonDisabled, setIsButtonDisabled] = useState(false);\r\n  const [isRotating, setIsRotating] = useState(false);\r\n  const [securitySessionId, setSecuritySessionId] = useState(null);\r\n  const [isCheckingExistingCookie, setIsCheckingExistingCookie] =\r\n    useState(true);\r\n  const [showManualRedirect, setShowManualRedirect] = useState(false);\r\n  const [manualRedirectUrl, setManualRedirectUrl] = useState(null);\r\n\r\n  // Restore code modal states\r\n  const [showUseRestoreCodeModal, setShowUseRestoreCodeModal] = useState(false);\r\n  const [showNewRestoreCodeModal, setShowNewRestoreCodeModal] = useState(false);\r\n  const [newRestoreCode, setNewRestoreCode] = useState(\"\");\r\n  const [isVerifyingRestoreCode, setIsVerifyingRestoreCode] = useState(false);\r\n\r\n  const provider = searchParams.get(\"provider\");\r\n  const resetPassword = searchParams.has(\"resetPassword\");\r\n  const rawNextUrl = searchParams.get(\"next\"); // For account security verification\r\n  const verificationType = searchParams.get(\"type\"); // For email update verification\r\n  const isEmailUpdate = verificationType === \"email-update\";\r\n\r\n  // Security verification protected routes (from centralized local configuration)\r\n  const securityProtectedRoutes = SECURITY_PROTECTED_ROUTES;\r\n\r\n  // Note: Using imported isValidSecureRoute function from securityConfig utils\r\n\r\n  // Validate and sanitize the next URL\r\n  const nextUrl =\r\n    rawNextUrl && isValidSecureRoute(rawNextUrl) ? rawNextUrl : null;\r\n  const isAccountSecurity = !!nextUrl && !isEmailUpdate; // If valid next parameter exists and not email update, it's account security verification\r\n\r\n  // Log security validation for monitoring\r\n  if (rawNextUrl && !nextUrl) {\r\n    console.warn(\"Invalid next URL detected and rejected:\", rawNextUrl);\r\n  }\r\n\r\n  const dataType = resetPassword\r\n    ? \"reset_password_data\"\r\n    : provider\r\n    ? `signup_${provider}`\r\n    : \"signup_data\";\r\n\r\n  // Function to check if security cookie is valid\r\n  const isValidSecurityCookie = (cookieValue) => {\r\n    if (!cookieValue) return false;\r\n\r\n    // Simple validation for 'true' value (legacy)\r\n    if (cookieValue === \"true\") return true;\r\n\r\n    // Simple validation for 'verified' value (fallback)\r\n    if (cookieValue === \"verified\") return true;\r\n\r\n    // Check if it's a session token (64 character hex string)\r\n    if (\r\n      typeof cookieValue === \"string\" &&\r\n      cookieValue.length === 64 &&\r\n      /^[a-f0-9]+$/i.test(cookieValue)\r\n    ) {\r\n      return true;\r\n    }\r\n\r\n    // Check if it's a UUID format (with hyphens)\r\n    if (\r\n      typeof cookieValue === \"string\" &&\r\n      /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i.test(\r\n        cookieValue\r\n      )\r\n    ) {\r\n      return true;\r\n    }\r\n\r\n    // Validate encrypted payload with timestamp\r\n    try {\r\n      const payload = JSON.parse(atob(cookieValue));\r\n      if (payload.verified_at) {\r\n        const verifiedAt = new Date(payload.verified_at);\r\n        const now = new Date();\r\n        const diffInSeconds = (now.getTime() - verifiedAt.getTime()) / 1000;\r\n        // Check if within reasonable window (backend will do authoritative validation)\r\n        // Use 15 minutes as a safe buffer since backend config may vary\r\n        return diffInSeconds <= 900;\r\n      }\r\n    } catch (e) {\r\n      return false;\r\n    }\r\n    return false;\r\n  };\r\n\r\n  // Function to get cookie value\r\n  const getCookie = (name) => {\r\n    const value = `; ${document.cookie}`;\r\n    const parts = value.split(`; ${name}=`);\r\n    if (parts.length === 2) return parts.pop().split(\";\").shift();\r\n    return null;\r\n  };\r\n\r\n  // ------------------------\r\n  // Check for existing valid security cookie and redirect immediately\r\n  useEffect(() => {\r\n    if (isAccountSecurity && nextUrl) {\r\n      // Get cookie name from local configuration\r\n      const config = getSecurityConfig();\r\n      const cookieName = config.cookie_name;\r\n\r\n      const securityCookie = getCookie(cookieName);\r\n      if (securityCookie && isValidSecurityCookie(securityCookie)) {\r\n        console.log(\r\n          \"Valid security cookie found, redirecting immediately to:\",\r\n          nextUrl\r\n        );\r\n\r\n        // Extract path from full URL if needed\r\n        let redirectUrl = nextUrl;\r\n        try {\r\n          if (\r\n            redirectUrl.startsWith(\"http://\") ||\r\n            redirectUrl.startsWith(\"https://\")\r\n          ) {\r\n            const url = new URL(redirectUrl);\r\n            redirectUrl = url.pathname + url.search + url.hash;\r\n          }\r\n        } catch (e) {\r\n          console.warn(\"Failed to parse next URL:\", redirectUrl);\r\n        }\r\n\r\n        console.log(\"Redirecting already verified user to:\", redirectUrl);\r\n\r\n        // Use window.location.href for production reliability\r\n        window.location.href = redirectUrl;\r\n        return;\r\n      }\r\n    }\r\n\r\n    // If we reach here, no valid cookie found, so show the verification form\r\n    setIsCheckingExistingCookie(false);\r\n  }, [isAccountSecurity, nextUrl, router]);\r\n\r\n  // ------------------------\r\n  // Validate session\r\n  useEffect(() => {\r\n    const checkSessionValidity = () => {\r\n      // For account security verification or email update, skip session storage validation\r\n      if (isAccountSecurity || isEmailUpdate) {\r\n        return true;\r\n      }\r\n\r\n      // If no valid next parameter and no dataType, redirect to account overview\r\n      if (!dataType && !rawNextUrl) {\r\n        console.warn(\r\n          \"No valid session data or next parameter found, redirecting to account overview\"\r\n        );\r\n        router.replace(\"/account/overview\");\r\n        return false;\r\n      }\r\n\r\n      if (!dataType) return false;\r\n\r\n      const savedData = JSON.parse(sessionStorage.getItem(dataType) || \"{}\");\r\n\r\n      if (\r\n        (!savedData.uuid || !savedData.expiresAt) &&\r\n        !expiredOnceRef.current\r\n      ) {\r\n        expiredOnceRef.current = true;\r\n        router.replace(\"/login\");\r\n        return false;\r\n      }\r\n\r\n      const expired = Date.now() > savedData.expiresAt;\r\n\r\n      if (expired && !expiredOnceRef.current) {\r\n        expiredOnceRef.current = true;\r\n        sessionStorage.removeItem(dataType);\r\n        sessionStorage.removeItem(\"masked_email\");\r\n        sessionStorage.removeItem(\"identifier_type\");\r\n\r\n        sessionStorage.setItem(\"sessionExpired\", \"true\");\r\n        // toast.error(\"Session expired. Please request a new code.\");\r\n\r\n        router.replace(resetPassword ? \"/locate-account\" : \"/login\");\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    };\r\n\r\n    if (checkSessionValidity()) {\r\n      setIsSessionValid(true);\r\n\r\n      const interval = setInterval(checkSessionValidity, 5000);\r\n      return () => clearInterval(interval);\r\n    }\r\n  }, [dataType, isAccountSecurity]);\r\n\r\n  // ------------------------\r\n  // Load masked email from sessionStorage or fetch from backend\r\n  useEffect(() => {\r\n    if (!isSessionValid) return;\r\n\r\n    // For email update verification, get data from session storage\r\n    if (isEmailUpdate) {\r\n      const sessionId = sessionStorage.getItem(\"email_update_session_id\");\r\n      const maskedEmail = sessionStorage.getItem(\"email_update_masked_email\");\r\n\r\n      if (sessionId && maskedEmail) {\r\n        setSecuritySessionId(sessionId);\r\n        setMaskedEmail(maskedEmail);\r\n        setType(\"email\");\r\n      } else {\r\n        console.warn(\"Email update session data not found, redirecting back\");\r\n        router.replace(\"/account/email/setup\");\r\n      }\r\n      return;\r\n    }\r\n\r\n    // For account security verification, send verification code immediately\r\n    if (isAccountSecurity) {\r\n      const sendSecurityCode = async () => {\r\n        try {\r\n          const response = await post(\"/security-verification/send-code\");\r\n          if (response.success) {\r\n            setSecuritySessionId(response.session_id);\r\n            setMaskedEmail(response.masked_email || \"your email\");\r\n            setType(\"email\");\r\n          } else {\r\n            console.log(\"Failed to send verification code\", response);\r\n            toast.error(\"Failed to send verification code\");\r\n            router.replace(\"/account/overview\");\r\n          }\r\n        } catch (error) {\r\n          console.log(\"Failed to send verification code\", error);\r\n          toast.error(\"Failed to send verification code\");\r\n          router.replace(\"/account/overview\");\r\n        }\r\n      };\r\n      sendSecurityCode();\r\n      return;\r\n    }\r\n\r\n    const masked = sessionStorage.getItem(\"masked_email\");\r\n    const identifierType = sessionStorage.getItem(\"identifier_type\");\r\n\r\n    if (masked) {\r\n      setMaskedEmail(masked);\r\n      if (identifierType) setType(identifierType);\r\n      return;\r\n    }\r\n\r\n    if (dataType.startsWith(\"signup_\") && dataType !== \"signup_data\") {\r\n      const fetchMaskedEmail = async () => {\r\n        const savedData = JSON.parse(sessionStorage.getItem(dataType) || \"{}\");\r\n        const uuid = savedData.uuid;\r\n        const expiresAt = savedData.expiresAt;\r\n\r\n        if (!uuid || !expiresAt || Date.now() > expiresAt) {\r\n          toast.error(\"Signup session expired. Please start over.\");\r\n          sessionStorage.removeItem(dataType);\r\n          sessionStorage.removeItem(\"masked_email\");\r\n          sessionStorage.removeItem(\"identifier_type\");\r\n          router.replace(\"/signup\");\r\n          return;\r\n        }\r\n\r\n        const providerName = dataType.replace(\"signup_\", \"\");\r\n        console.log(\"Fetching email for:\", providerName, uuid);\r\n\r\n        try {\r\n          const response = await axios.get(\r\n            `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/auth/signup-data/${providerName}/${uuid}`\r\n          );\r\n\r\n          console.log(\"Fetched signup-data:\", response.data);\r\n\r\n          const { email } = response.data;\r\n\r\n          if (email) {\r\n            const masked = hashInput(email);\r\n            setMaskedEmail(masked);\r\n            sessionStorage.setItem(\"masked_email\", masked);\r\n            sessionStorage.setItem(\"identifier_type\", \"email\");\r\n          } else {\r\n            throw new Error(\"Email not found in response\");\r\n          }\r\n        } catch (error) {\r\n          if (error.response) {\r\n            console.error(\r\n              \"Fetch failed:\",\r\n              error.response.status,\r\n              error.response.data\r\n            );\r\n          } else {\r\n            console.error(\"Network or Axios error:\", error.message);\r\n          }\r\n          toast.error(\"Failed to retrieve your email. Please start over.\");\r\n          // Optionally redirect: router.replace(\"/signup\");\r\n        }\r\n      };\r\n\r\n      fetchMaskedEmail();\r\n    }\r\n  }, [isSessionValid, dataType]);\r\n\r\n  useEffect(() => {\r\n    const storedCooldown = localStorage.getItem(\"resend_cooldown\");\r\n    let intervalId;\r\n\r\n    if (storedCooldown) {\r\n      const remainingTime = Math.floor((+storedCooldown - Date.now()) / 1000);\r\n      if (remainingTime > 0) {\r\n        intervalId = startCooldown(remainingTime);\r\n      } else {\r\n        intervalId = startCooldown(60);\r\n      }\r\n    } else {\r\n      intervalId = startCooldown(60);\r\n    }\r\n\r\n    return () => clearInterval(intervalId);\r\n  }, []);\r\n\r\n  const startCooldown = (duration = 60) => {\r\n    setIsButtonDisabled(true);\r\n    setCooldown(duration);\r\n    localStorage.setItem(\"resend_cooldown\", Date.now() + duration * 1000);\r\n\r\n    const interval = setInterval(() => {\r\n      setCooldown((prev) => {\r\n        if (prev <= 1) {\r\n          clearInterval(interval);\r\n          setIsButtonDisabled(false);\r\n          localStorage.removeItem(\"resend_cooldown\");\r\n          return 0;\r\n        }\r\n        return prev - 1;\r\n      });\r\n    }, 1000);\r\n    return interval;\r\n  };\r\n\r\n  const handleSubmit = async (values, { setSubmitting, setErrors }) => {\r\n    // Handle email update verification\r\n    if (isEmailUpdate) {\r\n      if (!securitySessionId) {\r\n        setErrors({\r\n          security_code: \"Session expired. Please refresh the page.\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const response = await post(\"/account/email/verify\", {\r\n          code: values.security_code,\r\n          session_id: securitySessionId,\r\n        });\r\n\r\n        if (response.success) {\r\n          // Clear email update session data\r\n          sessionStorage.removeItem(\"email_update_session_id\");\r\n          sessionStorage.removeItem(\"email_update_masked_email\");\r\n          const newEmail = sessionStorage.getItem(\"email_update_new_email\");\r\n          sessionStorage.removeItem(\"email_update_new_email\");\r\n\r\n          // Update Redux store and localStorage with new email\r\n          if (newEmail && response.user) {\r\n            const updatedUser = { ...response.user, email: newEmail };\r\n            dispatch(setUser(updatedUser));\r\n            localStorage.setItem(\"user\", JSON.stringify(updatedUser));\r\n          }\r\n\r\n          // Redirect back to email setup page with success\r\n          const redirectUrl = rawNextUrl || \"/account/email/setup\";\r\n          router.push(`${redirectUrl}?success=email-updated`);\r\n        } else {\r\n          setErrors({\r\n            security_code: response.message || \"Invalid verification code\",\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Email update verification error:\", error);\r\n        setErrors({ security_code: \"Verification failed. Please try again.\" });\r\n      } finally {\r\n        setSubmitting(false);\r\n      }\r\n      return;\r\n    }\r\n\r\n    // Handle account security verification\r\n    if (isAccountSecurity) {\r\n      if (!securitySessionId) {\r\n        setErrors({\r\n          security_code: \"Session expired. Please refresh the page.\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const response = await post(\"/security-verification/verify-code\", {\r\n          code: values.security_code,\r\n          session_id: securitySessionId,\r\n          next: nextUrl,\r\n        });\r\n\r\n        if (response.success) {\r\n          let redirectUrl =\r\n            response.redirect_url || nextUrl || \"/account/overview\";\r\n\r\n          console.log(\"Security verification successful!\", {\r\n            backend_redirect_url: response.redirect_url,\r\n            frontend_next_url: nextUrl,\r\n            final_redirect_url: redirectUrl,\r\n            cookie_config: response.cookie_config,\r\n          });\r\n\r\n          // If redirectUrl is a full URL, extract just the path to avoid port/domain issues\r\n          try {\r\n            if (\r\n              redirectUrl.startsWith(\"http://\") ||\r\n              redirectUrl.startsWith(\"https://\")\r\n            ) {\r\n              const url = new URL(redirectUrl);\r\n              redirectUrl = url.pathname + url.search + url.hash;\r\n              console.log(\"Extracted path from full URL:\", redirectUrl);\r\n            }\r\n          } catch (e) {\r\n            // If URL parsing fails, use the original redirectUrl\r\n            console.warn(\"Failed to parse redirect URL:\", redirectUrl);\r\n          }\r\n\r\n          console.log(\r\n            \"Verification successful! Final redirect URL:\",\r\n            redirectUrl\r\n          );\r\n\r\n          // Backend should have set the security cookie, but set a fallback cookie\r\n          // to ensure the middleware recognizes the verification\r\n          const cookieConfig = response.cookie_config || {};\r\n          const cookieName = cookieConfig.name || \"security_verified\";\r\n\r\n          // Set a simple fallback cookie that the middleware can recognize\r\n          // Use the session ID from the response as the cookie value\r\n          const cookieValue = securitySessionId || \"verified\";\r\n          const expiresInMinutes = cookieConfig.expires_in_minutes || 10;\r\n          const expires = new Date(\r\n            Date.now() + expiresInMinutes * 60 * 1000\r\n          ).toUTCString();\r\n\r\n          // Set the cookie with proper attributes\r\n          let cookieString = `${cookieName}=${cookieValue}`;\r\n          cookieString += `; expires=${expires}`;\r\n          cookieString += `; path=${cookieConfig.path || \"/\"}`;\r\n\r\n          if (cookieConfig.domain) {\r\n            cookieString += `; domain=${cookieConfig.domain}`;\r\n          }\r\n\r\n          if (cookieConfig.same_site) {\r\n            cookieString += `; SameSite=${cookieConfig.same_site}`;\r\n          }\r\n\r\n          // Note: Cannot set Secure or HttpOnly via JavaScript\r\n          document.cookie = cookieString;\r\n\r\n          console.log(\"Set fallback security cookie:\", {\r\n            cookieName,\r\n            cookieValue,\r\n            cookieString,\r\n          });\r\n\r\n          // Use router.replace with a longer delay to ensure cookie is properly set\r\n          // This gives time for the backend cookie to be processed by the browser\r\n          console.log(\"Redirecting to:\", redirectUrl);\r\n\r\n          // Debug: Check if cookie was set after a short delay\r\n          setTimeout(() => {\r\n            const cookieName =\r\n              response.cookie_config?.name || \"security_verified\";\r\n            const cookieValue = getCookie(cookieName);\r\n            console.log(\"Cookie check before redirect:\", {\r\n              cookieName,\r\n              cookieValue,\r\n              isValid: cookieValue ? isValidSecurityCookie(cookieValue) : false,\r\n            });\r\n          }, 100);\r\n\r\n          setTimeout(() => {\r\n            console.log(\"Executing redirect to:\", redirectUrl);\r\n\r\n            // Use window.location.href for production reliability\r\n            // router.replace() can be unreliable in production environments\r\n            window.location.href = redirectUrl;\r\n          }, 200); // Short delay to ensure cookie is set\r\n        } else {\r\n          console.log(\"Verification failed:\", response.message);\r\n          setErrors({\r\n            security_code:\r\n              response.message || \"Invalid verification code. Try again.\",\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.log(\"Verification error:\", error);\r\n        setErrors({ security_code: \"Verification failed. Please try again.\" });\r\n      }\r\n      return;\r\n    }\r\n\r\n    // Original signup/reset password logic\r\n    const savedData = JSON.parse(sessionStorage.getItem(dataType));\r\n\r\n    if (!savedData || Date.now() > savedData.expiresAt) {\r\n      setErrors({\r\n        security_code: \"Token has expired. Please request a new one.\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    const payload = {\r\n      type: dataType,\r\n      token: values.security_code,\r\n      ...{ uuid: savedData.uuid },\r\n    };\r\n\r\n    const response = await verifyEmailToken(payload);\r\n\r\n    if (response?.success) {\r\n      const redirectTo = resetPassword\r\n        ? \"/change-password\"\r\n        : `/create-username${provider ? `?provider=${provider}` : \"\"}`;\r\n\r\n      router.push(redirectTo);\r\n    } else {\r\n      setErrors({\r\n        security_code:\r\n          response?.message || \"Invalid verification code. Try again.\",\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleResendClick = async (setErrors) => {\r\n    if (isButtonDisabled) return;\r\n\r\n    setIsRotating(true);\r\n    startCooldown();\r\n\r\n    // Handle account security verification resend\r\n    if (isAccountSecurity) {\r\n      if (!securitySessionId) {\r\n        setErrors({\r\n          security_code: \"Session expired. Please refresh the page.\",\r\n        });\r\n        setIsRotating(false);\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const response = await post(\"/security-verification/resend-code\", {\r\n          session_id: securitySessionId,\r\n        });\r\n\r\n        if (response.success) {\r\n          setResendMessage(\r\n            \"Verification code resent! Please check your email (including spam or junk folders) for the new code. If you don’t receive it within a few minutes, try resending or contact our support team for assistance.\"\r\n          );\r\n        } else {\r\n          setErrors({\r\n            security_code: response.message || \"Failed to resend code.\",\r\n          });\r\n        }\r\n      } catch (error) {\r\n        setErrors({\r\n          security_code: \"Failed to resend code. Please try again.\",\r\n        });\r\n      } finally {\r\n        setIsRotating(false);\r\n      }\r\n      return;\r\n    }\r\n\r\n    const existingData = JSON.parse(sessionStorage.getItem(dataType)) || {};\r\n    const oldUuid = existingData.uuid || uuidv4();\r\n\r\n    try {\r\n      const payload = { type: dataType, uuid: oldUuid };\r\n      const response = await resendVerificationCode(payload);\r\n\r\n      if (response.status === 429) {\r\n        router.push(resetPassword ? \"/locate-account\" : \"/signup\");\r\n        return;\r\n      }\r\n\r\n      if (!response.success) {\r\n        setErrors({\r\n          security_code: response.message || \"Something went wrong.\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      // ✅ Cache does not exist → resend success\r\n      const expiresInMinutes = 15;\r\n      const expiresAt = Date.now() + expiresInMinutes * 60 * 1000;\r\n      sessionStorage.setItem(\r\n        dataType,\r\n        JSON.stringify({ uuid: oldUuid, expiresAt })\r\n      );\r\n\r\n      setResendMessage(\r\n        \"Verification code resent! Please check your email (including spam or junk folders) for the new code. If you don’t receive it within a few minutes, try resending or contact our support team for assistance.\"\r\n      );\r\n    } catch (error) {\r\n      console.error(error);\r\n      setErrors({ security_code: \"Something went wrong. Please try again.\" });\r\n    } finally {\r\n      setIsRotating(false);\r\n    }\r\n  };\r\n\r\n  // Restore code handlers\r\n  const handleUseRestoreCode = () => {\r\n    setShowUseRestoreCodeModal(true);\r\n  };\r\n\r\n  const handleRestoreCodeSubmit = async (restoreCode) => {\r\n    if (!securitySessionId) {\r\n      toast.error(\"Session expired. Please refresh the page.\");\r\n      return;\r\n    }\r\n\r\n    setIsVerifyingRestoreCode(true);\r\n    try {\r\n      const response = await verifyRestoreCode(restoreCode, securitySessionId);\r\n\r\n      if (response.success) {\r\n        // Close the restore code modal\r\n        setShowUseRestoreCodeModal(false);\r\n        // Set the new restore code and show the new restore code modal\r\n        setNewRestoreCode(response.data.new_restore_code);\r\n        setShowNewRestoreCodeModal(true);\r\n        // After showing the new restore code, proceed with the security verification\r\n        // We'll handle the redirect after the user closes the new restore code modal\r\n      } else {\r\n        toast.error(\r\n          response.message || \"Invalid restore code. Please try again.\"\r\n        );\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Restore code verification error:\", error);\r\n      toast.error(\"Verification failed. Please try again.\");\r\n    } finally {\r\n      setIsVerifyingRestoreCode(false);\r\n    }\r\n  };\r\n\r\n  const handleNewRestoreCodeClose = () => {\r\n    setShowNewRestoreCodeModal(false);\r\n    setNewRestoreCode(\"\");\r\n\r\n    // Complete the security verification flow by redirecting to the intended destination\r\n    if (nextUrl) {\r\n      let redirectUrl = nextUrl;\r\n      try {\r\n        if (\r\n          redirectUrl.startsWith(\"http://\") ||\r\n          redirectUrl.startsWith(\"https://\")\r\n        ) {\r\n          const url = new URL(redirectUrl);\r\n          redirectUrl = url.pathname + url.search + url.hash;\r\n        }\r\n      } catch (e) {\r\n        console.warn(\"Failed to parse redirect URL:\", redirectUrl);\r\n      }\r\n      window.location.href = redirectUrl;\r\n    } else {\r\n      window.location.href = \"/account/overview\";\r\n    }\r\n  };\r\n\r\n  const handleChange = (e, index) => {\r\n    const value = e.target.value;\r\n    if (/^[a-zA-Z0-9]$/.test(value)) {\r\n      if (index < inputRefs.current.length - 1) {\r\n        inputRefs.current[index + 1].focus();\r\n      }\r\n    } else {\r\n      e.target.value = \"\";\r\n    }\r\n  };\r\n\r\n  const handleKeyDown = (e, index) => {\r\n    if (e.key === \"Backspace\" && !e.target.value && index > 0) {\r\n      inputRefs.current[index - 1].focus();\r\n    }\r\n  };\r\n\r\n  const metaArray = {\r\n    noindex: true,\r\n    title: \"Security Check | Verify Your TradeReply Account\",\r\n    description:\r\n      \"Enter your authorization code to verify your identity and securely access your TradeReply account. Protect your trading data with an extra layer of security.\",\r\n    canonical_link: \"https://www.tradereply.com/security-check\",\r\n    og_site_name: \"TradeReply\",\r\n    og_title: \"Security Check | Verify Your TradeReply Account\",\r\n    og_description:\r\n      \"Enter your authorization code to verify your identity and securely access your TradeReply account. Protect your trading data with an extra layer of security.\",\r\n    twitter_title: \"Security Check | Verify Your TradeReply Account\",\r\n    twitter_description:\r\n      \"Enter your authorization code to verify your identity and securely access your TradeReply account. Protect your trading data with an extra layer of security.\",\r\n  };\r\n\r\n  // Show proper loading while checking for existing security cookie\r\n  if (isCheckingExistingCookie && isAccountSecurity) {\r\n    return (\r\n      <AuthLayout>\r\n        <MetaHead props={metaArray} />\r\n        <div className=\"loginCommon_rightSide security_check\">\r\n          <div className=\"loginCommon_rightSide_inner\">\r\n            <div className=\"loginCommon_rightSide_formBox\">\r\n              <AuthLogo />\r\n              <div className=\"loginHeading\">\r\n                <h1>Security Check</h1>\r\n              </div>\r\n              <div className=\"text-center py-5\">\r\n                <div className=\"security-check-loading\">\r\n                  <div className=\"loading-spinner mb-3\">\r\n                    <div className=\"spinner-border text-primary\" role=\"status\">\r\n                      <span className=\"visually-hidden\">Loading...</span>\r\n                    </div>\r\n                  </div>\r\n                  <h5 className=\"mb-2\">Verifying Security Check</h5>\r\n                  <p className=\"text-white\">\r\n                    Please wait while we verify your security status...\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"mt-4 mt-md-5\">\r\n              <LoginFooter />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <style jsx>{`\r\n          .security-check-loading {\r\n            padding: 2rem 0;\r\n          }\r\n          .loading-spinner {\r\n            display: flex;\r\n            justify-content: center;\r\n            align-items: center;\r\n          }\r\n          .spinner-border {\r\n            width: 3rem;\r\n            height: 3rem;\r\n            border-width: 0.3em;\r\n          }\r\n          .text-primary {\r\n            color: #007bff !important;\r\n          }\r\n          .text-muted {\r\n            color: #6c757d !important;\r\n            font-size: 0.9rem;\r\n          }\r\n          .visually-hidden {\r\n            position: absolute !important;\r\n            width: 1px !important;\r\n            height: 1px !important;\r\n            padding: 0 !important;\r\n            margin: -1px !important;\r\n            overflow: hidden !important;\r\n            clip: rect(0, 0, 0, 0) !important;\r\n            white-space: nowrap !important;\r\n            border: 0 !important;\r\n          }\r\n        `}</style>\r\n      </AuthLayout>\r\n    );\r\n  }\r\n\r\n  // Show professional loading screen for signup/reset password flows\r\n  if (isSessionValid === null) {\r\n    return (\r\n      <AuthLayout>\r\n        <MetaHead props={metaArray} />\r\n        <div className=\"loginCommon_rightSide security_check\">\r\n          <div className=\"loginCommon_rightSide_inner\">\r\n            <div className=\"loginCommon_rightSide_formBox\">\r\n              <AuthLogo />\r\n              <div className=\"loginHeading\">\r\n                <h1>Security Check</h1>\r\n              </div>\r\n              <div className=\"text-center py-5\">\r\n                <div className=\"security-check-loading\">\r\n                  <div className=\"loading-spinner mb-3\">\r\n                    <div className=\"spinner-border text-primary\" role=\"status\">\r\n                      <span className=\"visually-hidden\">Loading...</span>\r\n                    </div>\r\n                  </div>\r\n                  <h5 className=\"mb-2\">Verifying Your Information</h5>\r\n                  <p className=\"text-white\">\r\n                    {resetPassword\r\n                      ? \"Please wait while we verify your account details...\"\r\n                      : \"Please wait while we verify your signup details...\"}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"mt-4 mt-md-5\">\r\n              <LoginFooter />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <style jsx>{`\r\n          .security-check-loading {\r\n            padding: 2rem 0;\r\n          }\r\n          .loading-spinner {\r\n            display: flex;\r\n            justify-content: center;\r\n            align-items: center;\r\n          }\r\n          .spinner-border {\r\n            width: 3rem;\r\n            height: 3rem;\r\n            border-width: 0.3em;\r\n          }\r\n          .text-primary {\r\n            color: #007bff !important;\r\n          }\r\n          .text-muted {\r\n            color: #6c757d !important;\r\n            font-size: 0.9rem;\r\n          }\r\n          .visually-hidden {\r\n            position: absolute !important;\r\n            width: 1px !important;\r\n            height: 1px !important;\r\n            padding: 0 !important;\r\n            margin: -1px !important;\r\n            overflow: hidden !important;\r\n            clip: rect(0, 0, 0, 0) !important;\r\n            white-space: nowrap !important;\r\n            border: 0 !important;\r\n          }\r\n        `}</style>\r\n      </AuthLayout>\r\n    );\r\n  }\r\n\r\n  if (isSessionValid === false) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <AuthLayout>\r\n      <MetaHead props={metaArray} />\r\n      <div className=\"loginCommon_rightSide security_check\">\r\n        <div className=\"loginCommon_rightSide_inner\">\r\n          <div className=\"loginCommon_rightSide_formBox\">\r\n            <AuthLogo />\r\n            <div className=\"loginHeading\">\r\n              <h1>Security Check</h1>\r\n            </div>\r\n            <div id=\"auth-code-label\">\r\n              <div className=\"text-center pt-3\">\r\n                <div className=\"text-center pt-3\">\r\n                  {isEmailUpdate ? (\r\n                    <span className=\"font-semibold\">\r\n                      We’ve sent a code to your new email address. Please enter\r\n                      it below to confirm you have access.\r\n                    </span>\r\n                  ) : isAccountSecurity ? (\r\n                    <span className=\"font-semibold\">\r\n                      For your security, please enter the verification code sent\r\n                      to your email address to continue.\r\n                    </span>\r\n                  ) : dataType === \"reset_password_data\" ? (\r\n                    <>\r\n                      {type === \"email\" || !type ? (\r\n                        <span className=\"font-semibold\">\r\n                          {type === \"email\" || !type ? (\r\n                            \"If an account with the entered email or username exists, a verification code has been sent.\"\r\n                          ) : (\r\n                            <>\r\n                              Please enter the verification code sent to{\" \"}\r\n                              <strong>{maskedEmail}</strong>.\r\n                            </>\r\n                          )}\r\n                        </span>\r\n                      ) : (\r\n                        <span className=\"font-semibold\">\r\n                          Please enter the verification code sent to the email\r\n                          associated with <strong>{maskedEmail}</strong>. If you\r\n                          don't receive an email, check your spam folder or\r\n                          contact support for assistance.\r\n                        </span>\r\n                      )}\r\n                    </>\r\n                  ) : (\r\n                    <span>Please enter the verification code sent to:</span>\r\n                  )}\r\n                </div>\r\n              </div>\r\n              <div className=\"text-center py-3 user_email\">\r\n                <span>{maskedEmail}</span>\r\n              </div>\r\n            </div>\r\n            <div className=\"loginTabs\">\r\n              <div className=\"loginForm\">\r\n                <Formik\r\n                  initialValues={initialValues}\r\n                  validationSchema={securitySchema}\r\n                  onSubmit={handleSubmit}\r\n                >\r\n                  {({ isSubmitting }) => (\r\n                    <Form>\r\n                      <Field name=\"security_code\">\r\n                        {({ field, form, meta }) => {\r\n                          const handlePaste = (e) => {\r\n                            e.preventDefault();\r\n                            const pasteData = e.clipboardData.getData(\"Text\");\r\n                            const cleaned = pasteData.replace(\r\n                              /[^a-zA-Z0-9]/g,\r\n                              \"\"\r\n                            );\r\n                            const chars = cleaned.split(\"\").slice(0, 6);\r\n                            let newCode = new Array(6).fill(\"\");\r\n                            chars.forEach((char, i) => {\r\n                              newCode[i] = char;\r\n                              if (inputRefs.current[i]) {\r\n                                inputRefs.current[i].value = char;\r\n                              }\r\n                            });\r\n                            form.setFieldValue(field.name, newCode.join(\"\"));\r\n                            if (chars.length < inputRefs.current.length) {\r\n                              inputRefs.current[chars.length].focus();\r\n                            }\r\n                          };\r\n\r\n                          const handleResend = () => {\r\n                            inputRefs.current.forEach((input) => {\r\n                              if (input) input.value = \"\";\r\n                            });\r\n\r\n                            form.setFieldValue(\"security_code\", \"\");\r\n                            form.setFieldError(\"security_code\", \"\");\r\n\r\n                            handleResendClick({\r\n                              security_code: () => {\r\n                                form.setFieldError(\r\n                                  \"security_code\",\r\n                                  \"Failed to resend code.\"\r\n                                );\r\n                              },\r\n                            });\r\n\r\n                            inputRefs.current[0]?.focus();\r\n                          };\r\n\r\n                          return (\r\n                            <>\r\n                              <div className=\"py-3 security_check_input\">\r\n                                <div\r\n                                  role=\"group\"\r\n                                  aria-labelledby=\"auth-code-label\"\r\n                                >\r\n                                  <div className=\"d-flex justify-content-between\">\r\n                                    {Array.from({ length: 6 }).map(\r\n                                      (_, index) => {\r\n                                        const charValue = field.value\r\n                                          ? field.value[index] || \"\"\r\n                                          : \"\";\r\n                                        return (\r\n                                          <input\r\n                                            key={index}\r\n                                            type=\"text\"\r\n                                            maxLength=\"1\"\r\n                                            id={`auth-code-${index + 1}`}\r\n                                            inputMode=\"text\"\r\n                                            aria-label={`${\r\n                                              [\r\n                                                \"First\",\r\n                                                \"Second\",\r\n                                                \"Third\",\r\n                                                \"Fourth\",\r\n                                                \"Fifth\",\r\n                                                \"Sixth\",\r\n                                              ][index]\r\n                                            } character`}\r\n                                            value={charValue}\r\n                                            onChange={(e) => {\r\n                                              const val = e.target.value;\r\n                                              let newCode = field.value\r\n                                                ? field.value.split(\"\")\r\n                                                : new Array(6).fill(\"\");\r\n                                              newCode[index] = val;\r\n                                              form.setFieldValue(\r\n                                                field.name,\r\n                                                newCode.join(\"\")\r\n                                              );\r\n                                              handleChange(e, index);\r\n                                            }}\r\n                                            onKeyDown={(e) =>\r\n                                              handleKeyDown(e, index)\r\n                                            }\r\n                                            onPaste={handlePaste}\r\n                                            ref={(el) =>\r\n                                              (inputRefs.current[index] = el)\r\n                                            }\r\n                                            className={\r\n                                              meta.touched && meta.error\r\n                                                ? \"error-field\"\r\n                                                : \"\"\r\n                                            }\r\n                                          />\r\n                                        );\r\n                                      }\r\n                                    )}\r\n                                  </div>\r\n                                  {meta.touched && meta.error ? (\r\n                                    <InputError message={meta.error} />\r\n                                  ) : null}\r\n                                </div>\r\n                              </div>\r\n\r\n                              <div className=\"pb-3 d-flex justify-content-center\">\r\n                                <button\r\n                                  type=\"button\"\r\n                                  className=\"security_check_resend_btn d-flex align-items-center gap-3\"\r\n                                  onClick={handleResend}\r\n                                  disabled={isButtonDisabled}\r\n                                >\r\n                                  Resend Code\r\n                                  <ResendCodeIcon isRotating={isRotating} />\r\n                                  <span>\r\n                                    {cooldown > 0 ? `${cooldown}s` : \"\"}\r\n                                  </span>\r\n                                </button>\r\n                              </div>\r\n\r\n                              {/* Use Restore Code Button - Only show for account security verification */}\r\n                              {isAccountSecurity && (\r\n                                <div className=\"pb-3 flex justify-center items-center gap-2 font-semibold\">\r\n                                  <span>Having trouble?</span>\r\n                                  <button\r\n                                    type=\"button\"\r\n                                    className=\"flex items-center gap-2 rounded-full bg-[#2e5aac] text-white text-sm py-2 px-6 cursor-pointer transition-colors duration-200 hover:bg-[#3e6bc1]\"\r\n                                    onClick={handleUseRestoreCode}\r\n                                  >\r\n                                    <span className=\"font-medium\">\r\n                                      Use Restore Code\r\n                                    </span>\r\n                                  </button>\r\n                                </div>\r\n                              )}\r\n                            </>\r\n                          );\r\n                        }}\r\n                      </Field>\r\n                      {resendMessage && (\r\n                        <div className=\"text-center text-success mt-3 mb-6 px-3\">\r\n                          <p>{resendMessage}</p>\r\n                        </div>\r\n                      )}\r\n                      <div className=\"w-100\">\r\n                        <CommonButton\r\n                          type=\"submit\"\r\n                          title={isSubmitting ? \"Loading\" : \"Continue\"}\r\n                          fluid\r\n                          disabled={isSubmitting}\r\n                        />\r\n                      </div>\r\n                      <Link\r\n                        href={\r\n                          isAccountSecurity\r\n                            ? \"/account/details\"\r\n                            : isEmailUpdate\r\n                            ? \"/account/email/setup\"\r\n                            : \"/dashboard\"\r\n                        }\r\n                        className=\"w-100 mt-3\"\r\n                      >\r\n                        <CommonButton title=\"Cancel\" white20 />\r\n                      </Link>\r\n                      <div className=\"anAccount mt-3 d-flex justify-content-center\">\r\n                        <a\r\n                          href=\"/help\"\r\n                          target=\"_blank\"\r\n                          rel=\"noopener\"\r\n                          className=\"d-flex align-items-center gap-2\"\r\n                        >\r\n                          <span className=\"font-bold\">\r\n                            Contact Customer Support\r\n                          </span>\r\n                          <ContactCustomerSupport />\r\n                        </a>\r\n                      </div>\r\n                    </Form>\r\n                  )}\r\n                </Formik>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"mt-4 mt-md-5\">\r\n            <LoginFooter />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Restore Code Modals */}\r\n      <UseRestoreCodeModal\r\n        show={showUseRestoreCodeModal}\r\n        handleClose={() => setShowUseRestoreCodeModal(false)}\r\n        onSubmit={handleRestoreCodeSubmit}\r\n        isSubmitting={isVerifyingRestoreCode}\r\n      />\r\n\r\n      <NewRestoreCodeModal\r\n        show={showNewRestoreCodeModal}\r\n        handleClose={handleNewRestoreCodeClose}\r\n        restoreCode={newRestoreCode}\r\n      />\r\n    </AuthLayout>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AAhCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,MAAM,gBAAgB;IACpB,eAAe;AACjB;AAEe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAC3B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,0BAA0B,4BAA4B,GAC3D,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACX,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,4BAA4B;IAC5B,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErE,MAAM,WAAW,aAAa,GAAG,CAAC;IAClC,MAAM,gBAAgB,aAAa,GAAG,CAAC;IACvC,MAAM,aAAa,aAAa,GAAG,CAAC,SAAS,oCAAoC;IACjF,MAAM,mBAAmB,aAAa,GAAG,CAAC,SAAS,gCAAgC;IACnF,MAAM,gBAAgB,qBAAqB;IAE3C,gFAAgF;IAChF,MAAM,0BAA0B,+HAAA,CAAA,4BAAyB;IAEzD,6EAA6E;IAE7E,qCAAqC;IACrC,MAAM,UACJ,cAAc,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD,EAAE,cAAc,aAAa;IAC9D,MAAM,oBAAoB,CAAC,CAAC,WAAW,CAAC,eAAe,0FAA0F;IAEjJ,yCAAyC;IACzC,IAAI,cAAc,CAAC,SAAS;QAC1B,QAAQ,IAAI,CAAC,2CAA2C;IAC1D;IAEA,MAAM,WAAW,gBACb,wBACA,WACA,CAAC,OAAO,EAAE,UAAU,GACpB;IAEJ,gDAAgD;IAChD,MAAM,wBAAwB,CAAC;QAC7B,IAAI,CAAC,aAAa,OAAO;QAEzB,8CAA8C;QAC9C,IAAI,gBAAgB,QAAQ,OAAO;QAEnC,oDAAoD;QACpD,IAAI,gBAAgB,YAAY,OAAO;QAEvC,0DAA0D;QAC1D,IACE,OAAO,gBAAgB,YACvB,YAAY,MAAM,KAAK,MACvB,eAAe,IAAI,CAAC,cACpB;YACA,OAAO;QACT;QAEA,6CAA6C;QAC7C,IACE,OAAO,gBAAgB,YACvB,kEAAkE,IAAI,CACpE,cAEF;YACA,OAAO;QACT;QAEA,4CAA4C;QAC5C,IAAI;YACF,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK;YAChC,IAAI,QAAQ,WAAW,EAAE;gBACvB,MAAM,aAAa,IAAI,KAAK,QAAQ,WAAW;gBAC/C,MAAM,MAAM,IAAI;gBAChB,MAAM,gBAAgB,CAAC,IAAI,OAAO,KAAK,WAAW,OAAO,EAAE,IAAI;gBAC/D,+EAA+E;gBAC/E,gEAAgE;gBAChE,OAAO,iBAAiB;YAC1B;QACF,EAAE,OAAO,GAAG;YACV,OAAO;QACT;QACA,OAAO;IACT;IAEA,+BAA+B;IAC/B,MAAM,YAAY,CAAC;QACjB,MAAM,QAAQ,CAAC,EAAE,EAAE,SAAS,MAAM,EAAE;QACpC,MAAM,QAAQ,MAAM,KAAK,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QACtC,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO,MAAM,GAAG,GAAG,KAAK,CAAC,KAAK,KAAK;QAC3D,OAAO;IACT;IAEA,2BAA2B;IAC3B,oEAAoE;IACpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,qBAAqB,SAAS;YAChC,2CAA2C;YAC3C,MAAM,SAAS,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD;YAC/B,MAAM,aAAa,OAAO,WAAW;YAErC,MAAM,iBAAiB,UAAU;YACjC,IAAI,kBAAkB,sBAAsB,iBAAiB;gBAC3D,QAAQ,GAAG,CACT,4DACA;gBAGF,uCAAuC;gBACvC,IAAI,cAAc;gBAClB,IAAI;oBACF,IACE,YAAY,UAAU,CAAC,cACvB,YAAY,UAAU,CAAC,aACvB;wBACA,MAAM,MAAM,IAAI,IAAI;wBACpB,cAAc,IAAI,QAAQ,GAAG,IAAI,MAAM,GAAG,IAAI,IAAI;oBACpD;gBACF,EAAE,OAAO,GAAG;oBACV,QAAQ,IAAI,CAAC,6BAA6B;gBAC5C;gBAEA,QAAQ,GAAG,CAAC,yCAAyC;gBAErD,sDAAsD;gBACtD,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACvB;YACF;QACF;QAEA,yEAAyE;QACzE,4BAA4B;IAC9B,GAAG;QAAC;QAAmB;QAAS;KAAO;IAEvC,2BAA2B;IAC3B,mBAAmB;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,uBAAuB;YAC3B,qFAAqF;YACrF,IAAI,qBAAqB,eAAe;gBACtC,OAAO;YACT;YAEA,2EAA2E;YAC3E,IAAI,CAAC,YAAY,CAAC,YAAY;gBAC5B,QAAQ,IAAI,CACV;gBAEF,OAAO,OAAO,CAAC;gBACf,OAAO;YACT;YAEA,IAAI,CAAC,UAAU,OAAO;YAEtB,MAAM,YAAY,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC,aAAa;YAEjE,IACE,CAAC,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,SAAS,KACxC,CAAC,eAAe,OAAO,EACvB;gBACA,eAAe,OAAO,GAAG;gBACzB,OAAO,OAAO,CAAC;gBACf,OAAO;YACT;YAEA,MAAM,UAAU,KAAK,GAAG,KAAK,UAAU,SAAS;YAEhD,IAAI,WAAW,CAAC,eAAe,OAAO,EAAE;gBACtC,eAAe,OAAO,GAAG;gBACzB,eAAe,UAAU,CAAC;gBAC1B,eAAe,UAAU,CAAC;gBAC1B,eAAe,UAAU,CAAC;gBAE1B,eAAe,OAAO,CAAC,kBAAkB;gBACzC,8DAA8D;gBAE9D,OAAO,OAAO,CAAC,gBAAgB,oBAAoB;gBACnD,OAAO;YACT;YAEA,OAAO;QACT;QAEA,IAAI,wBAAwB;YAC1B,kBAAkB;YAElB,MAAM,WAAW,YAAY,sBAAsB;YACnD,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC;QAAU;KAAkB;IAEhC,2BAA2B;IAC3B,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,gBAAgB;QAErB,+DAA+D;QAC/D,IAAI,eAAe;YACjB,MAAM,YAAY,eAAe,OAAO,CAAC;YACzC,MAAM,cAAc,eAAe,OAAO,CAAC;YAE3C,IAAI,aAAa,aAAa;gBAC5B,qBAAqB;gBACrB,eAAe;gBACf,QAAQ;YACV,OAAO;gBACL,QAAQ,IAAI,CAAC;gBACb,OAAO,OAAO,CAAC;YACjB;YACA;QACF;QAEA,wEAAwE;QACxE,IAAI,mBAAmB;YACrB,MAAM,mBAAmB;gBACvB,IAAI;oBACF,MAAM,WAAW,MAAM,CAAA,GAAA,wHAAA,CAAA,OAAI,AAAD,EAAE;oBAC5B,IAAI,SAAS,OAAO,EAAE;wBACpB,qBAAqB,SAAS,UAAU;wBACxC,eAAe,SAAS,YAAY,IAAI;wBACxC,QAAQ;oBACV,OAAO;wBACL,QAAQ,GAAG,CAAC,oCAAoC;wBAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;wBACZ,OAAO,OAAO,CAAC;oBACjB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,GAAG,CAAC,oCAAoC;oBAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;oBACZ,OAAO,OAAO,CAAC;gBACjB;YACF;YACA;YACA;QACF;QAEA,MAAM,SAAS,eAAe,OAAO,CAAC;QACtC,MAAM,iBAAiB,eAAe,OAAO,CAAC;QAE9C,IAAI,QAAQ;YACV,eAAe;YACf,IAAI,gBAAgB,QAAQ;YAC5B;QACF;QAEA,IAAI,SAAS,UAAU,CAAC,cAAc,aAAa,eAAe;YAChE,MAAM,mBAAmB;gBACvB,MAAM,YAAY,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC,aAAa;gBACjE,MAAM,OAAO,UAAU,IAAI;gBAC3B,MAAM,YAAY,UAAU,SAAS;gBAErC,IAAI,CAAC,QAAQ,CAAC,aAAa,KAAK,GAAG,KAAK,WAAW;oBACjD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;oBACZ,eAAe,UAAU,CAAC;oBAC1B,eAAe,UAAU,CAAC;oBAC1B,eAAe,UAAU,CAAC;oBAC1B,OAAO,OAAO,CAAC;oBACf;gBACF;gBAEA,MAAM,eAAe,SAAS,OAAO,CAAC,WAAW;gBACjD,QAAQ,GAAG,CAAC,uBAAuB,cAAc;gBAEjD,IAAI;oBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,6DAAwC,yBAAyB,EAAE,aAAa,CAAC,EAAE,MAAM;oBAG3F,QAAQ,GAAG,CAAC,wBAAwB,SAAS,IAAI;oBAEjD,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS,IAAI;oBAE/B,IAAI,OAAO;wBACT,MAAM,SAAS,CAAA,GAAA,yHAAA,CAAA,YAAS,AAAD,EAAE;wBACzB,eAAe;wBACf,eAAe,OAAO,CAAC,gBAAgB;wBACvC,eAAe,OAAO,CAAC,mBAAmB;oBAC5C,OAAO;wBACL,MAAM,IAAI,MAAM;oBAClB;gBACF,EAAE,OAAO,OAAO;oBACd,IAAI,MAAM,QAAQ,EAAE;wBAClB,QAAQ,KAAK,CACX,iBACA,MAAM,QAAQ,CAAC,MAAM,EACrB,MAAM,QAAQ,CAAC,IAAI;oBAEvB,OAAO;wBACL,QAAQ,KAAK,CAAC,2BAA2B,MAAM,OAAO;oBACxD;oBACA,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACZ,kDAAkD;gBACpD;YACF;YAEA;QACF;IACF,GAAG;QAAC;QAAgB;KAAS;IAE7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB,aAAa,OAAO,CAAC;QAC5C,IAAI;QAEJ,IAAI,gBAAgB;YAClB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,CAAC,iBAAiB,KAAK,GAAG,EAAE,IAAI;YAClE,IAAI,gBAAgB,GAAG;gBACrB,aAAa,cAAc;YAC7B,OAAO;gBACL,aAAa,cAAc;YAC7B;QACF,OAAO;YACL,aAAa,cAAc;QAC7B;QAEA,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAC,WAAW,EAAE;QAClC,oBAAoB;QACpB,YAAY;QACZ,aAAa,OAAO,CAAC,mBAAmB,KAAK,GAAG,KAAK,WAAW;QAEhE,MAAM,WAAW,YAAY;YAC3B,YAAY,CAAC;gBACX,IAAI,QAAQ,GAAG;oBACb,cAAc;oBACd,oBAAoB;oBACpB,aAAa,UAAU,CAAC;oBACxB,OAAO;gBACT;gBACA,OAAO,OAAO;YAChB;QACF,GAAG;QACH,OAAO;IACT;IAEA,MAAM,eAAe,OAAO,QAAQ,EAAE,aAAa,EAAE,SAAS,EAAE;QAC9D,mCAAmC;QACnC,IAAI,eAAe;YACjB,IAAI,CAAC,mBAAmB;gBACtB,UAAU;oBACR,eAAe;gBACjB;gBACA;YACF;YAEA,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,wHAAA,CAAA,OAAI,AAAD,EAAE,yBAAyB;oBACnD,MAAM,OAAO,aAAa;oBAC1B,YAAY;gBACd;gBAEA,IAAI,SAAS,OAAO,EAAE;oBACpB,kCAAkC;oBAClC,eAAe,UAAU,CAAC;oBAC1B,eAAe,UAAU,CAAC;oBAC1B,MAAM,WAAW,eAAe,OAAO,CAAC;oBACxC,eAAe,UAAU,CAAC;oBAE1B,qDAAqD;oBACrD,IAAI,YAAY,SAAS,IAAI,EAAE;wBAC7B,MAAM,cAAc;4BAAE,GAAG,SAAS,IAAI;4BAAE,OAAO;wBAAS;wBACxD,SAAS,CAAA,GAAA,yHAAA,CAAA,UAAO,AAAD,EAAE;wBACjB,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;oBAC9C;oBAEA,iDAAiD;oBACjD,MAAM,cAAc,cAAc;oBAClC,OAAO,IAAI,CAAC,GAAG,YAAY,sBAAsB,CAAC;gBACpD,OAAO;oBACL,UAAU;wBACR,eAAe,SAAS,OAAO,IAAI;oBACrC;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,UAAU;oBAAE,eAAe;gBAAyC;YACtE,SAAU;gBACR,cAAc;YAChB;YACA;QACF;QAEA,uCAAuC;QACvC,IAAI,mBAAmB;YACrB,IAAI,CAAC,mBAAmB;gBACtB,UAAU;oBACR,eAAe;gBACjB;gBACA;YACF;YAEA,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,wHAAA,CAAA,OAAI,AAAD,EAAE,sCAAsC;oBAChE,MAAM,OAAO,aAAa;oBAC1B,YAAY;oBACZ,MAAM;gBACR;gBAEA,IAAI,SAAS,OAAO,EAAE;oBACpB,IAAI,cACF,SAAS,YAAY,IAAI,WAAW;oBAEtC,QAAQ,GAAG,CAAC,qCAAqC;wBAC/C,sBAAsB,SAAS,YAAY;wBAC3C,mBAAmB;wBACnB,oBAAoB;wBACpB,eAAe,SAAS,aAAa;oBACvC;oBAEA,kFAAkF;oBAClF,IAAI;wBACF,IACE,YAAY,UAAU,CAAC,cACvB,YAAY,UAAU,CAAC,aACvB;4BACA,MAAM,MAAM,IAAI,IAAI;4BACpB,cAAc,IAAI,QAAQ,GAAG,IAAI,MAAM,GAAG,IAAI,IAAI;4BAClD,QAAQ,GAAG,CAAC,iCAAiC;wBAC/C;oBACF,EAAE,OAAO,GAAG;wBACV,qDAAqD;wBACrD,QAAQ,IAAI,CAAC,iCAAiC;oBAChD;oBAEA,QAAQ,GAAG,CACT,gDACA;oBAGF,yEAAyE;oBACzE,uDAAuD;oBACvD,MAAM,eAAe,SAAS,aAAa,IAAI,CAAC;oBAChD,MAAM,aAAa,aAAa,IAAI,IAAI;oBAExC,iEAAiE;oBACjE,2DAA2D;oBAC3D,MAAM,cAAc,qBAAqB;oBACzC,MAAM,mBAAmB,aAAa,kBAAkB,IAAI;oBAC5D,MAAM,UAAU,IAAI,KAClB,KAAK,GAAG,KAAK,mBAAmB,KAAK,MACrC,WAAW;oBAEb,wCAAwC;oBACxC,IAAI,eAAe,GAAG,WAAW,CAAC,EAAE,aAAa;oBACjD,gBAAgB,CAAC,UAAU,EAAE,SAAS;oBACtC,gBAAgB,CAAC,OAAO,EAAE,aAAa,IAAI,IAAI,KAAK;oBAEpD,IAAI,aAAa,MAAM,EAAE;wBACvB,gBAAgB,CAAC,SAAS,EAAE,aAAa,MAAM,EAAE;oBACnD;oBAEA,IAAI,aAAa,SAAS,EAAE;wBAC1B,gBAAgB,CAAC,WAAW,EAAE,aAAa,SAAS,EAAE;oBACxD;oBAEA,qDAAqD;oBACrD,SAAS,MAAM,GAAG;oBAElB,QAAQ,GAAG,CAAC,iCAAiC;wBAC3C;wBACA;wBACA;oBACF;oBAEA,0EAA0E;oBAC1E,wEAAwE;oBACxE,QAAQ,GAAG,CAAC,mBAAmB;oBAE/B,qDAAqD;oBACrD,WAAW;wBACT,MAAM,aACJ,SAAS,aAAa,EAAE,QAAQ;wBAClC,MAAM,cAAc,UAAU;wBAC9B,QAAQ,GAAG,CAAC,iCAAiC;4BAC3C;4BACA;4BACA,SAAS,cAAc,sBAAsB,eAAe;wBAC9D;oBACF,GAAG;oBAEH,WAAW;wBACT,QAAQ,GAAG,CAAC,0BAA0B;wBAEtC,sDAAsD;wBACtD,gEAAgE;wBAChE,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACzB,GAAG,MAAM,sCAAsC;gBACjD,OAAO;oBACL,QAAQ,GAAG,CAAC,wBAAwB,SAAS,OAAO;oBACpD,UAAU;wBACR,eACE,SAAS,OAAO,IAAI;oBACxB;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,uBAAuB;gBACnC,UAAU;oBAAE,eAAe;gBAAyC;YACtE;YACA;QACF;QAEA,uCAAuC;QACvC,MAAM,YAAY,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC;QAEpD,IAAI,CAAC,aAAa,KAAK,GAAG,KAAK,UAAU,SAAS,EAAE;YAClD,UAAU;gBACR,eAAe;YACjB;YACA;QACF;QAEA,MAAM,UAAU;YACd,MAAM;YACN,OAAO,OAAO,aAAa;YAC3B,GAAG;gBAAE,MAAM,UAAU,IAAI;YAAC,CAAC;QAC7B;QAEA,MAAM,WAAW,MAAM,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD,EAAE;QAExC,IAAI,UAAU,SAAS;YACrB,MAAM,aAAa,gBACf,qBACA,CAAC,gBAAgB,EAAE,WAAW,CAAC,UAAU,EAAE,UAAU,GAAG,IAAI;YAEhE,OAAO,IAAI,CAAC;QACd,OAAO;YACL,UAAU;gBACR,eACE,UAAU,WAAW;YACzB;QACF;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI,kBAAkB;QAEtB,cAAc;QACd;QAEA,8CAA8C;QAC9C,IAAI,mBAAmB;YACrB,IAAI,CAAC,mBAAmB;gBACtB,UAAU;oBACR,eAAe;gBACjB;gBACA,cAAc;gBACd;YACF;YAEA,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,wHAAA,CAAA,OAAI,AAAD,EAAE,sCAAsC;oBAChE,YAAY;gBACd;gBAEA,IAAI,SAAS,OAAO,EAAE;oBACpB,iBACE;gBAEJ,OAAO;oBACL,UAAU;wBACR,eAAe,SAAS,OAAO,IAAI;oBACrC;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,UAAU;oBACR,eAAe;gBACjB;YACF,SAAU;gBACR,cAAc;YAChB;YACA;QACF;QAEA,MAAM,eAAe,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC,cAAc,CAAC;QACtE,MAAM,UAAU,aAAa,IAAI,IAAI,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD;QAE1C,IAAI;YACF,MAAM,UAAU;gBAAE,MAAM;gBAAU,MAAM;YAAQ;YAChD,MAAM,WAAW,MAAM,CAAA,GAAA,oHAAA,CAAA,yBAAsB,AAAD,EAAE;YAE9C,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,OAAO,IAAI,CAAC,gBAAgB,oBAAoB;gBAChD;YACF;YAEA,IAAI,CAAC,SAAS,OAAO,EAAE;gBACrB,UAAU;oBACR,eAAe,SAAS,OAAO,IAAI;gBACrC;gBACA;YACF;YAEA,0CAA0C;YAC1C,MAAM,mBAAmB;YACzB,MAAM,YAAY,KAAK,GAAG,KAAK,mBAAmB,KAAK;YACvD,eAAe,OAAO,CACpB,UACA,KAAK,SAAS,CAAC;gBAAE,MAAM;gBAAS;YAAU;YAG5C,iBACE;QAEJ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;YACd,UAAU;gBAAE,eAAe;YAA0C;QACvE,SAAU;YACR,cAAc;QAChB;IACF;IAEA,wBAAwB;IACxB,MAAM,uBAAuB;QAC3B,2BAA2B;IAC7B;IAEA,MAAM,0BAA0B,OAAO;QACrC,IAAI,CAAC,mBAAmB;YACtB,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,0BAA0B;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,wHAAA,CAAA,oBAAiB,AAAD,EAAE,aAAa;YAEtD,IAAI,SAAS,OAAO,EAAE;gBACpB,+BAA+B;gBAC/B,2BAA2B;gBAC3B,+DAA+D;gBAC/D,kBAAkB,SAAS,IAAI,CAAC,gBAAgB;gBAChD,2BAA2B;YAC3B,6EAA6E;YAC7E,6EAA6E;YAC/E,OAAO;gBACL,uJAAA,CAAA,UAAK,CAAC,KAAK,CACT,SAAS,OAAO,IAAI;YAExB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,0BAA0B;QAC5B;IACF;IAEA,MAAM,4BAA4B;QAChC,2BAA2B;QAC3B,kBAAkB;QAElB,qFAAqF;QACrF,IAAI,SAAS;YACX,IAAI,cAAc;YAClB,IAAI;gBACF,IACE,YAAY,UAAU,CAAC,cACvB,YAAY,UAAU,CAAC,aACvB;oBACA,MAAM,MAAM,IAAI,IAAI;oBACpB,cAAc,IAAI,QAAQ,GAAG,IAAI,MAAM,GAAG,IAAI,IAAI;gBACpD;YACF,EAAE,OAAO,GAAG;gBACV,QAAQ,IAAI,CAAC,iCAAiC;YAChD;YACA,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB,OAAO;YACL,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,MAAM,eAAe,CAAC,GAAG;QACvB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,IAAI,gBAAgB,IAAI,CAAC,QAAQ;YAC/B,IAAI,QAAQ,UAAU,OAAO,CAAC,MAAM,GAAG,GAAG;gBACxC,UAAU,OAAO,CAAC,QAAQ,EAAE,CAAC,KAAK;YACpC;QACF,OAAO;YACL,EAAE,MAAM,CAAC,KAAK,GAAG;QACnB;IACF;IAEA,MAAM,gBAAgB,CAAC,GAAG;QACxB,IAAI,EAAE,GAAG,KAAK,eAAe,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,QAAQ,GAAG;YACzD,UAAU,OAAO,CAAC,QAAQ,EAAE,CAAC,KAAK;QACpC;IACF;IAEA,MAAM,YAAY;QAChB,SAAS;QACT,OAAO;QACP,aACE;QACF,gBAAgB;QAChB,cAAc;QACd,UAAU;QACV,gBACE;QACF,eAAe;QACf,qBACE;IACJ;IAEA,kEAAkE;IAClE,IAAI,4BAA4B,mBAAmB;QACjD,qBACE,8OAAC,4HAAA,CAAA,UAAU;;8BACT,8OAAC,8HAAA,CAAA,UAAQ;oBAAC,OAAO;;;;;;8BACjB,8OAAC;8DAAc;8BACb,cAAA,8OAAC;kEAAc;;0CACb,8OAAC;0EAAc;;kDACb,8OAAC,uIAAA,CAAA,UAAQ;;;;;kDACT,8OAAC;kFAAc;kDACb,cAAA,8OAAC;;sDAAG;;;;;;;;;;;kDAEN,8OAAC;kFAAc;kDACb,cAAA,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;8DACb,cAAA,8OAAC;wDAA4C,MAAK;kGAAnC;kEACb,cAAA,8OAAC;sGAAe;sEAAkB;;;;;;;;;;;;;;;;8DAGtC,8OAAC;8FAAa;8DAAO;;;;;;8DACrB,8OAAC;8FAAY;8DAAa;;;;;;;;;;;;;;;;;;;;;;;0CAMhC,8OAAC;0EAAc;0CACb,cAAA,8OAAC,uIAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAuCxB;IAEA,mEAAmE;IACnE,IAAI,mBAAmB,MAAM;QAC3B,qBACE,8OAAC,4HAAA,CAAA,UAAU;;8BACT,8OAAC,8HAAA,CAAA,UAAQ;oBAAC,OAAO;;;;;;8BACjB,8OAAC;8DAAc;8BACb,cAAA,8OAAC;kEAAc;;0CACb,8OAAC;0EAAc;;kDACb,8OAAC,uIAAA,CAAA,UAAQ;;;;;kDACT,8OAAC;kFAAc;kDACb,cAAA,8OAAC;;sDAAG;;;;;;;;;;;kDAEN,8OAAC;kFAAc;kDACb,cAAA,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;8DACb,cAAA,8OAAC;wDAA4C,MAAK;kGAAnC;kEACb,cAAA,8OAAC;sGAAe;sEAAkB;;;;;;;;;;;;;;;;8DAGtC,8OAAC;8FAAa;8DAAO;;;;;;8DACrB,8OAAC;8FAAY;8DACV,gBACG,wDACA;;;;;;;;;;;;;;;;;;;;;;;0CAKZ,8OAAC;0EAAc;0CACb,cAAA,8OAAC,uIAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAuCxB;IAEA,IAAI,mBAAmB,OAAO;QAC5B,OAAO;IACT;IAEA,qBACE,8OAAC,4HAAA,CAAA,UAAU;;0BACT,8OAAC,8HAAA,CAAA,UAAQ;gBAAC,OAAO;;;;;;0BACjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,uIAAA,CAAA,UAAQ;;;;;8CACT,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;kDAAG;;;;;;;;;;;8CAEN,8OAAC;oCAAI,IAAG;;sDACN,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACZ,8BACC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;2DAI9B,kCACF,8OAAC;oDAAK,WAAU;8DAAgB;;;;;2DAI9B,aAAa,sCACf;8DACG,SAAS,WAAW,CAAC,qBACpB,8OAAC;wDAAK,WAAU;kEACb,SAAS,WAAW,CAAC,OACpB,8GAEA;;gEAAE;gEAC2C;8EAC3C,8OAAC;8EAAQ;;;;;;gEAAqB;;;;;;;6EAKpC,8OAAC;wDAAK,WAAU;;4DAAgB;0EAEd,8OAAC;0EAAQ;;;;;;4DAAqB;;;;;;;kFAOpD,8OAAC;8DAAK;;;;;;;;;;;;;;;;sDAIZ,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;0DAAM;;;;;;;;;;;;;;;;;8CAGX,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,+IAAA,CAAA,SAAM;4CACL,eAAe;4CACf,kBAAkB,4HAAA,CAAA,iBAAc;4CAChC,UAAU;sDAET,CAAC,EAAE,YAAY,EAAE,iBAChB,8OAAC,+IAAA,CAAA,OAAI;;sEACH,8OAAC,+IAAA,CAAA,QAAK;4DAAC,MAAK;sEACT,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE;gEACrB,MAAM,cAAc,CAAC;oEACnB,EAAE,cAAc;oEAChB,MAAM,YAAY,EAAE,aAAa,CAAC,OAAO,CAAC;oEAC1C,MAAM,UAAU,UAAU,OAAO,CAC/B,iBACA;oEAEF,MAAM,QAAQ,QAAQ,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG;oEACzC,IAAI,UAAU,IAAI,MAAM,GAAG,IAAI,CAAC;oEAChC,MAAM,OAAO,CAAC,CAAC,MAAM;wEACnB,OAAO,CAAC,EAAE,GAAG;wEACb,IAAI,UAAU,OAAO,CAAC,EAAE,EAAE;4EACxB,UAAU,OAAO,CAAC,EAAE,CAAC,KAAK,GAAG;wEAC/B;oEACF;oEACA,KAAK,aAAa,CAAC,MAAM,IAAI,EAAE,QAAQ,IAAI,CAAC;oEAC5C,IAAI,MAAM,MAAM,GAAG,UAAU,OAAO,CAAC,MAAM,EAAE;wEAC3C,UAAU,OAAO,CAAC,MAAM,MAAM,CAAC,CAAC,KAAK;oEACvC;gEACF;gEAEA,MAAM,eAAe;oEACnB,UAAU,OAAO,CAAC,OAAO,CAAC,CAAC;wEACzB,IAAI,OAAO,MAAM,KAAK,GAAG;oEAC3B;oEAEA,KAAK,aAAa,CAAC,iBAAiB;oEACpC,KAAK,aAAa,CAAC,iBAAiB;oEAEpC,kBAAkB;wEAChB,eAAe;4EACb,KAAK,aAAa,CAChB,iBACA;wEAEJ;oEACF;oEAEA,UAAU,OAAO,CAAC,EAAE,EAAE;gEACxB;gEAEA,qBACE;;sFACE,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFACC,MAAK;gFACL,mBAAgB;;kGAEhB,8OAAC;wFAAI,WAAU;kGACZ,MAAM,IAAI,CAAC;4FAAE,QAAQ;wFAAE,GAAG,GAAG,CAC5B,CAAC,GAAG;4FACF,MAAM,YAAY,MAAM,KAAK,GACzB,MAAM,KAAK,CAAC,MAAM,IAAI,KACtB;4FACJ,qBACE,8OAAC;gGAEC,MAAK;gGACL,WAAU;gGACV,IAAI,CAAC,UAAU,EAAE,QAAQ,GAAG;gGAC5B,WAAU;gGACV,cAAY,GACV;oGACE;oGACA;oGACA;oGACA;oGACA;oGACA;iGACD,CAAC,MAAM,CACT,UAAU,CAAC;gGACZ,OAAO;gGACP,UAAU,CAAC;oGACT,MAAM,MAAM,EAAE,MAAM,CAAC,KAAK;oGAC1B,IAAI,UAAU,MAAM,KAAK,GACrB,MAAM,KAAK,CAAC,KAAK,CAAC,MAClB,IAAI,MAAM,GAAG,IAAI,CAAC;oGACtB,OAAO,CAAC,MAAM,GAAG;oGACjB,KAAK,aAAa,CAChB,MAAM,IAAI,EACV,QAAQ,IAAI,CAAC;oGAEf,aAAa,GAAG;gGAClB;gGACA,WAAW,CAAC,IACV,cAAc,GAAG;gGAEnB,SAAS;gGACT,KAAK,CAAC,KACH,UAAU,OAAO,CAAC,MAAM,GAAG;gGAE9B,WACE,KAAK,OAAO,IAAI,KAAK,KAAK,GACtB,gBACA;+FAtCD;;;;;wFA0CX;;;;;;oFAGH,KAAK,OAAO,IAAI,KAAK,KAAK,iBACzB,8OAAC,sIAAA,CAAA,UAAU;wFAAC,SAAS,KAAK,KAAK;;;;;+FAC7B;;;;;;;;;;;;sFAIR,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFACC,MAAK;gFACL,WAAU;gFACV,SAAS;gFACT,UAAU;;oFACX;kGAEC,8OAAC,oIAAA,CAAA,iBAAc;wFAAC,YAAY;;;;;;kGAC5B,8OAAC;kGACE,WAAW,IAAI,GAAG,SAAS,CAAC,CAAC,GAAG;;;;;;;;;;;;;;;;;wEAMtC,mCACC,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;8FAAK;;;;;;8FACN,8OAAC;oFACC,MAAK;oFACL,WAAU;oFACV,SAAS;8FAET,cAAA,8OAAC;wFAAK,WAAU;kGAAc;;;;;;;;;;;;;;;;;;;4DAQ1C;;;;;;wDAED,+BACC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;0EAAG;;;;;;;;;;;sEAGR,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,wIAAA,CAAA,UAAY;gEACX,MAAK;gEACL,OAAO,eAAe,YAAY;gEAClC,KAAK;gEACL,UAAU;;;;;;;;;;;sEAGd,8OAAC,4JAAA,CAAA,UAAI;4DACH,MACE,oBACI,qBACA,gBACA,yBACA;4DAEN,WAAU;sEAEV,cAAA,8OAAC,wIAAA,CAAA,UAAY;gEAAC,OAAM;gEAAS,OAAO;;;;;;;;;;;sEAEtC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,MAAK;gEACL,QAAO;gEACP,KAAI;gEACJ,WAAU;;kFAEV,8OAAC;wEAAK,WAAU;kFAAY;;;;;;kFAG5B,8OAAC,oIAAA,CAAA,yBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASvC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,uIAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;;;;;0BAMlB,8OAAC,mKAAA,CAAA,UAAmB;gBAClB,MAAM;gBACN,aAAa,IAAM,2BAA2B;gBAC9C,UAAU;gBACV,cAAc;;;;;;0BAGhB,8OAAC,mKAAA,CAAA,UAAmB;gBAClB,MAAM;gBACN,aAAa;gBACb,aAAa;;;;;;;;;;;;AAIrB", "debugId": null}}]}